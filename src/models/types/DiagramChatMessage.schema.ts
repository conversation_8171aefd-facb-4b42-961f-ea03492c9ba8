import { z } from 'zod';

import { DiagramChatChannelSchema } from './DiagramChatChannel.schema';

export const DiagramChatMessageSchema = z.object({
    id: z.string(),
    project: z.string(),
    channel: DiagramChatChannelSchema.shape.id,
    parent: z.string().nullable(),
    content: z
        .object({
            blockType: z.literal('message'),
            content: z.string(),
            files: z
                .object({
                    file: z.string(),
                })
                .array()
                .default([]),
        })
        .array(),
    reactions: z
        .object({
            reaction: z.string(),
            createdBy: z.string(),
            createdAt: z.string(),
        })
        .array(),
    as: z.string().nullable(),
    createdBy: z.string(),
    createdByCompany: z.string().nullable(),
    createdAt: z.date(),
    team: z.string(),
});
