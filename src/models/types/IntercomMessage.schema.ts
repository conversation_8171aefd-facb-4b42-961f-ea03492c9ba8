import { z } from 'zod';

export const IntercomMessageSchema = z.object({
    id: z.string(),
    channel: z.string(),
    content: z
        .object({
            blockType: z.literal('message'),
            content: z.string(),
            files: z
                .object({
                    file: z.string(),
                })
                .array()
                .default([]),
        })
        .array(),
    reactions: z
        .object({
            reaction: z.string(),
            createdBy: z.string(),
            createdAt: z.string(),
        })
        .array(),
    createdBy: z.string(),
    createdByCompany: z.string().nullable(),
    createdAt: z.date(),
    team: z.string(),
});
