import React, { FC } from 'react';

import { createPolymorphicComponent, Loader, Menu, MenuItemProps, MenuProps } from '@mantine/core';
import { BsPencil } from 'react-icons/bs';
import { IoSettingsOutline, IoTrashOutline } from 'react-icons/io5';

import { SimpleButton } from 'elements/buttons';

const SettingsDropdown: FC<{ loading?: boolean; children: React.ReactNode } & MenuProps> & {
    Item: typeof Item;
    Edit: typeof EditItem;
    Delete: typeof DeleteItem;
} = ({ loading, children, disabled, ...props }) => {
    return (
        <Menu trigger="click-hover" position="bottom-end" offset={3} keepMounted {...props}>
            <Menu.Target>
                <SimpleButton disabled={disabled}>{loading ? <Loader size={8} /> : <IoSettingsOutline />}</SimpleButton>
            </Menu.Target>

            <Menu.Dropdown>{children}</Menu.Dropdown>
        </Menu>
    );
};

const Item = createPolymorphicComponent<'button', MenuItemProps>((props: MenuItemProps) => {
    return <Menu.Item fz="xs" {...props} />;
});

const EditItem = createPolymorphicComponent<'button', MenuItemProps>(
    ({ children = 'Edit', ...props }: MenuItemProps) => {
        return (
            <SettingsDropdown.Item leftSection={<BsPencil />} {...props}>
                {children}
            </SettingsDropdown.Item>
        );
    },
);

const DeleteItem = createPolymorphicComponent<'button', MenuItemProps>(
    ({ children = 'Delete', ...props }: MenuItemProps) => {
        return (
            <SettingsDropdown.Item color="red" leftSection={<IoTrashOutline />} {...props}>
                {children}
            </SettingsDropdown.Item>
        );
    },
);

SettingsDropdown.Item = Item;
SettingsDropdown.Edit = EditItem;
SettingsDropdown.Delete = DeleteItem;

export { SettingsDropdown };
