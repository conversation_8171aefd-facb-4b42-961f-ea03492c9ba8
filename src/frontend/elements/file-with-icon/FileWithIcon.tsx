import React, { FC, useState } from 'react';

import { <PERSON>chor, Box, Button, Flex, Group, MenuDivider, MenuLabel, Modal, Stack, Text, TextInput } from '@mantine/core';

import { ComponentFileTypes, File } from 'models';

import { IoCheckmarkSharp } from 'react-icons/io5';

import { getFileIcon } from 'components/file-icon/FileIcon';
import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';

import cx from './FileWithIcon.module.scss';

const FileWithIcon: FC<{
    file: File;
    type?: string;
    onRename?: (name: string) => Promise<void>;
    onChangeType?: (type: string) => Promise<void>;
    onDelete?: () => Promise<void>;
}> = ({ file, type, onRename, onDelete, onChangeType }) => {
    const [isRenaming, setIsRenaming] = useState(false);
    const [newName, setNewName] = useState(file.name);

    const [updating, setUpdating] = useState(false);

    const handleRename = async () => {
        setUpdating(true);

        setIsRenaming(false);
        await onRename?.(newName);

        setUpdating(false);
    };

    const handleDelete = async () => {
        setUpdating(true);

        await onDelete?.();

        setUpdating(false);
    };

    const handleChangeType = async (type: string) => {
        setUpdating(true);

        await onChangeType?.(type);

        setUpdating(false);
    };

    const Icon = getFileIcon(file.mimeType);

    const fileType = ComponentFileTypes.options.find(({ value }) => value === type)?.label;

    return (
        <>
            <Flex className={cx.file} align="flex-start" gap={8}>
                <Icon className={cx.icon} />

                <Anchor fz="sm" href={file.url} target="_blank" download td="none">
                    {ComponentFileTypes.options.find(({ value }) => value === type)?.label}

                    {fileType !== file.name && (
                        <Text inherit span c="dimmed" style={{ display: 'block' }}>
                            {file.name}
                        </Text>
                    )}
                </Anchor>

                {(onRename || onDelete || onChangeType) && (
                    <Box ml="auto">
                        <SettingsDropdown loading={updating}>
                            {onRename && (
                                <SettingsDropdown.Edit onClick={() => setIsRenaming(true)} disabled={isRenaming}>
                                    Rename
                                </SettingsDropdown.Edit>
                            )}
                            {onDelete && <SettingsDropdown.Delete onClick={handleDelete} />}

                            {onChangeType && (
                                <>
                                    <MenuDivider />
                                    <MenuLabel>Change document type</MenuLabel>
                                    {ComponentFileTypes.options.map(({ label, value }) => (
                                        <SettingsDropdown.Item
                                            key={value}
                                            onClick={() => handleChangeType(value)}
                                            disabled={value === type}
                                            rightSection={
                                                value === type && (
                                                    <Text c="green">
                                                        <IoCheckmarkSharp />
                                                    </Text>
                                                )
                                            }
                                        >
                                            {label}
                                        </SettingsDropdown.Item>
                                    ))}
                                </>
                            )}
                        </SettingsDropdown>
                    </Box>
                )}
            </Flex>
            <Modal opened={isRenaming} onClose={() => setIsRenaming(false)} title="Rename file" keepMounted={false}>
                <Stack gap="xs">
                    <TextInput
                        value={newName}
                        onChange={({ target: { value } }) => {
                            setNewName(value);
                        }}
                    />
                    <Group gap={4}>
                        <Button size="xs" onClick={handleRename}>
                            Save
                        </Button>
                        <Button size="xs" variant="subtle" onClick={() => setIsRenaming(false)}>
                            Cancel
                        </Button>
                    </Group>
                </Stack>
            </Modal>
        </>
    );
};

export { FileWithIcon };
