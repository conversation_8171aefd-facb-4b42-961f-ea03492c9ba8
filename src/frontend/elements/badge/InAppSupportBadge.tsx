import { MouseEvent<PERSON>and<PERSON> } from 'react';
import { Component, CompanyProfile } from 'models';

import { Button, ButtonProps } from '@mantine/core';
import { IoChatbubblesOutline } from 'react-icons/io5';

import { IntercomService } from 'services/IntercomService';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useRouter } from 'next/router';
import { RouterService } from 'services/RouterService';

const InAppSupportBadge = ({
    label,
    component,
    company,
    size = 'compact-xs',
    variant = 'gradient',
    isExternalLink,
    ...props
}: {
    label?: string;
    component?: Component;
    company: CompanyProfile;
    isExternalLink?: boolean;
} & ButtonProps) => {
    const { asPath } = useRouter();
    const onClick: MouseEventHandler = (event) => {
        event.stopPropagation();

        let url = '';

        IntercomService.open();

        if (company) {
            const companyUrl = CompanyProfileHelpers.urls.view(company.slug);

            if (!asPath.includes(companyUrl)) {
                url = companyUrl;
            }
        }

        if (component) {
            const componentUrl = ComponentHelpers.urls.view(component.id);

            if (!asPath.includes(componentUrl)) {
                url = componentUrl;
            }
        }

        if (url) {
            if (isExternalLink) {
                url += '?action=intercom';

                window.open(url, '_blank');
            } else {
                RouterService.push(url);
            }
        }
    };

    const longName = company.name.length > 10;

    return (
        <Button size={size} leftSection={<IoChatbubblesOutline />} variant={variant} onClick={onClick} {...props}>
            {label || `Chat with ${longName ? 'Company' : company.name}`}
        </Button>
    );
};

export { InAppSupportBadge };
