import { Badge, ThemeIcon, Tooltip } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

const AIEnabledBadge = ({ iconOnly, disableTooltip }: { iconOnly?: boolean; disableTooltip?: boolean }) => {
    let badge = (
        <Badge radius={99} variant="light" color="brand" size="sm" leftSection={<BsRobot />}>
            AI enabled
        </Badge>
    );

    if (iconOnly) {
        badge = (
            <ThemeIcon color="brand" size="xs" radius="xl" variant="light">
                <BsRobot size={10} />
            </ThemeIcon>
        );
    }

    return (
        <Tooltip label="Chat with product documentation" disabled={disableTooltip}>
            {badge}
        </Tooltip>
    );
};

export { AIEnabledBadge };
