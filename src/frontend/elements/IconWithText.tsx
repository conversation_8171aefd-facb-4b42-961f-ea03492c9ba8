import { FC } from 'react';

import { Box, BoxProps, Flex, FlexProps } from '@mantine/core';

const IconWithText: FC<{
    icon: React.ReactNode;
    iconColor?: BoxProps['c'];
    text: React.ReactNode;
    textProps?: FlexProps;
}> = ({ icon, iconColor = 'primary', text, textProps }) => {
    return (
        <Flex gap={6} style={{ wordBreak: 'break-all' }} align="center" {...textProps}>
            <Box mt={4} c={iconColor}>
                {icon}
            </Box>
            {text}
        </Flex>
    );
};

export { IconWithText };
