import { proxy, subscribe } from 'valtio';

import { MeasurementSystem } from 'models';
import { UserService } from 'services/UserService';

import { state as currentUserState } from 'state/current-user';

const state = proxy<{
    measurementSystem: MeasurementSystem | null;
}>({
    measurementSystem: null,
});

subscribe(state, () => {
    const userId = currentUserState.user?.id;
    const userMeasurementSystem = currentUserState.user?.measurementSystem;

    if (!userId) {
        return;
    }

    if (!state.measurementSystem) {
        return;
    }

    if (state.measurementSystem === userMeasurementSystem) {
        return;
    }

    UserService.update(userId, {
        measurementSystem: state.measurementSystem,
    });
});

export { state };
