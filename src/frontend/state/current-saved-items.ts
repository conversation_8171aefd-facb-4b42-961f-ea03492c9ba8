import { proxy } from 'valtio';

import { SavedItem, SavedItemType } from 'models';
import { derive } from 'derive-valtio';
import { getId } from 'helpers/getId';

type SavedItemsState = { savedItems: SavedItem[]; initialized: boolean };

const savedItemsState = proxy<SavedItemsState>({
    savedItems: [],
    initialized: false,
});

const savedItemsDerivedState = derive({
    lookup: (get) => {
        const savedItems = get(savedItemsState).savedItems;

        const lookup: {
            [itemId: string]: string;
        } = {};

        savedItems.forEach((savedItem) => {
            const id = getId(savedItem.item.value);

            if (id) {
                lookup[id] = savedItem.id;
            }
        });

        return lookup;
    },
    profiles: (get) => {
        const savedItems = get(savedItemsState).savedItems;

        return savedItems.filter((savedItem) => savedItem.item.relationTo === SavedItemType.PROFILE);
    },
    designs: (get) => {
        const savedItems = get(savedItemsState).savedItems;

        return savedItems.filter((savedItem) => savedItem.item.relationTo === SavedItemType.PROJECT);
    },
    components: (get) => {
        const savedItems = get(savedItemsState).savedItems;

        return savedItems.filter((savedItem) => savedItem.item.relationTo === SavedItemType.COMPONENT);
    },
});

export { savedItemsState, savedItemsDerivedState };
