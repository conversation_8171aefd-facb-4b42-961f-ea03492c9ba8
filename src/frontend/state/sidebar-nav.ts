import { proxy } from 'valtio';

type SidebarNavState = {
    isFloating: boolean; // true = show as overlay, eg. on a diagram
    isOpen: boolean;
    isMobileOpen: boolean;
    isDisabled: boolean;
};

const sidebarNavStateDefaults: SidebarNavState = {
    isFloating: false,
    isOpen: true,
    isMobileOpen: false,
    isDisabled: false,
};

const sidebarNavState = proxy<SidebarNavState>(sidebarNavStateDefaults);

export { sidebarNavState, sidebarNavStateDefaults };
