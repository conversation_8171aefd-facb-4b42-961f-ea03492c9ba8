import { proxy } from 'valtio';

import {
    FeatureKey,
    PermissionDiagram,
    PermissionDiagramElements,
    PermissionDiagramFiles,
    PermissionDiagramGeneral,
    PermissionDiagramGroups,
    PermissionDiagramImages,
    PermissionDiagramSimulations,
    PermissionDigramComments,
    PermissionProject,
    Project,
    subscriptionCanUseFeature,
    UserFeatureFlags,
} from 'models';

import { state as userState } from 'state/current-user';
import { UserHelpers } from 'helpers/UserHelpers';

type Permissions = Partial<Record<PermissionDiagram, boolean>>;

export const permissionsState = proxy<{ permissions: Permissions }>({
    permissions: {},
});

export const setPermissions = (project: Project | null) => {
    if (!project) {
        clearPermissions();
    } else {
        setProjectPermissions(project);
    }
};

export function clearPermissions(): void {
    permissionsState.permissions = {};
}

export function setSimulationPermissions(): void {
    permissionsState.permissions = {
        [PermissionDiagramElements.VIEW]: true,
        [PermissionDiagramElements.EDIT]: true, // TODO: disable drag and connection
        [PermissionDiagramGroups.VIEW]: true,
        [PermissionDiagramSimulations.VIEW]: true,
    };
}

export function setFaultSimulationPermissions(): void {
    permissionsState.permissions = {
        [PermissionDiagramElements.VIEW]: true,
        [PermissionDiagramElements.EDIT]: true, // TODO: disable drag and connection
        [PermissionDiagramGroups.VIEW]: true,
        [PermissionDiagramSimulations.VIEW]: true,
    };
}

function setProjectPermissions(project: Project) {
    const { permissions: projectPermissions, permissionsAs, teamSubscription } = project;

    // reset state before setting individual permissions
    permissionsState.permissions = {};

    if (
        projectPermissions.includes(PermissionProject.ALL) ||
        projectPermissions.includes(PermissionProject.ADMIN) ||
        projectPermissions.includes(PermissionProject.VIEW)
    ) {
        permissionsState.permissions[PermissionDiagramElements.VIEW] = true;
        permissionsState.permissions[PermissionDiagramImages.VIEW] = true;
        permissionsState.permissions[PermissionDiagramGroups.VIEW] = true;
    }

    if (
        projectPermissions.includes(PermissionProject.ALL) ||
        projectPermissions.includes(PermissionProject.ADMIN) ||
        projectPermissions.includes(PermissionProject.EDIT)
    ) {
        permissionsState.permissions[PermissionDiagramGeneral.EDIT] = true;
        permissionsState.permissions[PermissionDiagramGeneral.SHARE] = true;
        permissionsState.permissions[PermissionDiagramGeneral.DOWNLOAD] = true;
        permissionsState.permissions[PermissionDiagramGeneral.BOM] = true;

        permissionsState.permissions[PermissionDiagramElements.CREATE] = true;
        permissionsState.permissions[PermissionDiagramElements.EDIT] = true;

        permissionsState.permissions[PermissionDiagramImages.CREATE] = true;
        permissionsState.permissions[PermissionDiagramImages.EDIT] = true;

        permissionsState.permissions[PermissionDiagramGroups.CREATE] = true;
        permissionsState.permissions[PermissionDiagramGroups.EDIT] = true;

        permissionsState.permissions[PermissionDigramComments.VIEW] = true;
        permissionsState.permissions[PermissionDigramComments.EDIT] = true;
        permissionsState.permissions[PermissionDigramComments.CREATE] = true;

        permissionsState.permissions[PermissionDiagramFiles.CREATE] = true;
        permissionsState.permissions[PermissionDiagramFiles.EDIT] = true;
        permissionsState.permissions[PermissionDiagramFiles.VIEW] = true;

        const userHasSimulation = UserHelpers.checkUserFlag(userState.user, UserFeatureFlags.SIMULATION);
        const hasProjectSimulatePermission =
            permissionsAs === 'user' && subscriptionCanUseFeature(teamSubscription, FeatureKey.RUN_SIMULATIONS);

        if (userHasSimulation || hasProjectSimulatePermission) {
            permissionsState.permissions[PermissionDiagramSimulations.CREATE] = true;
            permissionsState.permissions[PermissionDiagramSimulations.EDIT] = true;
            permissionsState.permissions[PermissionDiagramSimulations.VIEW] = true;
        }
    }

    if (projectPermissions.includes(PermissionProject.COMPANY)) {
        permissionsState.permissions[PermissionDiagramGeneral.COMPANY] = true;
        permissionsState.permissions[PermissionDiagramGeneral.BOM] = true;

        permissionsState.permissions[PermissionDigramComments.VIEW] = true;
        permissionsState.permissions[PermissionDigramComments.CREATE] = true;
        permissionsState.permissions[PermissionDigramComments.EDIT] = true;
    }
}
