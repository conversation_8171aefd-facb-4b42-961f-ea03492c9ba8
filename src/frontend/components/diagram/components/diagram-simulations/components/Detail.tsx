import React, { FC, useEffect, useState } from 'react';

import { Button, Table, Space, Group, Tooltip, Box, Alert, Loader } from '@mantine/core';
import { Dropzone } from '@mantine/dropzone';

import { TbArrowsDiagonal, TbArrowsDiagonalMinimize2, TbSettings, TbUpload } from 'react-icons/tb';

import { DiagramSidebar } from 'components/diagram';
import { UploadModal } from './UploadModal';
import { DiagramInputTable } from 'components/diagram/components/diagram-input-table/DiagramInputTable';

import { ModalService } from 'services/ModalService';
import { SimulationHelpers } from 'components/diagram/helpers/SimulationHelpers';
import {
    SimulationProfileService,
    SimulationProfile,
    SimulationProfileData,
} from 'components/diagram/services/SimulationProfileService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { SingleReadService } from 'services/SingleReadService';

import { SimulationProfileHelpers } from 'components/diagram/helpers/SimulationProfileHelpers';

import { download } from 'components/diagram/helpers/utils/download';

import { useDebouncedCallback } from '@mantine/hooks';
import { useDiagram } from 'components/diagram/hooks';
import { useSimulationProfile } from 'components/diagram/hooks/use-simulation-profile';
import { useSidebarFullscreen } from 'components/diagram/hooks/use-sidebar-fullscreen';
import { useSidebarWidth } from 'components/diagram/hooks/use-sidebar-width';
import { useSimulationProfiles } from 'components/diagram/hooks/use-simulation-profiles';

import cx from '../SimulationProfiles.module.css';

const Detail: FC<{
    simulationProfile: SimulationProfile;
}> = ({ simulationProfile }) => {
    // This is needed to make mutate is working.
    useSimulationProfiles();

    useSidebarWidth(window.innerWidth / 2);
    const [fullscreen, toggleFullscreen] = useSidebarFullscreen();

    const { componentInstances } = useDiagram();
    const [data, setData] = useState(SimulationProfileHelpers.csvToJson(simulationProfile.data));

    const save = useDebouncedCallback(() => {
        SimulationProfileService.update(simulationProfile.id, {
            data: SimulationProfileHelpers.jsonToCsv(data),
        }).then();
    }, 500);

    const setDataAndSave = (next: SimulationProfileData) => {
        setData(next);
        save();
    };

    const components = Object.values(componentInstances).filter((componentInstance) => {
        if (simulationProfile.type === 'combined') {
            return SimulationHelpers.componentHasPowerField(componentInstance);
        }

        if (simulationProfile.type === 'load') {
            return SimulationHelpers.componentIsLoad(componentInstance);
        }

        if (simulationProfile.type === 'generation') {
            return SimulationHelpers.componentIsGenerator(componentInstance);
        }

        return [];
    });

    const addRow = () => {
        const next = JSON.parse(JSON.stringify(data));

        next.push({
            '"time"': `${data.length + 1}`,
            ...Object.fromEntries(components.map((load) => [`"${load.designator}.power"`, ''])),
        });

        setDataAndSave(next);
    };

    const createSimulation = () => {
        SingleReadService.write('simulationProfile', simulationProfile.id);
        SidebarService.openCreateSimulationSidebar();
    };

    const downloadCsv = () => {
        download(`${simulationProfile.name}.csv`, 'text/csv', SimulationProfileHelpers.jsonToCsv(data));
    };

    const uploadCsv = (file?: File) => {
        ModalService.open({
            withCloseButton: false,
            children: <UploadModal components={components} data={data} updateData={setDataAndSave} file={file} />,
        });
    };

    const rename = () => {
        ModalService.openContextModal({
            modal: 'name',
            innerProps: {
                title: 'Rename simulation profile',
                label: 'Name',
                placeholder: 'Enter new name',
                value: simulationProfile.name,
                onChange: async (value) => {
                    await SimulationProfileService.update(simulationProfile.id, { name: value });
                },
            },
            withCloseButton: false,
        });
    };

    const duplicate = () => {
        ModalService.openContextModal({
            modal: 'name',
            innerProps: {
                title: 'Duplicate simulation profile',
                label: 'Name',
                placeholder: 'Enter new name',
                value: simulationProfile.name,
                onChange: async (value) => {
                    const { simulationProfile: duplicateSimulationProfile } = await SimulationProfileService.duplicate(
                        simulationProfile,
                        value,
                    );

                    SimulationProfileService.activate(duplicateSimulationProfile.id);
                },
                submitLabel: 'Duplicate',
            },
            withCloseButton: false,
        });
    };

    useEffect(() => {
        if (data.length === 0) {
            addRow();
        }
    }, [data.length]);

    useEffect(() => {
        return () => {
            SimulationProfileService.deactivate();
        };
    }, []);

    return (
        <>
            <DiagramSidebar.SimpleHeader
                handleBackClick={() => {
                    SimulationProfileService.deactivate();
                }}
                rightSection={
                    <Group gap="xs">
                        <DiagramSidebar.ButtonGroup>
                            <Tooltip label="Toggle fullscreen" position="left" withArrow>
                                <DiagramSidebar.Button onClick={toggleFullscreen}>
                                    {fullscreen ? <TbArrowsDiagonalMinimize2 /> : <TbArrowsDiagonal />}
                                </DiagramSidebar.Button>
                            </Tooltip>
                            <DiagramSidebar.Button onClick={rename}>
                                <TbSettings />
                            </DiagramSidebar.Button>
                        </DiagramSidebar.ButtonGroup>
                    </Group>
                }
            >
                {simulationProfile.name}
            </DiagramSidebar.SimpleHeader>
            <DiagramSidebar.Section>
                {components.length === 0 && (
                    <Alert mb="xs" color="blue">
                        You don&apos;t have any components in your diagram that generate or consume power.
                    </Alert>
                )}
                <Dropzone
                    className={cx.dropzone}
                    onDrop={([file]) => {
                        uploadCsv(file);
                    }}
                    onReject={() => {
                        alert('Only CSV files are allowed.');
                    }}
                    accept={['text/csv']}
                    activateOnClick={false}
                >
                    <Dropzone.Accept>
                        <Box className={cx.dropzoneOverlay}>
                            <TbUpload strokeWidth={2} />
                            Drop your CSV file
                        </Box>
                    </Dropzone.Accept>
                    <DiagramInputTable>
                        <Table.Thead>
                            <Table.Tr>
                                <Table.Th>Time</Table.Th>
                                {components.map((load) => (
                                    <Table.Th key={load.id}>{load.designator}.power</Table.Th>
                                ))}
                            </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                            {data.map((row, rowIndex) => (
                                <Table.Tr>
                                    <Table.Td>
                                        <input
                                            value={row['"time"']}
                                            onChange={(event) => {
                                                const next = JSON.parse(JSON.stringify(data));

                                                next[rowIndex]['"time"'] = event.target.value;

                                                setDataAndSave(next);
                                            }}
                                        />
                                    </Table.Td>
                                    {components.map((load) => (
                                        <Table.Td>
                                            <input
                                                value={row[`"${load.designator}.power"`]}
                                                onChange={(event) => {
                                                    const next = JSON.parse(JSON.stringify(data));

                                                    next[rowIndex][`"${load.designator}.power"`] = event.target.value;

                                                    setDataAndSave(next);
                                                }}
                                            />
                                        </Table.Td>
                                    ))}
                                </Table.Tr>
                            ))}
                        </Table.Tbody>
                    </DiagramInputTable>
                </Dropzone>
                <Space h="xs" />
                <Group gap="xs">
                    <Button size="xs" variant="gradient" onClick={addRow}>
                        Add a row
                    </Button>
                    <Button size="xs" variant="gradient" onClick={createSimulation}>
                        Create simulation
                    </Button>
                    <Button.Group>
                        <Button size="xs" variant="default" onClick={downloadCsv}>
                            Download CSV
                        </Button>
                        <Button
                            size="xs"
                            variant="default"
                            onClick={() => {
                                uploadCsv();
                            }}
                        >
                            Upload CSV
                        </Button>
                        <Button size="xs" variant="default" onClick={duplicate}>
                            Duplicate
                        </Button>
                    </Button.Group>
                </Group>
            </DiagramSidebar.Section>
        </>
    );
};

const DetailFetcher: FC<{
    id: string;
}> = ({ id }) => {
    const { simulationProfile } = useSimulationProfile(id);

    return simulationProfile ? (
        <Detail simulationProfile={simulationProfile} />
    ) : (
        <DiagramSidebar.Section>
            <Group justify="center">
                <Loader color="blue" />
            </Group>
        </DiagramSidebar.Section>
    );
};

export { DetailFetcher as Detail };
