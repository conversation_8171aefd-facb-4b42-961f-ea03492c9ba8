import { Table, ScrollArea, TableProps } from '@mantine/core';

import cx from './DiagramInputTable.module.scss';

const DiagramInputTable = (props: TableProps) => {
    return (
        // let's not use type=never for Windows friends
        <ScrollArea className={cx.tableScrollContainer} type="auto" scrollbarSize={10}>
            <Table className={cx.table} withColumnBorders withTableBorder stickyHeader {...props} />
        </ScrollArea>
    );
};

export { DiagramInputTable };
