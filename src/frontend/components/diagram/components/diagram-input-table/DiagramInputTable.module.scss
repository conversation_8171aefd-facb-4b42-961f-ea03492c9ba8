.tableScrollContainer {
    --scrollbar-space: var(--mantine-spacing-sm);

    overflow: visible; // make sure scrollbar is sticky at the bottom
    scrollbar-width: thin;

    :global(.mantine-ScrollArea-scrollbar),
    :global(.mantine-ScrollArea-thumb) {
        position: sticky !important;
    }

    &::before,
    &::after {
        content: "";

        position: absolute;
        top: 0;

        z-index: 10;

        width: 1px;
        height: calc(100% - var(--scrollbar-space));

        background-color: var(--mantine-color-gray-3);
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
}

.table {
    height: 1px; // hack to make inputs 100% td height
    position: relative;

    th {
        white-space: nowrap;
    }

    td:has(input:not([type='checkbox'])),
    td:has(textarea) {
        padding: 0;

        height: 100%;

        &:hover {
            background: var(--mantine-color-gray-0);
        }

        :global(.mantine-InputWrapper-root),
        :global(.mantine-Input-wrapper) {
            height: 100%;
        }

        input:not([type='checkbox']),
        textarea {
            width: 100%;
            height: 100%;

            padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));
            border: none;
            border-radius: 0;

            background-color: transparent;

            &:focus {
                outline: none;
            }
        }
    }
}

