import React from 'react';

import { Group, Stack } from '@mantine/core';

import { CompanyService } from 'models';

import { Z_INDEX } from 'components/diagram/diagram-z-index';

import { DiagramControls } from 'components/diagram/components';
import { DiagramIndicators } from 'components/diagram/components/diagram-indicators/DiagramIndicators';
import { DiagramMessages } from 'components/diagram/components/diagram-messages/DiagramMessages';
import { DiagramSimulationToolbar } from 'components/diagram/components/diagram-simulation/DiagramSimulationToolbar';
import { DiagramWireSizingToolbar } from 'components/diagram/components/diagram-wire-sizing/DiagramWireSizingToolbar';
import { FakeProjectIntercom } from 'components/intercom/types/FakeProjectIntercom';
import { ProjectIntercom } from 'components/intercom/types/ProjectIntercom';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentProject } from 'hooks/use-current-project';
import { useSimulationState } from 'components/diagram/hooks/use-simulation-state';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';

const DiagramFooter = () => {
    const user = useCurrentUser();
    const project = useCurrentProject()!;

    const { company: templateCompany } = useCompanyProfile(project?.template?.profile ?? null);
    const { company: referenceCompany } = useCompanyProfile(project?.reference?.profile ?? null);

    const { active: activeSimulation } = useSimulationState();
    const { active: activeWireSizing } = useWireSizings();

    if (activeSimulation) {
        return <DiagramSimulationToolbar />;
    }

    if (activeWireSizing) {
        return <DiagramWireSizingToolbar />;
    }
    const showTemplateIntercom = user && templateCompany?.services.includes(CompanyService.IN_APP_SUPPORT);
    const showReferenceIntercom = project.collaborators.manufacturers?.includes(referenceCompany?.id || '');

    return (
        <>
            <DiagramMessages />
            <DiagramIndicators />

            <Stack
                gap="xs"
                align="flex-end"
                style={{
                    position: 'absolute',
                    zIndex: Z_INDEX.CONTROLS,
                    right: 'var(--mantine-spacing-xs)',
                    bottom: 'var(--mantine-spacing-xs)',
                }}
                data-diagram-footer
            >
                <DiagramControls.ContextMenu />

                <Group gap="xs" align="flex-end">
                    <DiagramControls.Legend />

                    {showReferenceIntercom && referenceCompany && <FakeProjectIntercom company={referenceCompany} />}
                    {showTemplateIntercom && templateCompany && (
                        <ProjectIntercom
                            company={templateCompany}
                            project={project}
                            buttonProps={{ size: 'xs', h: 32 }}
                        />
                    )}
                </Group>
            </Stack>
        </>
    );
};

export { DiagramFooter };
