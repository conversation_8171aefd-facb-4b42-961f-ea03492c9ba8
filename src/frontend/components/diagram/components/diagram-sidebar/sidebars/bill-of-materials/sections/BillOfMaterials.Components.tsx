import { useEffect } from 'react';

import { Flex, Group, Loader, Text, Transition } from '@mantine/core';

import { BillOfMaterials, BillOfMaterialsPayload } from 'models';

import { BillOfMaterialsService } from 'services/BillOfMaterialsService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { DiagramSidebar } from 'components/diagram';
import { useBillOfMaterials, useDiagram } from 'components/diagram/hooks';

import { CheckboxButton } from 'components/checkbox-button/CheckboxButton';

import { BillOfMaterialsGrouped } from './BillOfMaterials.Grouped';
import { BillOfMaterialsUngrouped } from './BillOfMaterials.Ungrouped';
import { DiagramInputTable } from 'components/diagram/components/diagram-input-table/DiagramInputTable';

import { useFormContext } from 'react-hook-form';
import { Form, FormOnSubmit, useSubmitForm } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { AutoSave } from 'components/forms/AutoSave';

import { SimpleButton } from 'elements/buttons';
import { BillOfMaterialsView } from 'components/diagram/state/bill-of-materials';
import { useBillOfMaterialsState } from 'components/diagram/hooks/use-bill-of-materials-state';
import { BillOfMaterialsAddFieldModal } from '../components/BillOfMaterialsAddFieldModal';
import { BillOfMaterialsTableHeaders } from '../components/BillOfMaterialsTableHeaders';

import cx from './BillOfMaterials.Components.module.scss';

const BillOfMaterialsComponents = () => {
    const submit = useSubmitForm();
    const { formState } = useFormContext();

    const { billOfMaterials, isLoading } = useBillOfMaterials();
    const { currentView, openAllGroups } = useBillOfMaterialsState();

    const components = billOfMaterials?.components ?? [];
    const componentInstances = billOfMaterials?.componentInstances ?? [];

    const allComponents = [...components, ...componentInstances];

    const hasChanges = Object.keys(formState.dirtyFields).length > 0;

    useEffect(() => {
        if (hasChanges) {
            SidebarService.setConfirmModal({
                title: 'Update Bill of Materials?',
                children: 'You have unsaved changes. Do you want to save them?',
                labels: {
                    confirm: 'Save changes',
                    cancel: 'Discard changes',
                },
                onConfirm: async () => {
                    await submit();
                },
            });
        } else {
            SidebarService.setConfirmModal(null);
        }
    }, [hasChanges]);

    return (
        <DiagramSidebar.Section className={cx.list}>
            {isLoading ? (
                <Loader size="sm" p="md" />
            ) : allComponents?.length ? (
                <>
                    <Group gap={8} mb="xs">
                        <CheckboxButton
                            display="inline-flex"
                            label="Show per group"
                            checked={currentView === BillOfMaterialsView.GROUPED}
                            setChecked={(checked) =>
                                BillOfMaterialsService.setShowView(
                                    checked ? BillOfMaterialsView.GROUPED : BillOfMaterialsView.UNGROUPED,
                                )
                            }
                        />
                        {currentView === BillOfMaterialsView.GROUPED && (
                            <CheckboxButton
                                display="inline-flex"
                                label="Open all"
                                checked={openAllGroups}
                                setChecked={(checked) => BillOfMaterialsService.setShowOpenAllGroups(checked)}
                            />
                        )}
                        <SimpleButton ml="auto" onClick={() => BillOfMaterialsService.setShowAddFieldModal(true)}>
                            + Add custom field
                        </SimpleButton>
                    </Group>
                    <DiagramInputTable>
                        <BillOfMaterialsTableHeaders />
                        {currentView === BillOfMaterialsView.UNGROUPED ? (
                            <BillOfMaterialsUngrouped />
                        ) : (
                            <BillOfMaterialsGrouped />
                        )}
                    </DiagramInputTable>
                </>
            ) : (
                <Text c="dimmed" p="md">
                    Bill of materials is not available yet.
                </Text>
            )}
            <Transition mounted={hasChanges} transition="fade-up" duration={200}>
                {(styles) => (
                    <Flex pb="sm" justify="center" style={{ ...styles, position: 'sticky', bottom: 0, zIndex: 1 }}>
                        <FormSubmit variant="gradient">Save changes</FormSubmit>
                    </Flex>
                )}
            </Transition>

            <BillOfMaterialsAddFieldModal />
        </DiagramSidebar.Section>
    );
};

const WrappedBillOfMaterialsComponents = () => {
    const { groups } = useDiagram();
    const { billOfMaterials, mutate } = useBillOfMaterials();

    const onSubmit: FormOnSubmit<BillOfMaterials> = async (values) => {
        try {
            if (!billOfMaterials || !billOfMaterials.id) return;

            const cleanValues: BillOfMaterialsPayload = {
                ...values,
                components: values.components?.map((component) => ({
                    ...component,
                    component: component.component.id,
                })),
            };

            const result = await BillOfMaterialsService.update(billOfMaterials.id, cleanValues);

            if (!result.doc) {
                throw new Error();
            }

            await mutate(result, { revalidate: false });
        } catch {
            LocalNotificationService.showError({ message: 'Could not update bill of materials' });
        }
    };

    return (
        <Form<BillOfMaterials> onSubmit={onSubmit} defaultValues={billOfMaterials}>
            <AutoSave
                instant={groups.map((group) => `groups.${group.id}.showGroupFields`)}
                saveOnFields={groups.map((group) => `groups.${group.id}.showGroupFields`)}
            />
            <BillOfMaterialsComponents />
        </Form>
    );
};

export { WrappedBillOfMaterialsComponents as BillOfMaterialsComponents };
