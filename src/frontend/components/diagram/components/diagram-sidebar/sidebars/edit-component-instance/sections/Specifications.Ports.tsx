import React, { FC } from 'react';

import { IoAddSharp } from 'react-icons/io5';
import { list } from 'radash';

import { getComponentDefinition, DiagramComponentInstance } from 'models';

import { SimpleButton } from 'elements/buttons';

import { DiagramSidebar } from 'components/diagram';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { SpecificationsPort } from './Specifications.Port';

const SpecificationsPorts: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const { specifications } = componentInstance;
    const linkedToProduct = Boolean(componentInstance.componentId);

    const definition = getComponentDefinition(componentInstance.componentType);
    const ports = specifications.electrical.ports || [];
    const numberOfPorts = ports.length;

    return (
        <React.Fragment>
            {ports.map((_, index) => {
                const { displayAsSinglePort } = definition.ports;
                if (displayAsSinglePort && index !== 0) {
                    return null;
                }

                return (
                    <SpecificationsPort
                        port={index}
                        componentInstance={componentInstance}
                        switchable={!linkedToProduct && !displayAsSinglePort && numberOfPorts > 1}
                        switchablePorts={list(numberOfPorts - 1).filter((switchablePort) => switchablePort !== index)}
                        key={index}
                    />
                );
            })}
            {!linkedToProduct && <AddPortButton componentInstance={componentInstance} />}
        </React.Fragment>
    );
};

const AddPortButton: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const {
        hasDynamicNumberOfPorts,
        ports: { max: maximumNumberOfPorts },
    } = getComponentDefinition(componentInstance.componentType);

    const numberOfPorts = componentInstance.specifications.electrical.ports.length;

    if (!hasDynamicNumberOfPorts || numberOfPorts >= maximumNumberOfPorts) {
        return null;
    }

    const handle = async () => {
        ComponentInstanceService.addDynamicPort(componentInstance);
        DiagramSyncService.save();
    };

    return (
        <DiagramSidebar.Section>
            <SimpleButton data-keep-selection fullWidth onClick={handle}>
                <IoAddSharp />
                <span>Add port</span>
            </SimpleButton>
        </DiagramSidebar.Section>
    );
};

export { SpecificationsPorts };
