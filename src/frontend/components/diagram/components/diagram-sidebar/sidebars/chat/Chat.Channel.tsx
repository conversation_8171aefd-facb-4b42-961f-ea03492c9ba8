import React, { FC, useEffect, useRef, useState } from 'react';
import { DiagramChatChannel, DiagramChatMessage, FeatureKey } from 'models';

import Link from 'next/link';

import { IoChatbubbleEllipsesOutline, IoCreateOutline } from 'react-icons/io5';
import { ActionIcon, Alert, Box, Group, Modal, ScrollArea, Stack, Text, Tooltip } from '@mantine/core';
import { TbChevronLeft, TbMoodPlus } from 'react-icons/tb';

import { DiagramSidebar } from 'components/diagram';
import { Avatar } from 'components/avatar/Avatar';
import { ChannelHeader } from './Chat.ChannelHeader';

import { useUser } from 'hooks/use-user';
import { useChat } from 'components/diagram/hooks/use-chat';
import { useChatMessages } from 'components/diagram/hooks/use-chat-messages';
import { useProjectTeam } from 'hooks/use-project-team';

import { ChatService } from 'components/diagram/services/ChatService';
import { DateService } from 'services/DateService';

import { TeamHelpers } from 'helpers/TeamHelpers';

import { TipTapViewer } from 'components/tiptap/TipTapViewer';
import { Reactions } from 'components/reactions/Reactions';
import { AddReaction } from 'components/reactions/AddReaction';

import cx from 'components/diagram/components/diagram-comments/DiagramComment.module.scss';
import cx2 from './Chat.module.scss';

import { useComponentInstanceMentions } from '../../../../hooks/use-component-instance-mentions';
import { useUserMentions } from '../../../../hooks/use-user-mentions';
import { Composer } from './Chat.Composer';
import { useCurrentUser } from 'hooks/use-current-user';
import { useChatChannels } from 'components/diagram/hooks/use-chat-channels';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';
import { useFeatureAccess } from 'hooks/use-feature-access';
import { useCurrentProject } from 'hooks/use-current-project';

const Channel: FC<{
    channel: DiagramChatChannel;
}> = ({ channel }) => {
    const { thread } = useChat();
    const { messages } = useChatMessages();

    const subscription = useCurrentTeamSubscription();
    const access = useFeatureAccess(FeatureKey.TECHNICAL_SUPPORT) || channel.type !== 'engineering-support';

    useEffect(() => {
        ChatService.viewChannel(channel.id).then();
    }, [messages.length]);

    if (thread) {
        return <Thread channel={channel} thread={thread} />;
    }

    const topLevelMessages = messages.filter((message) => !message.parent);

    const copy = {
        'team': 'Start having a conversation with your team.',
        'engineering-support':
            "Ask your technical support questions here. We'll get you in touch with the right person.",
        'manufacturing-support': 'Ask your product questions.',
    }[channel.type];

    const confirmationMessage = {
        'team': undefined,
        'engineering-support':
            'Your request has been dispatched to out technical support team. We will get back to you shortly.',
        'manufacturing-support': undefined,
    }[channel.type];

    return (
        <React.Fragment>
            <ChannelHeader />
            <DiagramSidebar.Section height="calc(100% - 47px)">
                <Box style={{ display: 'flex', flexDirection: 'column', gap: '16px', height: '100%' }}>
                    <Messages messages={topLevelMessages} emptyCopy={copy} confirmationMessage={confirmationMessage} />
                    <Box
                        style={{
                            position: 'sticky',
                            bottom: 'var(--mantine-spacing-xs)',
                        }}
                    >
                        {!access && (
                            <Alert color="yellow" mb="xs">
                                You are currently on our <strong>{subscription} plan</strong>. Please note that
                                technical support is only available on our <strong>pro plan</strong>.
                                <Box mt="xs">
                                    <strong>
                                        <Link href="/upgrade" style={{ textDecoration: 'none', color: 'currentColor' }}>
                                            Upgrade your plan
                                        </Link>
                                    </strong>
                                </Box>
                            </Alert>
                        )}
                        <MessageComposer />
                    </Box>
                </Box>
            </DiagramSidebar.Section>
        </React.Fragment>
    );
};

const MessageComposer: FC<{
    initial?: {
        id: string;
        content: any;
        files: any[];
    };
    onSubmit?: () => void;
}> = ({ initial, onSubmit }) => {
    const project = useCurrentProject()!;
    const { channels } = useChatChannels();

    const { channel: activeChannelId, thread: activeThreadId } = useChat();
    const channel = channels.find((channel) => channel.id === activeChannelId);

    return channel ? (
        <Composer
            placeholder={`Message #${channel.name}`}
            initial={initial}
            onSubmit={async (content, files) => {
                if (initial) {
                    await ChatService.updateMessage(initial.id, JSON.stringify(content), files).then();
                } else {
                    await ChatService.createMessage({
                        projectId: project.id,
                        channelId: channel.id,
                        parentId: activeThreadId,
                        content: JSON.stringify(content),
                        files: files,
                    });
                }

                onSubmit?.();
            }}
            mentions={{
                ai: false,
                manufacturers: channel.access.company ? [channel.access.company] : [],
            }}
        />
    ) : null;
};

const Thread: FC<{
    channel: DiagramChatChannel;
    thread: DiagramChatMessage['id'];
}> = ({ thread }) => {
    const { messages } = useChatMessages();
    const threadMessages = messages.filter((message) => {
        return message.id === thread || message.parent === thread;
    });

    const deactivateThread = () => {
        ChatService.deactivateThread();
    };

    return (
        <React.Fragment>
            <DiagramSidebar.Header>
                <Box className={cx2.channelHeader}>
                    <Group gap="xs">
                        <DiagramSidebar.Button onClick={deactivateThread}>
                            <TbChevronLeft />
                        </DiagramSidebar.Button>
                        Thread
                    </Group>
                </Box>
            </DiagramSidebar.Header>
            <DiagramSidebar.Section height="calc(100% - 47px)">
                <Box style={{ display: 'flex', flexDirection: 'column', gap: '16px', height: '100%' }}>
                    <Messages messages={threadMessages} />
                    <MessageComposer />
                </Box>
            </DiagramSidebar.Section>
        </React.Fragment>
    );
};

const Messages: FC<{
    messages: DiagramChatMessage[];
    emptyCopy?: string;
    confirmationMessage?: string;
}> = ({ messages, emptyCopy = '', confirmationMessage = '' }) => {
    const { team } = useProjectTeam();
    const viewport = useRef<HTMLDivElement>(null);

    const hasCollaboratorResponse = messages.some((message) => {
        return !TeamHelpers.isTeamMember(team ?? null, message.createdBy);
    });

    const scrollToBottom = () => {
        setTimeout(() => {
            if (viewport.current) {
                viewport.current!.scrollTo({ top: viewport.current.scrollHeight });
            }
        }, 100);
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages.length]);

    return (
        <ScrollArea style={{ flexGrow: 1, flexShrink: 1 }} type="auto" viewportRef={viewport}>
            <Stack gap="xs">
                {messages.length === 0 && emptyCopy && (
                    <Text c="dimmed" ta="center">
                        {emptyCopy}
                    </Text>
                )}
                {messages.map((message) => (
                    <Message message={message} key={message.id} />
                ))}
                {messages.length > 0 && confirmationMessage && !hasCollaboratorResponse && (
                    <Text c="dimmed" ta="center">
                        {confirmationMessage}
                    </Text>
                )}
            </Stack>
        </ScrollArea>
    );
};

const Message: FC<{ message: DiagramChatMessage }> = ({ message }) => {
    const { thread } = useChat();
    const [editModalOpen, setEditModalOpen] = useState(false);

    const currentUser = useCurrentUser();
    const { user } = useUser(message.createdBy);
    const [{ content, files }] = message.content;

    const { company } = useCompanyProfile(message.createdByCompany);

    const { messages: all } = useChatMessages();
    const numberOfReplies = all.filter((needle) => needle.parent === message.id).length;
    const hasReplies = numberOfReplies > 0;

    const openThread = () => {
        if (!message.parent) {
            ChatService.activateThread(message.id);
        }
    };

    const openEditModal = () => {
        setEditModalOpen(true);
    };

    const { extension: componentInstanceMentionsExtension } = useComponentInstanceMentions();
    const { extension: userMentionsExtension } = useUserMentions('mentions-@');

    return (
        <Stack className={cx2.message}>
            <Group justify="space-between">
                <Group gap="xs">
                    <Avatar company={company} user={message.createdBy} size={20} />
                    <Group gap={4}>
                        <span style={{ fontSize: 12, fontWeight: 500 }}>
                            {company ? company.name : user ? user.name : ''}
                        </span>
                        <span style={{ fontSize: 12, color: 'var(--mantine-color-dimmed)' }}>
                            {DateService.formatDistanceToNow(message.createdAt)}
                        </span>
                    </Group>
                </Group>
                <Group gap={'xs'}>
                    {!thread && hasReplies && (
                        <Text fz={12}>{numberOfReplies === 1 ? '1 reply' : `${numberOfReplies} replies`}</Text>
                    )}
                    <Group className={cx2.messageActions} gap="4">
                        <AddReaction collection="diagramChatMessages" id={message.id}>
                            <ActionIcon variant="transparent" size={20}>
                                <TbMoodPlus size={16} strokeWidth={1.5} />
                            </ActionIcon>
                        </AddReaction>
                        {currentUser?.id === message.createdBy && (
                            <Tooltip label="Edit message" position="bottom" withArrow>
                                <ActionIcon variant="transparent" onClick={openEditModal} size={20}>
                                    <IoCreateOutline size={16} strokeWidth={1.5} />
                                </ActionIcon>
                            </Tooltip>
                        )}
                        {!thread && (
                            <Tooltip label={hasReplies ? 'View thread' : 'Reply in thread'} position="bottom" withArrow>
                                <ActionIcon variant="transparent" size={20} onClick={openThread}>
                                    <IoChatbubbleEllipsesOutline size={16} strokeWidth={1.5} />
                                </ActionIcon>
                            </Tooltip>
                        )}
                    </Group>
                </Group>
            </Group>
            <Box className={cx.threadItemBody}>
                <TipTapViewer
                    content={JSON.parse(content)}
                    extensions={[componentInstanceMentionsExtension, userMentionsExtension]}
                    files={files}
                />
                <Reactions reactions={message.reactions} collection="diagramChatMessages" id={message.id} mt={4} />
            </Box>
            <Modal
                title="Edit message"
                opened={editModalOpen}
                onClose={() => {
                    setEditModalOpen(false);
                }}
            >
                <MessageComposer
                    initial={{
                        id: message.id,
                        content: JSON.parse(content),
                        files: files,
                    }}
                    onSubmit={() => {
                        setEditModalOpen(false);
                    }}
                />
            </Modal>
        </Stack>
    );
};

export { Channel };
