import React, { FC } from 'react';

import { TbAt, TbHash } from 'react-icons/tb';

import { TipTapComposer } from 'components/tiptap/TipTapComposer';

import { useComponentInstanceMentions } from 'components/diagram/hooks/use-component-instance-mentions';
import { useUserMentions } from 'components/diagram/hooks/use-user-mentions';

const Composer: FC<{
    placeholder: string;
    initial?: {
        content: any;
        files: any[];
    };
    onSubmit: (content: object, files: any[]) => void;
    mentions: {
        ai: boolean;
        manufacturers: string[];
    };
}> = ({ placeholder, initial, onSubmit, mentions }) => {
    const { extension: componentInstanceMentionsExtension, component: componentInstanceMentionsComponent } =
        useComponentInstanceMentions();

    const { extension: userMentionsExtension, component: userMentionsComponent } = useUserMentions(
        'mentions-@',
        mentions,
    );

    return (
        <TipTapComposer
            placeholder={placeholder}
            extensions={[componentInstanceMentionsExtension, userMentionsExtension]}
            initial={initial}
            onSubmit={onSubmit}
        >
            <TipTapComposer.Content />
            <TipTapComposer.Footer>
                <TipTapComposer.EmojiButton />
                <TipTapComposer.Button
                    onClick={(editor) => {
                        editor.commands.focus();
                        editor.commands.insertContent('@');
                    }}
                >
                    <TbAt strokeWidth={1.5} />
                </TipTapComposer.Button>
                <TipTapComposer.Button
                    onClick={(editor) => {
                        editor.commands.focus();
                        editor.commands.insertContent('#');
                    }}
                >
                    <TbHash />
                </TipTapComposer.Button>
                <TipTapComposer.FilesButton />
            </TipTapComposer.Footer>
            {componentInstanceMentionsComponent}
            {userMentionsComponent}
        </TipTapComposer>
    );
};

export { Composer };
