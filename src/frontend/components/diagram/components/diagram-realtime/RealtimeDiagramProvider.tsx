import React, { useEffect, FC } from 'react';
import { Transaction } from 'sync-engine';
import { User, WebSocketEvent } from 'models';

import { ChannelProvider, useChannel } from 'ably/react';
import { SpaceProvider, useSpace } from '@ably/spaces/react';

import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { CommentService } from 'components/diagram/services';
import { ChatService } from 'components/diagram/services/ChatService';
import { SimulationService } from 'components/diagram/services/SimulationService';
import { WireSizingService } from 'components/diagram/services/WireSizingService';
import { JobService } from 'components/diagram/services/JobService';

import { CompressionHelpers } from 'helpers/CompressionHelpers';

import { useAblyConnectable } from 'hooks/use-ably-connectable';
import { useCurrentUser } from 'hooks/use-current-user';
import { useDiagram } from 'components/diagram/hooks';
import { AIConversationService } from 'components/diagram/services/AIConversationService';

const Connector: FC<{ user: User }> = ({ user }) => {
    const { space } = useSpace();
    const channelState = space?.channel.state;

    useEffect(() => {
        if (space && channelState === 'attached') {
            space.enter(user).then();

            return () => {
                space?.leave().catch((error) => {
                    console.warn({
                        message: 'Error leaving space',
                        error,
                    });
                });
            };
        }
    }, [user?.id, space, channelState]);

    return null;
};

const Listeners: FC = () => {
    const { id } = useDiagram();

    useChannel(`diagram-${id}`, async (message) => {
        switch (message.name) {
            case WebSocketEvent.PROJECT_REFRESH_CHAT_CHANNELS: {
                await ChatService.refreshChannels(message.data.projectId);

                break;
            }
            case WebSocketEvent.PROJECT_REFRESH_CHAT_MESSAGES: {
                await ChatService.refreshMessages(message.data.channelId);

                break;
            }
            case WebSocketEvent.DIAGRAM_TRANSACTIONS: {
                const { compression, transactions: transactionsOrString } = message.data;

                let transactions = transactionsOrString;

                if (compression === 'gzip') {
                    const transactionsAsString = await CompressionHelpers.decompress(transactionsOrString);
                    transactions = JSON.parse(transactionsAsString);
                }

                if (transactions.length > 0) {
                    DiagramSyncService.pullTransactions(transactions as Transaction[]);
                }

                break;
            }
            case WebSocketEvent.DIAGRAM_TRANSACTION: {
                const { compression, transaction: transactionOrString } = message.data;

                let transaction = transactionOrString;

                if (compression === 'gzip') {
                    const transactionAsString = await CompressionHelpers.decompress(transactionOrString);
                    transaction = JSON.parse(transactionAsString);
                }

                DiagramSyncService.pullTransactions([transaction as Transaction]);

                break;
            }
            case WebSocketEvent.DIAGRAM_REFRESH_AI_CONVERSATION: {
                await AIConversationService.refresh(message.data.diagramId);

                break;
            }
            case WebSocketEvent.DIAGRAM_REFRESH_COMMENTS: {
                await CommentService.refreshComments(message.data.diagramId);

                break;
            }
            case WebSocketEvent.DIAGRAM_REFRESH_SIMULATIONS: {
                await SimulationService.refresh(message.data.diagramId, message.data.type);

                break;
            }
            case WebSocketEvent.DIAGRAM_REFRESH_WIRE_SIZINGS: {
                await WireSizingService.refresh(message.data.diagramId);

                break;
            }
            case WebSocketEvent.DIAGRAM_WORST_CASE_LOAD_CALCULATED: {
                WireSizingService.worstCaseLoadCompleted();

                break;
            }
            case WebSocketEvent.DIAGRAM_REFRESH_JOB_LOGS: {
                await JobService.refreshLogs(message.data.jobId);

                break;
            }
        }
    });

    return null;
};

const RealtimeDiagramProvider: FC<{ children: React.ReactNode }> = ({ children }) => {
    const user = useCurrentUser();
    const connectable = useAblyConnectable();

    const { id } = useDiagram();
    const name = `diagram-${id}`;

    return (
        <SpaceProvider name={name} key={name} options={{ offlineTimeout: 5000 }}>
            <ChannelProvider channelName={name}>
                {user && connectable && <Connector user={user} />}
                <Listeners />
                {children}
            </ChannelProvider>
        </SpaceProvider>
    );
};

export { RealtimeDiagramProvider };
