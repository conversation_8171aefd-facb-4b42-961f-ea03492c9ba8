import React, { FC, useEffect } from 'react';

import { Box } from '@mantine/core';

import {
    DIAGRAM_SIDEBAR_NAV_WIDTH,
    DiagramAutoSave,
    DiagramCanvas,
    DiagramDragAndDrop,
    DiagramEffects,
    DiagramSidebar,
} from 'components/diagram';
import { SIDEBAR_NAV_WIDTH } from 'components/sidebar-nav/SidebarNav';

import { Page } from 'components/page';

import { DiagramFooter } from 'components/diagram/components/diagram-footer/DiagramFooter';
import { DiagramAddFileModalComponent } from 'components/diagram/components/diagram-add-file/DiagramAddFileModal.Component';
import { DiagramAddFileModalDesign } from 'components/diagram/components/diagram-add-file/DiagramAddFileModal.Design';
import { DiagramTour } from 'components/diagram/components/diagram-tour/DiagramTour';
import { DiagramDragAndDropHandlers } from './components/diagram-drag-and-drop/DiagramDragAndDropHandlers';
import { RealtimeDiagramProvider } from 'components/diagram/components/diagram-realtime/RealtimeDiagramProvider';
import { DiagramGettingStarted } from 'components/diagram/components/diagram-getting-started/DiagramGettingStarted';
import { ProjectAutoSave } from 'components/diagram/components/project-auto-save/ProjectAutoSave';
import { DiagramBadges } from 'components/diagram/components/diagram-badges/DiagramBadges';
import { DiagramSyncStatus } from 'components/diagram/components/diagram-sync-status/DiagramSyncStatus';
import { MeasurementSystemModal } from 'components/measurement-system-modal/MeasurementSystemModal';
import { DiagramMobileOverlay } from 'components/diagram/components/diagram-mobile-overlay/DiagramMobileOverlay';
import { DiagramPrintOverlay } from 'components/diagram/components/diagram-print-overlay/DiagramPrintOverlay';

import { useActions } from 'components/diagram/hooks/use-actions';
import { useDeeplinks } from 'components/diagram/hooks/use-deeplinks';
import { useInitialTransform } from 'components/diagram/hooks/use-initial-transform';
import { useSetOverscroll } from 'components/diagram/hooks/use-set-overscroll';
import { usePermission } from 'hooks/use-permission';
import { useCurrentProject } from 'hooks/use-current-project';
import { useSidebar } from 'components/diagram/hooks/use-sidebar';
import { useSidebarNav } from 'hooks/use-sidebar-nav';

import { PermissionDiagramElements } from 'models';

import { DiagramService } from './services/DiagramService';
import { SidebarService } from 'components/diagram/services/SidebarService';

import classes from './Diagram.module.scss';

import '../../temp-dusan-debug';

const Diagram: FC = () => {
    const project = useCurrentProject();
    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    useActions();
    useDeeplinks();
    useInitialTransform();
    useSetOverscroll();

    useEffect(() => {
        DiagramService.setMode(canEdit ? 'edit' : 'navigate');
    }, [canEdit]);

    useEffect(() => {
        document.body.classList.add('is-diagram');

        return () => {
            document.body.classList.remove('is-diagram');
        };
    }, []);

    const { width: diagramSidebarWidth, collapsed } = useSidebar();
    const { isOpen: sidebarIsOpen } = useSidebarNav();
    const showSidebar = SidebarService.getShowSidebar();

    const diagramSidebarIsOpen = showSidebar && !collapsed;

    const getBreadcrumbWidth = () => {
        let width = '100% - var(--mantine-spacing-xs) * 2';

        if (sidebarIsOpen) {
            width = `${width} - ${SIDEBAR_NAV_WIDTH}px`;
        }

        if (diagramSidebarIsOpen) {
            width = `${width} - ${diagramSidebarWidth}px`;
        } else {
            width = `${width} - ${DIAGRAM_SIDEBAR_NAV_WIDTH}px`;
        }

        return `calc(${width})`;
    };

    return (
        <RealtimeDiagramProvider>
            <Page
                breadcrumbs={{
                    type: 'floating',
                    rightSection: (
                        <>
                            <DiagramSyncStatus />
                            <DiagramBadges />
                        </>
                    ),
                    showHomeAsLogo: true,
                    wrapperProps: {
                        w: getBreadcrumbWidth(),
                    },
                }}
                hideFooter
                title={project?.name}
            >
                <Page.FullScreenContent>
                    <DiagramPrintOverlay />
                    {canEdit && <DiagramMobileOverlay />}
                    {canEdit && <DiagramAutoSave />}
                    {canEdit && <ProjectAutoSave />}
                    {canEdit && <MeasurementSystemModal />}
                    <DiagramEffects />
                    <Box
                        style={{
                            width: '100vw',
                            height: '100dvh',
                            // Prevent navigation when scrolling left
                            overflowBehavior: 'none',
                            select: 'none',
                        }}
                    >
                        <Box
                            id="diagram-editor-container"
                            style={{
                                display: 'flex',
                                flexDirection: 'row',

                                height: '100dvh',
                                overflow: 'hidden',
                            }}
                        >
                            <DiagramAddFileModalComponent />
                            <DiagramAddFileModalDesign />
                            {canEdit && (
                                <React.Fragment>
                                    <DiagramGettingStarted />
                                    <DiagramTour />
                                </React.Fragment>
                            )}
                            <DiagramDragAndDrop>
                                <Box id="diagram-canvas-with-header" className={classes.canvas}>
                                    <DiagramCanvas />
                                    <DiagramFooter />
                                </Box>
                                <DiagramSidebar />
                                <DiagramDragAndDropHandlers />
                            </DiagramDragAndDrop>
                        </Box>
                    </Box>
                </Page.FullScreenContent>
            </Page>
        </RealtimeDiagramProvider>
    );
};

export default Diagram;
