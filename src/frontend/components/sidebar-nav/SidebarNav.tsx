import React, { FC, useEffect } from 'react';
import { Router } from 'next/router';

import { Box, ScrollArea, Stack } from '@mantine/core';

import { sidebarNavState } from 'state/sidebar-nav';

import { useIsMobile } from 'hooks/use-is-mobile';
import { useSidebarNav } from 'hooks/use-sidebar-nav';

import { SidebarNavItem } from 'components/sidebar-nav/components/SidebarNav.Item';
import { SidebarNavTitle } from 'components/sidebar-nav/components/SidebarNav.Title';
import { SidebarNavBadge } from 'components/sidebar-nav/components/SidebarNav.Badge';
import { SidebarNavHeader } from 'components/sidebar-nav/components/SidebarNav.Header';
import { SidebarNavDivider } from 'components/sidebar-nav/components/SidebarNav.Divider';
import { SidebarNavSection } from 'components/sidebar-nav/components/SidebarNav.Section';
import { SidebarNavProfile } from 'components/sidebar-nav/components/SidebarNav.Profile';
import { SidebarNavUserIcon } from 'components/sidebar-nav/components/SidebarNav.UserIcon';
import { SidebarNavTeamSwitcher } from 'components/sidebar-nav/components/SidebarNav.TeamSwitcher';
import { SidebarNavCreateButton } from 'components/sidebar-nav/components/SidebarNav.CreateButton';
import { SidebarNavUpdateMessage } from 'components/sidebar-nav/components/SidebarNav.UpdateMessage';
import { SidebarNavDesignEditorMessage } from './components/SidebarNav.DesignEditorMessage';
import { SidebarNavSearch } from 'components/sidebar-nav/components/SidebarNav.Search';

import { SidebarNavCompany } from './sections/SidebarNav.Company';
import { SidebarNavExplore } from 'components/sidebar-nav/sections/SidebarNav.Explore';
import { SidebarNavDesignEditor } from 'components/sidebar-nav/sections/SidebarNav.DesignEditor';
import { SidebarNavResources } from 'components/sidebar-nav/sections/SidebarNav.Resources';
import { SidebarNavUserLinks } from 'components/sidebar-nav/sections/SidebarNav.UserLinks';
import { SidebarNavFinishSignup } from 'components/sidebar-nav/components/SidebarNav.FinishSignup';

import cx from './SidebarNav.module.scss';

export const SIDEBAR_NAV_WIDTH = 240;

const SidebarNav: FC & {
    Item: typeof SidebarNavItem;
    Badge: typeof SidebarNavBadge;
    Title: typeof SidebarNavTitle;
    Header: typeof SidebarNavHeader;
    Divider: typeof SidebarNavDivider;
    Section: typeof SidebarNavSection;
    Profile: typeof SidebarNavProfile;
    UserIcon: typeof SidebarNavUserIcon;
    TeamSwitcher: typeof SidebarNavTeamSwitcher;
    CreateButton: typeof SidebarNavCreateButton;
    UpdateMessage: typeof SidebarNavUpdateMessage;
    DesignEditorMessage: typeof SidebarNavDesignEditorMessage;

    Explore: typeof SidebarNavExplore;
    Resources: typeof SidebarNavResources;
    DesignEditor: typeof SidebarNavDesignEditor;
    UserLinks: typeof SidebarNavUserLinks;
    Company: typeof SidebarNavCompany;
} = () => {
    const { isOpen, isMobileOpen, isFloating } = useSidebarNav();

    return (
        <>
            <Box
                className={cx.sidebar}
                data-sidebar-nav
                data-open={isOpen}
                data-open-mobile={isMobileOpen}
                data-floating={isFloating}
            >
                <ScrollArea className={cx.scrollArea} type="never" h="100dvh">
                    <SidebarNav.Header />

                    <Stack className={cx.inner}>
                        <SidebarNavSearch />

                        <SidebarNavFinishSignup />

                        <SidebarNav.Explore />

                        <SidebarNav.Divider />

                        <SidebarNav.UserLinks />

                        <SidebarNav.Company />

                        <SidebarNav.DesignEditor />

                        <Stack mt="auto" gap="md">
                            <SidebarNav.Resources />
                            <SidebarNav.UpdateMessage />
                            <SidebarNav.DesignEditorMessage />
                        </Stack>
                    </Stack>
                </ScrollArea>
            </Box>
            <SidebarNavSetup />
        </>
    );
};

const SidebarNavSetup = () => {
    const { isOpen, isFloating } = useSidebarNav();
    const isMobile = useIsMobile('md');

    useEffect(() => {
        if (isOpen) {
            document.body.classList.add('is-sidebar-open');
        } else {
            document.body.classList.remove('is-sidebar-open');
        }
    }, [isOpen]);

    useEffect(() => {
        Router.events.on('routeChangeComplete', () => {
            sidebarNavState.isMobileOpen = false;
        });
    }, []);

    useEffect(() => {
        if (isMobile) {
            sidebarNavState.isOpen = false;
            return;
        }

        if (!isFloating) {
            sidebarNavState.isOpen = true;
        }
    }, [isMobile]);

    useEffect(() => {
        const navbar = document.querySelector('#app_shell .mantine-AppShell-navbar');

        navbar?.addEventListener('click', (event) => {
            const clickInSidebar = (event.target as HTMLElement).closest('[data-sidebar-nav]');

            if (!clickInSidebar) {
                sidebarNavState.isMobileOpen = false;
            }
        });
    }, []);

    return null;
};

SidebarNav.Item = SidebarNavItem;
SidebarNav.Badge = SidebarNavBadge;
SidebarNav.Title = SidebarNavTitle;
SidebarNav.Header = SidebarNavHeader;
SidebarNav.Divider = SidebarNavDivider;
SidebarNav.Section = SidebarNavSection;
SidebarNav.Profile = SidebarNavProfile;
SidebarNav.UserIcon = SidebarNavUserIcon;
SidebarNav.TeamSwitcher = SidebarNavTeamSwitcher;
SidebarNav.CreateButton = SidebarNavCreateButton;
SidebarNav.UpdateMessage = SidebarNavUpdateMessage;
SidebarNav.DesignEditorMessage = SidebarNavDesignEditorMessage;

SidebarNav.Explore = SidebarNavExplore;
SidebarNav.DesignEditor = SidebarNavDesignEditor;
SidebarNav.Resources = SidebarNavResources;
SidebarNav.UserLinks = SidebarNavUserLinks;
SidebarNav.Company = SidebarNavCompany;

export { SidebarNav };
