import React from 'react';

import { Group, Title } from '@mantine/core';

const SidebarNavTitle = ({ icon, children }: { icon?: React.ReactNode; children: string }) => {
    return (
        <Title
            order={3}
            fw={600}
            fz={10}
            c="dimmed"
            style={{
                userSelect: 'none',
            }}
        >
            <Group gap={4}>
                {icon}
                {children}
            </Group>
        </Title>
    );
};

export { SidebarNavTitle };
