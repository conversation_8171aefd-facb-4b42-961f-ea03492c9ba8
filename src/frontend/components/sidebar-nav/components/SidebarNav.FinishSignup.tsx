import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { Box, Button } from '@mantine/core';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { RouterService } from 'services/RouterService';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import cx from './SidebarNav.FinishSignup.module.scss';

const SidebarNavFinishSignup = () => {
    const user = useCurrentUser();

    return user && !user.name ? <FinishSignupUser /> : <FinishSignupCompany />;
};

const FinishSignupUser = () => (
    <Box className={cx.finishSignup}>
        You did not complete your profile yet
        <Button variant="gradient" size="xs" fullWidth component={Link} href="/account">
            Tell us about yourself
        </Button>
    </Box>
);

const FinishSignupCompany = () => {
    const user = useCurrentUser();
    const team = useCurrentTeam();

    const [localSignupUrl, setLocalSignupUrl] = useState('');

    useEffect(() => {
        if (team && user) {
            setLocalSignupUrl(CompanyProfileService.getLocalSignupUrl(team.id));
        }
    }, [team, user]);

    const navigate = async () => {
        await RouterService.push(localSignupUrl ?? CompanyProfileHelpers.urls.create());
    };

    if (localSignupUrl) {
        return (
            <Box className={cx.finishSignup}>
                Finish setting up your company profile.
                <Button variant="gradient" onClick={navigate} fullWidth size="xs">
                    Continue Setup
                </Button>
            </Box>
        );
    }

    if (user?.isManufacturer && !user.hasCompany) {
        return (
            <Box className={cx.finishSignup}>
                Do not forget to create your company profile.
                <Button variant="gradient" onClick={navigate} fullWidth size="xs">
                    Create profile
                </Button>
            </Box>
        );
    }

    return null;
};

export { SidebarNavFinishSignup };
