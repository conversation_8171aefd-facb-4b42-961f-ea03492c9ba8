import React from 'react';

import { Box, BoxProps, Stack } from '@mantine/core';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { CollapsibleSection } from 'components/collapsible-section/CollapsibleSection';

const SidebarNavSection = ({
    storageKey,
    icon,
    title,
    boxProps,
    children,
}: {
    storageKey?: string;
    icon?: React.ReactNode;
    title?: string;
    boxProps?: BoxProps;
    children: React.ReactNode;
}) => {
    if (storageKey && title) {
        return (
            <Box {...boxProps}>
                <CollapsibleSection
                    storageKey={storageKey}
                    title={<SidebarNav.Title icon={icon}>{title}</SidebarNav.Title>}
                >
                    <Stack gap="xs">{children}</Stack>
                </CollapsibleSection>
            </Box>
        );
    }

    return (
        <Stack gap="xs" {...boxProps}>
            {title && <SidebarNav.Title>{title}</SidebarNav.Title>}
            {children}
        </Stack>
    );
};

export { SidebarNavSection };
