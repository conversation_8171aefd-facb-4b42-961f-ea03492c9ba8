import { Tooltip, UnstyledButton } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import cx from './SidebarNav.CreateButton.module.scss';
import cxTooltip from './SidebarNav.Tooltip.module.scss';

const SidebarNavCreateButton = ({ tooltip, onClick }: { tooltip: string; onClick: () => void }) => {
    return (
        <Tooltip label={tooltip} position="top-end" classNames={cxTooltip} offset={0}>
            <UnstyledButton
                className={cx.button}
                onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();

                    onClick();
                }}
            >
                <IoAddSharp size={16} />
            </UnstyledButton>
        </Tooltip>
    );
};

export { SidebarNavCreateButton };
