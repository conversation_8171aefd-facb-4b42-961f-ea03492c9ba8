import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { UnstyledButton, Menu } from '@mantine/core';
import { TbFileInfo, TbLogout } from 'react-icons/tb';
import { IoSettingsOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { AuthenticationService } from 'services/AuthenticationService';

import { Avatar } from 'components/avatar/Avatar';
import { AdminZone } from 'components/header/components/AdminZone';

import cxMenu from './Menu.module.scss';
import cx from './SidebarNav.UserIcon.module.scss';

const SidebarNavUserIcon: FC = () => {
    const user = useCurrentUser();

    if (!user) {
        return null;
    }

    const handleLogout = async () => {
        await AuthenticationService.logout();
    };

    return (
        <Menu
            classNames={cxMenu}
            offset={4}
            position="bottom-end"
            transitionProps={{
                transition: 'pop-top-right',
            }}
        >
            <Menu.Target>
                <UnstyledButton className={cx.userIcon}>
                    <Avatar user={user} size="sm" />
                </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown>
                <Link href="/account" legacyBehavior>
                    <Menu.Item component="a" leftSection={<IoSettingsOutline size={14} />}>
                        My account
                    </Menu.Item>
                </Link>
                <Menu.Item
                    component={Link}
                    href="/license-agreement"
                    target="_blank"
                    leftSection={<TbFileInfo size={14} />}
                >
                    License agreement
                </Menu.Item>
                <Menu.Item leftSection={<TbLogout size={14} />} c="red" onClick={handleLogout}>
                    Logout
                </Menu.Item>

                <AdminZone />
            </Menu.Dropdown>
        </Menu>
    );
};

export { SidebarNavUserIcon };
