import { useEffect, useState } from 'react';
import { Box, LoadingOverlay, Transition } from '@mantine/core';
import { useInterval } from '@mantine/hooks';

type Props = {
    enabled: boolean;
    steps: React.ReactNode[];
    onStep?: (step: number) => void;
    onEnd?: () => void;
    interval?: number;
};

export const SteppedLoadingOverlay = ({ enabled, steps, onStep, onEnd, interval = 2000 }: Props) => {
    const [step, setStep] = useState(0);
    const timer = useInterval(() => setStep((s) => s + 1), interval);

    const reset = () => {
        setStep(0);
        timer.stop();
    };

    useEffect(() => {
        if (enabled) {
            timer.start();
        } else {
            reset();
        }

        return reset;
    }, [enabled]);

    useEffect(() => {
        if (step >= steps.length) {
            onEnd?.();
        } else {
            onStep?.(step);
        }
    }, [step, steps.length, onEnd, onStep]);

    return (
        <LoadingOverlay
            visible={enabled}
            loaderProps={{
                children: steps.map((stepComponent, index) => (
                    <Transition
                        mounted={index === step}
                        duration={1000}
                        transition="fade"
                        exitDuration={0}
                        key={`SteppedLoadingOverlay-${index}`}
                    >
                        {(style) => <Box style={style}>{stepComponent}</Box>}
                    </Transition>
                )),
            }}
        />
    );
};
