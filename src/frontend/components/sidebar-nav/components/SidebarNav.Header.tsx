import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { Box } from '@mantine/core';
import { TbUser } from 'react-icons/tb';

import { UserReferrer } from 'models';

import { useCurrentUser } from 'hooks/use-current-user';
import { useLocalUserInfo } from 'hooks/use-local-user-info';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { UnstyledButton } from '@mantine/core';

import { Logo } from 'components/logo';
import { ReplusLogo } from 'components/logo/ReplusLogo';
import { SidebarNav } from 'components/sidebar-nav/SidebarNav';

import cx from './SidebarNav.Header.module.scss';

const SidebarNavHeader: FC = () => {
    const user = useCurrentUser();

    const { initialized, referrer } = useLocalUserInfo();

    if (!user) {
        return (
            <Box className={cx.root}>
                {initialized ? (
                    <UnstyledButton component={Link} href={RouterHelpers.urls.search()} className={cx.logo}>
                        {referrer === UserReferrer.REPLUS ? <ReplusLogo width={40} /> : <Logo width={70} />}
                    </UnstyledButton>
                ) : (
                    <div />
                )}

                <SidebarNav.Item href="/login" icon={<TbUser />} w={'fit-content'}>
                    Login
                </SidebarNav.Item>
            </Box>
        );
    }

    return (
        <Box className={cx.root}>
            <SidebarNav.TeamSwitcher />

            {/* {showFreeBadge && (
                <SidebarNav.Badge
                    mr="auto"
                    color="orange"
                    href={`/upgrade?redirect=${query.redirect || asPath}`}
                    tooltip="Upgrade"
                >
                    Free
                </SidebarNav.Badge>
            )} */}

            <Box className={cx.rightSide}>
                <SidebarNav.UserIcon />
            </Box>
        </Box>
    );
};

export { SidebarNavHeader };
