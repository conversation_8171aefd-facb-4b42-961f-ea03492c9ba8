import React, { useEffect, useState } from 'react';

import { Flex } from '@mantine/core';
import { BsPersonVcard } from 'react-icons/bs';

import { CompanySubscription, getCompanySubscriptionData, PublishedStatus } from 'models';

import { useTeam } from 'hooks/use-team';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { SidebarNavDot } from './SidebarNav.Dot';

const SidebarNavProfile = () => {
    const { companies } = useCurrentTeamCompanies();
    const nbCompanies = companies.length;
    const company = nbCompanies === 1 ? companies[0] : null;

    const { team } = useTeam(company?.team);
    const subscription =
        (team && getCompanySubscriptionData(team.subscriptions)?.subscription) ?? CompanySubscription.NONE;

    const [localSignupUrl, setLocalSignupUrl] = useState('');

    useEffect(() => {
        if (team) {
            setLocalSignupUrl(CompanyProfileService.getLocalSignupUrl(team.id));
        }
    }, [team]);

    if (localSignupUrl && nbCompanies <= 1) {
        return (
            <SidebarNav.Item
                icon={<BsPersonVcard />}
                href={localSignupUrl}
                rightSection={<SidebarNavDot tooltip="Continue setting up your profile..." color="red" />}
                color="primary"
            >
                My Company Profile
            </SidebarNav.Item>
        );
    }

    if (company) {
        return (
            <SidebarNav.Item
                icon={<BsPersonVcard />}
                href={CompanyProfileHelpers.urls.view(company.slug)}
                rightSection={
                    <Flex gap={4}>
                        {subscription === CompanySubscription.PREMIUM && (
                            <SidebarNav.Badge variant="gradient">Premium</SidebarNav.Badge>
                        )}
                        {company.status === PublishedStatus.DRAFT && (
                            <SidebarNavDot tooltip="Your profile is not published" color="red" />
                        )}
                        {company.status === PublishedStatus.REVIEW && (
                            <SidebarNavDot tooltip="Your profile is in review" color="orange" />
                        )}
                    </Flex>
                }
                color="primary"
            >
                My Company Profile
            </SidebarNav.Item>
        );
    }

    if (nbCompanies > 1) {
        return (
            <SidebarNav.Item icon={<BsPersonVcard />} href={CompanyProfileHelpers.urls.manage()} color="primary">
                My Company Profiles
            </SidebarNav.Item>
        );
    }

    return (
        <SidebarNav.Item icon={<BsPersonVcard />} href={CompanyProfileHelpers.urls.create()} color="primary">
            Create Company Profile
        </SidebarNav.Item>
    );
};

export { SidebarNavProfile };
