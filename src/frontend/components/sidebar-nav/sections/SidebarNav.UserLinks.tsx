import React, { <PERSON> } from 'react';

import { MantineColor } from '@mantine/core';
import { IoBookmarkOutline, IoFileTrayOutline, IoChatbubblesOutline } from 'react-icons/io5';

import { useInbox } from 'hooks/use-inbox';
import { useCurrentUser } from 'hooks/use-current-user';
import { useSupportCenter } from 'hooks/use-support-center';
import { useShowShowtimeNav } from 'hooks/use-show-showtime-nav';

import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';
import { SavedItemsHelpers } from 'helpers/SavedItemsHelpers';
import { SupportCenterHelpers } from 'helpers/SupportCenterHelpers';

import { ReplusLogo } from 'components/logo/ReplusLogo';
import { SidebarNav } from 'components/sidebar-nav/SidebarNav';

const SidebarNavUserLinks: FC = () => {
    const user = useCurrentUser();

    const showShowtimeNav = useShowShowtimeNav();

    if (!user) {
        if (showShowtimeNav) {
            return <SidebarNav.Section>{showShowtimeNav && <Showtime />}</SidebarNav.Section>;
        }

        return null;
    }

    return (
        <SidebarNav.Section>
            {showShowtimeNav && <Showtime />}
            <LeadManagement color={showShowtimeNav ? 'gray' : 'primary'} />
            <Notifications />
            <Bookmarks />
        </SidebarNav.Section>
    );
};

const Showtime = () => (
    <SidebarNav.Item
        icon={<ReplusLogo width={12} height={12} />}
        href={ShowtimeHelpers.urls.showtime()}
        color="primary"
    >
        Showtime
    </SidebarNav.Item>
);

const LeadManagement = ({ color }: { color?: MantineColor }) => {
    const user = useCurrentUser();
    const { supportCenterChannels } = useSupportCenter();

    if (!user?.isSupportUser) {
        return null;
    }

    const totalUnreadCount = supportCenterChannels.reduce(
        (sum, channel) => sum + SupportCenterHelpers.getUnreadCount(channel),
        0,
    );

    return (
        <SidebarNav.Item
            icon={<IoChatbubblesOutline />}
            href="/support-center"
            rightSection={
                totalUnreadCount > 0 && (
                    <SidebarNav.Badge color="red" variant="filled">
                        {totalUnreadCount}
                    </SidebarNav.Badge>
                )
            }
            color={color}
        >
            Lead Management
        </SidebarNav.Item>
    );
};

const Notifications = () => {
    const { unreadCount } = useInbox();

    return (
        <SidebarNav.Item
            href="/inbox"
            icon={<IoFileTrayOutline />}
            rightSection={
                unreadCount > 0 && (
                    <SidebarNav.Badge color="red" variant="filled">
                        {unreadCount}
                    </SidebarNav.Badge>
                )
            }
        >
            Notifications
        </SidebarNav.Item>
    );
};

const Bookmarks = () => (
    <SidebarNav.Item icon={<IoBookmarkOutline />} href={SavedItemsHelpers.urls.overview()}>
        My Bookmarks
    </SidebarNav.Item>
);

export { SidebarNavUserLinks };
