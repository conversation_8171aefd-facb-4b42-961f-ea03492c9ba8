import React, { <PERSON> } from 'react';

import { IoBookOutline, IoFolderOutline, IoGlobeOutline } from 'react-icons/io5';
import { TbSparkles } from 'react-icons/tb';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { SidebarNavItemProps } from 'components/sidebar-nav/components/SidebarNav.Item';
import { BsPersonVcard } from 'react-icons/bs';
import { RouterHelpers } from 'helpers/RouterHelpers';

const items: SidebarNavItemProps[] = [
    {
        children: 'Get Matched',
        href: RouterHelpers.urls.searchAssistant(),
        icon: <TbSparkles />,
        color: 'primary',
    },
    {
        children: 'Product Catalog',
        href: RouterHelpers.urls.searchTab('overview'),
        icon: <IoBookOutline />,
    },
    {
        children: 'Company Profiles',
        href: RouterHelpers.urls.searchTab('profiles'),
        icon: <BsPersonVcard />,
    },
    {
        children: 'Reference Designs',
        href: RouterHelpers.urls.searchTab('designs'),
        icon: <IoFolderOutline />,
    },
    {
        children: 'DC Microgrid Map',
        href: '/map',
        icon: <IoGlobeOutline />,
    },
];

const SidebarNavExplore: FC = () => {
    return (
        <SidebarNav.Section>
            <SidebarNav.Title icon={<IoGlobeOutline />}>Discover</SidebarNav.Title>
            {items.map((item) => (
                <SidebarNav.Item key={item.href} {...item} />
            ))}
        </SidebarNav.Section>
    );
};

export { SidebarNavExplore };
