import React, { FC } from 'react';

import { useRecentProjects } from 'hooks/use-recent-projects';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { useCurrentUser } from 'hooks/use-current-user';

const SidebarNavRecentProjects: FC = () => {
    const recentProjects = useRecentProjects();

    if (!recentProjects?.length) return null;

    return (
        <SidebarNav.Section storageKey="sidebar.recentProjects" title="My recent projects" boxProps={{ ml: 20 }}>
            {recentProjects?.slice(0, 3).map((project) => (
                <SidebarNav.Item key={project.id} href={ProjectHelpers.urls.editor(project.id)}>
                    {project.name}
                </SidebarNav.Item>
            ))}
        </SidebarNav.Section>
    );
};

const WrappedSidebarNavRecentProjects: FC = () => {
    const user = useCurrentUser();

    if (user?.hasCompany && !user?.developer) {
        return null;
    }

    return <SidebarNavRecentProjects />;
};

export { WrappedSidebarNavRecentProjects as SidebarNavRecentProjects };
