import React, { FC, useMemo } from 'react';

import { BsArrowUpRight } from 'react-icons/bs';
import { IoCalendarOutline, IoGlobeOutline, IoInfiniteSharp, IoStarSharp } from 'react-icons/io5';
import { TbBrandYoutube, TbMessage } from 'react-icons/tb';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { SidebarNavItemProps } from 'components/sidebar-nav/components/SidebarNav.Item';
import { useRouter } from 'next/router';
import { useCurrentUser } from 'hooks/use-current-user';
import { useShowDesignEditor } from 'hooks/use-show-design-editor';

const SidebarNavResources: FC = () => {
    const { asPath, query } = useRouter();
    const user = useCurrentUser();
    const showDesignEditor = useShowDesignEditor();

    // useMemo to prevent hydration error
    const items: SidebarNavItemProps[] = useMemo(
        () => [
            {
                children: 'DCIDE Plans',
                color: 'yellow',
                icon: <IoStarSharp />,
                href: `/upgrade?redirect=${query.redirect || asPath}`,
                hidden: !user,
            },
            {
                children: 'RE+ Events',
                href: 'https://re-plus.events/events',
                icon: <IoCalendarOutline />,
                rightSection: <BsArrowUpRight />,
            },
            {
                children: 'EMerge Alliance',
                href: 'https://www.emergealliance.org',
                icon: <IoGlobeOutline />,
                rightSection: <BsArrowUpRight />,
            },
            {
                children: 'Feedback',
                icon: <TbMessage />,
                onClick:
                    //@ts-ignore
                    () => window?.usersnapLogger('custom_button_click'),
            },
            {
                children: 'Changelog',
                href: 'https://about.dcide.app/blog?category=Changelog',
                target: '_blank',
                icon: <IoInfiniteSharp />,
                rightSection: <BsArrowUpRight />,
                hidden: !showDesignEditor,
            },
            {
                children: 'Video Tutorials',
                href: 'https://www.youtube.com/@dcmicrogriddesignandengine5189/videos',
                target: '_blank',
                icon: <TbBrandYoutube />,
                rightSection: <BsArrowUpRight />,
                hidden: !showDesignEditor,
            },
        ],
        [],
    );

    return (
        <SidebarNav.Section>
            {items.map((item, index) => (
                <SidebarNav.Item key={index} {...item} />
            ))}
        </SidebarNav.Section>
    );
};

export { SidebarNavResources };
