import React, { FC } from 'react';

import { BsBox } from 'react-icons/bs';
import { IoAdd, IoShapesOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { DesignLibraryHelpers } from 'helpers/DesignLibraryHelpers';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';

import { ProjectService } from 'services/ProjectService';
import { ComponentService } from 'services/ComponentService';

const SidebarNavCompany: FC = () => {
    const user = useCurrentUser();

    const { isManufacturer = false, hasCompany = false } = user ?? {};

    return (
        <SidebarNav.Section>
            <SidebarNav.Profile />

            {user && !hasCompany && (
                <SidebarNav.Item href={ComponentHelpers.urls.create()} icon={<IoAdd />}>
                    Sign Up Your Products
                </SidebarNav.Item>
            )}

            {(isManufacturer || hasCompany) && (
                <SidebarNav.Item
                    href={ComponentHelpers.urls.manage()}
                    icon={<BsBox />}
                    rightSection={
                        <SidebarNav.CreateButton
                            tooltip="Add Product"
                            onClick={() => ComponentService.navigate.create()}
                        />
                    }
                >
                    My Company Products
                </SidebarNav.Item>
            )}

            {isManufacturer && (
                <SidebarNav.Item
                    href={DesignLibraryHelpers.urls.manage()}
                    icon={<IoShapesOutline />}
                    rightSection={
                        <SidebarNav.CreateButton
                            tooltip="Create Project"
                            onClick={() => ProjectService.navigate.create()}
                        />
                    }
                >
                    My Reference Designs
                </SidebarNav.Item>
            )}
        </SidebarNav.Section>
    );
};

export { SidebarNavCompany };
