import React, { <PERSON> } from 'react';

import { IoFolderOpenOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';
import { useRecentProjects } from 'hooks/use-recent-projects';
import { useShowDesignEditor } from 'hooks/use-show-design-editor';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { ProjectService } from 'services/ProjectService';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';

const SidebarNavDesignEditor: FC = () => {
    const user = useCurrentUser();
    const showDesignEditor = useShowDesignEditor();

    if (!user) {
        return null;
    }

    return (
        <SidebarNav.Section>
            <SidebarNav.Item
                href={ProjectHelpers.urls.overview()}
                icon={<IoFolderOpenOutline />}
                rightSection={
                    <SidebarNav.CreateButton
                        tooltip="Create project"
                        onClick={() => ProjectService.navigate.create()}
                    />
                }
                hidden={!showDesignEditor}
                color="primary"
            >
                My DCIDE Projects
            </SidebarNav.Item>

            {showDesignEditor && <RecentProjects />}
        </SidebarNav.Section>
    );
};

const RecentProjects: FC = () => {
    const recentProjects = useRecentProjects();

    if (!recentProjects?.length) return null;

    return (
        <SidebarNav.Section storageKey="sidebar.recentProjects" title="My recent projects" boxProps={{ ml: 20 }}>
            {recentProjects?.slice(0, 3).map((project) => (
                <SidebarNav.Item key={project.id} href={ProjectHelpers.urls.editor(project.id)}>
                    {project.name}
                </SidebarNav.Item>
            ))}
        </SidebarNav.Section>
    );
};

export { SidebarNavDesignEditor };
