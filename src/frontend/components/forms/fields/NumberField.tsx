import { forwardRef } from 'react';

import { NumberInput, NumberInputProps } from '@mantine/core';
import { useMergedRef } from '@mantine/hooks';
import { useController } from 'react-hook-form';
import { useReadOnlyPlaceholder } from './hooks/use-readonly-placeholder';

export type NumberFieldProps = NumberInputProps & {
    name: string;
    afterChange?: (value: number | null) => void;
};

const NumberField = forwardRef<HTMLInputElement, NumberFieldProps>(
    ({ name, afterChange, ...props }: NumberFieldProps, forwardedRef) => {
        const {
            field: { onChange, ...field },
            fieldState: { error, isTouched },
        } = useController({ name });

        const placeholderRef = useReadOnlyPlaceholder('Not specified');

        const ref = useMergedRef(forwardedRef, placeholderRef);

        return (
            <NumberInput
                {...field}
                {...props}
                onValueChange={(values) => {
                    onChange(values.floatValue ?? '');

                    if (afterChange) {
                        afterChange(values.floatValue ?? null);
                    }
                }}
                value={field.value ?? ''}
                error={isTouched && error?.message}
                ref={ref}
            />
        );
    },
);

NumberField.displayName = 'NumberField';

export { NumberField };
