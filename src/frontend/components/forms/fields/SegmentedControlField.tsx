import React from 'react';
import { useC<PERSON>roller } from 'react-hook-form';

import { SegmentedControl, SegmentedControlProps } from '@mantine/core';

export type SegmentedControlFieldProps = SegmentedControlProps & {
    name: string;
};

const SegmentedControlField = ({ name, data, onChange, ...props }: SegmentedControlFieldProps) => {
    const { field } = useController({ name });

    const handleChange = (value: string) => {
        field.onChange(value);
        onChange?.(value);
    };

    return <SegmentedControl {...field} {...props} onChange={handleChange} data={data} />;
};

export { SegmentedControlField };
