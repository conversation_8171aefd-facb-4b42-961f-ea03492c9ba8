import React from 'react';
import { useController } from 'react-hook-form';

import { Textarea, TextareaProps } from '@mantine/core';

type MultilineTextFieldProps = TextareaProps & {
    name: string;
};

const MultilineTextField = ({ name, ...props }: MultilineTextFieldProps) => {
    const {
        field,
        fieldState: { isTouched, error },
    } = useController({ name });

    return <Textarea {...field} {...props} error={isTouched && error?.message} />;
};

export { MultilineTextField };
