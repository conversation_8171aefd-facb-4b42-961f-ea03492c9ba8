import React from 'react';

import { NumberF<PERSON>, NumberFieldProps } from './NumberField';

export type RatioFieldProps = NumberFieldProps & {
    name: string;
    allowNegative?: boolean;
};

function RatioField({ name, allowNegative = false, ...props }: RatioFieldProps) {
    return (
        <NumberField
            decimalScale={2}
            step={0.01}
            name={name}
            min={allowNegative ? -1 : 0}
            max={1}
            placeholder={'Value'}
            {...props}
        />
    );
}

RatioField.displayName = 'RatioField';

export { RatioField };
