import React, { FC } from 'react';

import { Button, Select, SelectProps } from '@mantine/core';

import { useController } from 'react-hook-form';
import { useReadOnlyPlaceholder } from './hooks/use-readonly-placeholder';

export type SelectFieldProps = SelectProps & {
    name: string;
    stringify?: (value: any) => string | null;
    parse?: (value: string | null) => any;
    afterChange?: (value: string | null, option: any) => void;
    inline?: boolean;
};

const SelectField = ({
    name,
    defaultValue,
    afterChange,
    stringify = (value) => value,
    parse = (value) => value,
    inline = false,
    ...props
}: SelectFieldProps) => {
    const {
        field,
        fieldState: { isDirty, isTouched, error },
    } = useController({ name, defaultValue });

    const ref = useReadOnlyPlaceholder(props.placeholder || 'Not specified');
    const value = stringify(field.value);

    if (inline) {
        return <InlineSelectField name={name} data={props.data} afterChange={afterChange} {...props} />;
    }

    return (
        <Select
            placeholder="Select an option"
            {...field}
            {...props}
            value={value}
            onChange={(value, option) => {
                const clean = parse(value);
                field.onChange(clean, option);

                if (afterChange) {
                    afterChange(clean, option);
                }
            }}
            error={isTouched && error?.message}
            ref={ref}
            data-dirty={isDirty}
            data-touched={isTouched}
        />
    );
};

const InlineSelectField: FC<SelectFieldProps> = ({ name, data = [], afterChange }) => {
    const { field } = useController({ name });

    return (
        <Button.Group data-as-input>
            {data.map((option) => (
                <Button
                    size="xs"
                    variant="default"
                    onClick={() => {
                        // @ts-ignore
                        field.onChange(option.value);

                        if (afterChange) {
                            // @ts-ignore
                            afterChange(option.value, option);
                        }
                    }}
                    // @ts-ignore
                    data-active={option.value === field.value}
                    // @ts-ignore
                    key={option.label}
                >
                    {/* @ts-ignore */}
                    {option.label}
                </Button>
            ))}
        </Button.Group>
    );
};

export { SelectField };
