import { useController } from 'react-hook-form';

import { Checkbox, CheckboxProps } from '@mantine/core';

type CheckboxFieldProps = CheckboxProps & {
    name: string;
};

const CheckboxField = ({ name, ...props }: CheckboxFieldProps) => {
    const {
        field: { onChange, ...field },
        fieldState: { error },
    } = useController({ name });

    return (
        <Checkbox
            {...field}
            {...props}
            checked={!!field.value}
            onChange={(event) => onChange(event.currentTarget.checked)}
            error={error?.message}
        />
    );
};

export { CheckboxField };
