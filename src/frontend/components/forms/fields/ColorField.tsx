import { useController } from 'react-hook-form';

import { ColorInput, ColorInputProps, isLightColor, Stack, Text, Transition } from '@mantine/core';

type ColorFieldProps = ColorInputProps & {
    name: string;
    immediate?: boolean;
    showLightColorWarning?: boolean;
};

const ColorField = ({ name, immediate, showLightColorWarning = false, ...props }: ColorFieldProps) => {
    const {
        field: { onChange, ...field },
        fieldState: { isTouched, error },
    } = useController({ name });

    const isLight = field.value && isLightColor(field.value);

    return (
        <Stack gap={4}>
            <ColorInput
                {...field}
                format="rgba"
                swatches={SWATCHES}
                {...props}
                onChange={immediate ? onChange : undefined}
                onChangeEnd={immediate ? undefined : onChange}
                error={isTouched && error?.message}
            />
            <Transition mounted={showLightColorWarning && isLight}>
                {(styles) => (
                    <Text fz="xs" style={styles}>
                        This is a light color, we will adjust color where needed for better readability. <br />
                        Choose a different color if you want to control the color manually.
                    </Text>
                )}
            </Transition>
        </Stack>
    );
};

export const SWATCHES = [
    '#000000',
    '#868e96',
    '#fa5252',
    '#e64980',
    '#be4bdb',
    '#7950f2',
    '#4c6ef5',
    '#228be6',
    '#15aabf',
    '#12b886',
    '#40c057',
    '#82c91e',
    '#fab005',
    '#fd7e14',
];

export { ColorField };
