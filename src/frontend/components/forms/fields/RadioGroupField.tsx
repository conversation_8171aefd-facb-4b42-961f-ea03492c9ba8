import React from 'react';
import { useController } from 'react-hook-form';

import { Flex, Input, Radio, RadioGroupProps } from '@mantine/core';

import cx from './RadioGroupField.module.scss';

export type RadioGroupFieldProps = Omit<RadioGroupProps, 'children'> & {
    name: string;
    data: {
        value: any;
        label: React.ReactNode;
    }[];
    isInline?: boolean;
};

const RadioGroupField = ({ name, data, isInline, required, ...props }: RadioGroupFieldProps) => {
    const {
        field,
        fieldState: { isTouched, error },
    } = useController({ name });

    return (
        <Radio.Group {...field} {...props} error={isTouched && error?.message} classNames={cx} required={required}>
            <Flex gap={isInline ? 20 : 4} direction={isInline ? 'row' : 'column'}>
                {data.map((option) => (
                    <Radio value={option.value} label={option.label} key={option.value} required={required} />
                ))}
            </Flex>
            {error && <Input.Error mt="xs">{error.message}</Input.Error>}
        </Radio.Group>
    );
};

export { RadioGroupField };
