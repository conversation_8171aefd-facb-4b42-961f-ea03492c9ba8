import { useEffect, useRef } from 'react';

import { Grid } from '@mantine/core';
import { useFormContext, useWatch } from 'react-hook-form';

import { useFormPreSubmit } from 'components/forms/Form';

import { GeoService } from 'services/GeoService';

import { TextField } from './TextField';
import { Address } from 'models';

type AddressFieldProps = {
    name: string;
    required?: boolean;
    calculateCoordinates?: boolean;
};

const AddressField = ({ name, required, calculateCoordinates = true }: AddressFieldProps) => {
    const addressValues = useWatch({ name });
    const { coordinates: _, ...addressWithoutCoordinates } = addressValues;

    const { setValue } = useFormContext();

    const { registerPreSubmitHook } = useFormPreSubmit();

    const initialAddressRef = useRef<Address | null>(null);

    useEffect(() => {
        const hasAddress = Object.keys(addressValues || {}).length > 0;
        const hasNoInitialAddress = Object.keys(initialAddressRef.current || {}).length === 0;

        if (hasAddress && hasNoInitialAddress) {
            initialAddressRef.current = addressWithoutCoordinates;
        }
    }, [JSON.stringify(addressWithoutCoordinates)]);

    useEffect(() => {
        registerPreSubmitHook(async () => {
            const hasAddress = Object.keys(addressValues || {}).length > 0;
            const hasInitialAddress = Object.keys(initialAddressRef.current || {}).length > 0;

            if (!hasAddress || !hasInitialAddress || !calculateCoordinates) return;

            const hasChanges = Object.entries(initialAddressRef.current || {}).find(
                ([key, value]) => addressValues?.[key] !== value,
            );

            if (!hasChanges?.length) return;

            try {
                const addressString = [
                    addressValues.street,
                    addressValues.number,
                    addressValues.postalCode,
                    addressValues.city,
                    addressValues.state,
                    addressValues.country,
                ]
                    .filter(Boolean)
                    .join(', ');

                const geoData = await GeoService.geocode(addressString);

                if (geoData) {
                    setValue(`${name}.coordinates`, [geoData.coordinates.longitude, geoData.coordinates.latitude]);
                }
            } catch (error) {
                console.error('Error geocoding address:', error);
            }
        });
    }, [name, JSON.stringify(addressWithoutCoordinates), setValue, registerPreSubmitHook, calculateCoordinates]);

    return (
        <Grid gutter="xs">
            <Grid.Col span={9}>
                <TextField name={`${name}.street`} label="Street" required={required} />
            </Grid.Col>
            <Grid.Col span={3}>
                <TextField name={`${name}.number`} label="Number" required={required} />
            </Grid.Col>
            <Grid.Col span={3}>
                <TextField name={`${name}.postalCode`} label="ZIP Code" required={required} />
            </Grid.Col>
            <Grid.Col span={9}>
                <TextField name={`${name}.city`} label="City" required={required} />
            </Grid.Col>
            <Grid.Col span={6}>
                <TextField name={`${name}.state`} label="State" />
            </Grid.Col>
            <Grid.Col span={6}>
                <TextField name={`${name}.country`} label="Country" required={required} />
            </Grid.Col>
        </Grid>
    );
};

export { AddressField };
