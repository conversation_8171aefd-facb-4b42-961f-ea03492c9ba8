import { use<PERSON><PERSON>roller } from 'react-hook-form';

import { ActionIcon, TextInput, TextInputProps } from '@mantine/core';
import { IoLinkSharp } from 'react-icons/io5';

type URLFieldProps = TextInputProps & {
    name: string;
};

const URLField = ({ name, ...props }: URLFieldProps) => {
    const {
        field: { onChange, ...field },
        fieldState: { isTouched, error },
    } = useController({ name });

    return (
        <TextInput
            placeholder="https://"
            {...field}
            {...props}
            value={field.value ?? ''}
            error={isTouched && error?.message}
            onChange={(event) => {
                const value = event.target.value;

                if (value && !value.startsWith('http')) {
                    onChange(`https://${value}`);
                    return;
                }

                onChange(event.target.value);
            }}
            rightSection={
                field?.value && (
                    <ActionIcon
                        component="a"
                        href={field.value}
                        variant="transparent"
                        size={props.size}
                        target="_blank"
                    >
                        <IoLinkSharp size="60%" />
                    </ActionIcon>
                )
            }
        />
    );
};

export { URLField };
