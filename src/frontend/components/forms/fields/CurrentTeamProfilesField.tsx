import { useController } from 'react-hook-form';

import { Input, InputWrapperProps, SimpleGrid } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyButton } from 'components/company-button/CompanyButton';

type Props = InputWrapperProps & {
    name: string;
    emptyMessage?: string;
};

const CurrentTeamProfilesField = ({ name, emptyMessage, ...props }: Props) => {
    const {
        field,
        fieldState: { error },
    } = useController({ name });

    const { companies } = useCurrentTeamCompanies();

    const createUrl = CompanyProfileHelpers.urls.create({
        redirect: encodeURIComponent(`${window.location.pathname}?action=publish`),
    });

    return (
        <Input.Wrapper {...props}>
            <SimpleGrid cols={{ base: 2, sm: 6 }} spacing={8} mt={4}>
                {companies.map((profile) => (
                    <CompanyButton
                        key={profile.id}
                        company={profile}
                        selected={field.value === profile.id}
                        onClick={() => field.onChange(profile.id)}
                    />
                ))}
                <CompanyButton icon={<IoAddSharp />} label="Create new profile" href={createUrl} />
            </SimpleGrid>
            {!companies.length && emptyMessage && <Input.Error mt={5}>{emptyMessage}</Input.Error>}
            {error && <Input.Error mt={5}>{error.message}</Input.Error>}
        </Input.Wrapper>
    );
};

export { CurrentTeamProfilesField };
