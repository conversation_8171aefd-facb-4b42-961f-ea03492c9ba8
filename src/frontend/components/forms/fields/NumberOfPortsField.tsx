import { useController } from 'react-hook-form';

import { Input, InputWrapperProps, SimpleGrid, Text, UnstyledButton } from '@mantine/core';

import { Port, PortsIcon } from 'components/port-icon/PortsIcon';

type NumberOfPortsFieldProps = InputWrapperProps & {
    name: string;
    onChange?: () => void;
};

export const NumberOfPortsField = ({ name, onChange = () => {}, ...props }: NumberOfPortsFieldProps) => {
    const { field } = useController({ name });

    const portsOptions: { [key: string]: Port[] } = {
        '2': [
            { portNumber: 1, configuration: 'single-line' },
            { portNumber: 2, configuration: 'single-line' },
        ],
        '3': [
            { portNumber: 1, configuration: 'single-line' },
            { portNumber: 2, configuration: 'single-line' },
            { portNumber: 3, configuration: 'single-line' },
        ],
    };

    return (
        <Input.Wrapper {...props}>
            <SimpleGrid cols={2}>
                {Object.entries(portsOptions).map(([index, ports]) => {
                    const selected = field?.value === index;

                    return (
                        <UnstyledButton
                            onClick={() => {
                                field.onChange(index);
                                onChange();
                            }}
                            style={(theme) => ({
                                display: 'flex',
                                flexDirection: 'column',

                                aspectRatio: '1 / 1',

                                alignItems: 'center',
                                justifyContent: 'center',

                                backgroundColor: selected ? theme.colors.gray['9'] : '#ffffff',
                                color: selected ? '#ffffff' : theme.colors.gray['8'],
                                border: `1px solid ${selected ? theme.colors.gray['9'] : theme.colors.gray['3']}`,
                                borderRadius: theme.radius.sm,
                                transition: 'all 300ms ease-in-out',
                            })}
                            key={index}
                        >
                            <div style={{ width: '40%', fill: `#ffffff` }}>
                                <PortsIcon ports={ports} label={`${index}`} />
                            </div>
                            <Text fw="600" mt="sm">
                                {`${index} ports`}
                            </Text>
                        </UnstyledButton>
                    );
                })}
            </SimpleGrid>
        </Input.Wrapper>
    );
};
