import React from 'react';

import { Box, Text, Group, Combobox, InputBase, Input, useCombobox, Flex } from '@mantine/core';

import { all, Component } from 'models';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import { useController, useWatch } from 'react-hook-form';

type Props = {
    name: string;
    label?: string;
    enabledComponents?: Component['type'][];
    readOnly?: boolean;
};

const ComponentTypeSelectField = ({ name, label, enabledComponents, readOnly }: Props) => {
    const combobox = useCombobox();

    const { field } = useController({ name });
    const ports = useWatch({ name: 'ports' });

    const data = Object.values(all)
        .filter((component) => !enabledComponents || enabledComponents.includes(component.type))
        .map((component) => ({
            value: component.type,
            label: component.name,
        }));

    const selectedOption = data.find((option) => {
        return option.value === field.value;
    });

    const ComboboxInstance = (
        <Combobox
            store={combobox}
            onOptionSubmit={(value) => {
                field.onChange(value);
                combobox.closeDropdown();
            }}
            readOnly={readOnly}
        >
            <Combobox.Target>
                <InputBase
                    name={name}
                    value={selectedOption?.label || ''}
                    rightSection={!readOnly && <Combobox.Chevron />}
                    rightSectionPointerEvents="none"
                    onClick={
                        !readOnly
                            ? () => {
                                  combobox.openDropdown();
                              }
                            : undefined
                    }
                    onFocus={
                        !readOnly
                            ? () => {
                                  combobox.openDropdown();
                              }
                            : undefined
                    }
                    onBlur={() => {
                        combobox.closeDropdown();
                        field.onBlur();
                    }}
                    placeholder="Select a component type"
                />
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options>
                    {data.map((option) => (
                        <Combobox.Option value={option.value} key={option.value}>
                            <Group wrap="nowrap">
                                <Box style={{ width: '24px', fill: 'currentColor' }}>
                                    <ComponentIcon type={option.value} />
                                </Box>
                                <Text size="sm">{option.label}</Text>
                            </Group>
                        </Combobox.Option>
                    ))}
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );

    return (
        <Input.Wrapper label={label}>
            <Flex align="center" justify="space-around">
                {ComboboxInstance}
                <Box style={{ width: 36, height: 36 }}>
                    <ComponentIcon type={field.value} ports={ports} />
                </Box>
            </Flex>
        </Input.Wrapper>
    );
};

export { ComponentTypeSelectField };
