import { useController } from 'react-hook-form';

import { InputWrapper, InputWrapperProps, Slider, SliderProps } from '@mantine/core';

type SliderFieldProps = InputWrapperProps & {
    name: string;
    sliderProps?: Omit<SliderProps, 'value' | 'onChange' | 'name'>;
};

const SliderField = ({ name, sliderProps, ...props }: SliderFieldProps) => {
    const {
        field: { value, ...fieldProps },
        fieldState: { error },
    } = useController({
        name,
    });

    return (
        <InputWrapper {...props} error={error?.message}>
            <Slider {...fieldProps} mt={8} mb="md" value={value} color="primary" {...sliderProps} />
        </InputWrapper>
    );
};

export { SliderField };
