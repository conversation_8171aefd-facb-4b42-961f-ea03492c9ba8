import { use<PERSON><PERSON>roller } from 'react-hook-form';

import { TextInput, TextInputProps } from '@mantine/core';

type TextFieldProps = TextInputProps & {
    name: string;
    transform?: (value: string) => string;
};

const defaultTransform = (value: string) => value;

const TextField = ({ name, transform = defaultTransform, ...props }: TextFieldProps) => {
    const {
        field: { onChange, ...field },
        fieldState: { error },
    } = useController({ name });

    return (
        <TextInput
            {...field}
            value={field.value ?? ''}
            styles={{ input: { lineHeight: '1em' } }}
            error={error?.message}
            {...props}
            onChange={(event) => {
                onChange(transform(event.target.value));
            }}
        />
    );
};

export { TextField };
