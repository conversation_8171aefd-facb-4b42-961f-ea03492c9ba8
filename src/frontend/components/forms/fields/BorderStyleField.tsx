import { Select, SelectProps } from '@mantine/core';
import { useController } from 'react-hook-form';

type BorderStyleFieldProps = SelectProps & {
    name: string;
};

const BorderStyleField = ({ name, ...props }: BorderStyleFieldProps) => {
    const {
        field: { ...field },
        fieldState: { isTouched, error },
    } = useController({ name });

    return <Select {...field} data={BORDER_STYLES} {...props} error={isTouched && error?.message} />;
};

export const BORDER_STYLES = [
    {
        label: 'Solid',
        value: '1 0',
        borderStyle: 'solid',
    },
    {
        label: 'Dotted',
        value: '2 2',
        borderStyle: 'dotted',
    },
    {
        label: 'Dashed',
        value: '8 2',
        borderStyle: 'dashed',
    },
];

export { BorderStyleField };
