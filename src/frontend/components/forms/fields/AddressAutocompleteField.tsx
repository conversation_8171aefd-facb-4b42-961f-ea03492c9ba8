import { useMemo, useState } from 'react';

import { use<PERSON><PERSON>roller } from 'react-hook-form';

import { Checkbox, InputWrapper, Stack } from '@mantine/core';
import { AddressField } from 'components/forms/fields/AddressField';

import { Address } from 'models';

import { Geo, GeocodingFeature } from 'services/GeoService';

import dynamic from 'next/dynamic';

import cx from './AddressAutocompleteField.module.scss';

const MapboxInput = dynamic(() => import('../MapboxInput.lazy'), {
    ssr: false,
});

type AddressAutocompleteFieldProps = {
    name: string;
    label?: string;
};

const AddressAutocompleteField = ({ name, label }: AddressAutocompleteFieldProps) => {
    const {
        field: { onChange, value },
        fieldState: { error },
    } = useController({ name });

    const [inputValue, setInputValue] = useState('');

    const [isManual, setIsManual] = useState(false);

    const [suggestions, setSuggestions] = useState<GeocodingFeature[] | null>(null);

    const addressString = useMemo(() => {
        if (isManual) {
            return '';
        }

        if (!value) {
            return '';
        }

        const _addressString = [value.street, value.number, value.postalCode, value.city, value.state, value.country]
            .filter(Boolean)
            .join(', ');

        setInputValue(_addressString);

        return _addressString;
    }, [value, isManual]);

    const setFieldValues = (geoValues?: Geo) => {
        if (!geoValues) {
            return;
        }

        const {
            full_address,
            context: { country, street, postcode, address, place, region },
            coordinates,
        } = geoValues;

        const addressData: Address = {
            name: full_address,
            // @ts-ignore it exists
            street: address?.street_name ?? street?.name ?? '',
            // @ts-ignore it exists
            number: address?.address_number ?? '',
            postalCode: postcode?.name ?? '',
            city: place?.name ?? '',
            state: region?.name ?? '',
            country: country?.name ?? '',
            coordinates: [coordinates.longitude, coordinates.latitude],
        };

        onChange(addressData);
    };

    const handleBlur = async () => {
        if (suggestions?.length) {
            setFieldValues(suggestions[0].properties);

            resetState();
        }
    };

    const resetState = () => {
        setInputValue('');
        setSuggestions(null);
    };

    return (
        <InputWrapper label={label} error={error?.message} onBlur={handleBlur}>
            <Stack gap="xs" data-disable-mapbox={isManual} className={cx.root}>
                <MapboxInput
                    value={inputValue || addressString}
                    onChange={setInputValue}
                    onRetrieve={(feature) => {
                        resetState();
                        setFieldValues(feature.properties);
                    }}
                    placeholder="Search for an address"
                    onSuggest={(res) => setSuggestions(res.features)}
                />
                <Checkbox
                    label="Fill in manually"
                    checked={isManual}
                    onChange={(event) => {
                        if (event.currentTarget.checked) {
                            resetState();
                        }

                        setIsManual(event.currentTarget.checked);
                    }}
                />
                {isManual && <AddressField name={name} />}
            </Stack>
        </InputWrapper>
    );
};

export { AddressAutocompleteField };
