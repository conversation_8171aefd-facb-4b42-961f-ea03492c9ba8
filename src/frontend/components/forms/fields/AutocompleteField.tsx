import { useController } from 'react-hook-form';

import { Autocomplete, AutocompleteProps } from '@mantine/core';
import { useReadOnlyPlaceholder } from './hooks/use-readonly-placeholder';

export type AutocompleteFieldProps = AutocompleteProps & {
    name: string;
    component?: string;
};

const AutocompleteField = ({ name, defaultValue, ...props }: AutocompleteFieldProps) => {
    const {
        field,
        fieldState: { isTouched, error },
    } = useController({ name, defaultValue });

    const ref = useReadOnlyPlaceholder('Not specified');

    return <Autocomplete {...field} {...props} error={isTouched && error?.message} ref={ref} />;
};

export { AutocompleteField };
