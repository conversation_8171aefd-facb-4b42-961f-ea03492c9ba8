import React from 'react';

import { NumberField, NumberFieldProps } from './NumberField';

type IntegerFieldProps = Omit<NumberFieldProps, 'decimalScale'>;

const IntegerField = ({ name, ...props }: IntegerFieldProps) => {
    return (
        <NumberField
            name={name}
            decimalScale={0}
            {...props}
            onKeyDown={props.min !== undefined && props.min > 0 ? preventEnterJustZero : undefined}
        />
    );
};

const preventEnterJustZero = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === '0' && event.currentTarget.value === '') {
        event.preventDefault();
    }
};

IntegerField.displayName = 'IntegerField';

export { IntegerField };
