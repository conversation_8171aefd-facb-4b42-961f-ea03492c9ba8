import { useState } from 'react';

import { useController } from 'react-hook-form';

import { useMergedRef } from '@mantine/hooks';
import {
    TextInput,
    Button,
    Group,
    Flex,
    Text,
    ScrollArea,
    Input,
    Combobox,
    useCombobox,
    InputWrapperProps,
} from '@mantine/core';
import { IoChevronDown } from 'react-icons/io5';

import { defaultCountries, FlagImage, usePhoneInput } from 'react-international-phone';

import cx from './PhoneField.module.scss';

type ManufacturerInputProps = Omit<InputWrapperProps, 'data'> & {
    name: string;
};

const mappedCountries = defaultCountries.map(([name, iso2, dialCode]) => ({ name, iso2, dialCode }));

const PhoneField = ({ name, ...props }: ManufacturerInputProps) => {
    const combobox = useCombobox({
        onDropdownClose: () => {
            combobox.focusTarget();
            setSearch('');
        },

        onDropdownOpen: () => {
            combobox.focusSearchInput();
        },
    });
    const [search, setSearch] = useState('');

    const {
        field: { onChange, value, ref: formRef, ...field },
        fieldState: { error },
    } = useController({ name });

    const {
        inputValue,
        handlePhoneValueChange,
        inputRef: phoneRef,
        country,
        setCountry,
    } = usePhoneInput({
        value,
        onChange: ({ phone }) => {
            onChange(phone);
        },
    });

    const ref = useMergedRef(phoneRef, formRef);

    const options = mappedCountries.filter((option) => option.name.toLowerCase().includes(search.toLowerCase().trim()));

    return (
        <Input.Wrapper {...props}>
            <Combobox store={combobox} onOptionSubmit={() => combobox.closeDropdown()}>
                <Combobox.Target>
                    <TextInput
                        ref={ref}
                        mt={2}
                        leftSectionWidth={48}
                        leftSection={
                            <Button
                                px={8}
                                variant="transparent"
                                onClick={() => {
                                    combobox.toggleDropdown();
                                }}
                            >
                                <Group gap={4}>
                                    <FlagImage iso2={country.iso2} size={16} />
                                    <IoChevronDown size={10} />
                                </Group>
                            </Button>
                        }
                        {...field}
                        value={inputValue}
                        onChange={handlePhoneValueChange}
                        error={error?.message}
                    />
                </Combobox.Target>
                <Combobox.Dropdown>
                    <Combobox.Options>
                        <ScrollArea.Autosize type="always" mah={200}>
                            <Combobox.Search
                                value={search}
                                onChange={(event) => setSearch(event.currentTarget.value)}
                                placeholder="Search country"
                                className={cx.search}
                            />
                            {options.map((option) => (
                                <Combobox.Option
                                    key={option.iso2}
                                    value={option.iso2}
                                    className={cx.option}
                                    selected={option.iso2 === country.iso2}
                                    onClick={() => setCountry(option.iso2)}
                                >
                                    <Flex gap={8}>
                                        <FlagImage iso2={option.iso2} size={20} />
                                        <Text size="sm" style={{ flex: 1 }}>
                                            {option.name}
                                        </Text>
                                        <Text size="sm" c="dimmed">
                                            +{option.dialCode}
                                        </Text>
                                    </Flex>
                                </Combobox.Option>
                            ))}

                            {!options.length && <Combobox.Empty>Nothing found</Combobox.Empty>}
                        </ScrollArea.Autosize>
                    </Combobox.Options>
                </Combobox.Dropdown>
            </Combobox>
        </Input.Wrapper>
    );
};

export { PhoneField };
