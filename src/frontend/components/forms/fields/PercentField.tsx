import {
    MeasurementInputWrapperProps,
    MeasurementInputWrapper,
    Unit as UnitElement,
    MeasurementInput,
} from 'components/forms/fields/measurement';
import React from 'react';

import { useController } from 'react-hook-form';

type PercentFieldProps = { name: string } & MeasurementInputWrapperProps;

const PercentField = ({ name, ...wrapperProps }: PercentFieldProps) => {
    const {
        field: { value, onChange },
    } = useController({ name });

    const Unit = <UnitElement>%</UnitElement>;

    return (
        <MeasurementInputWrapper {...wrapperProps}>
            <MeasurementInput
                key="nom"
                min={0}
                max={100}
                value={value}
                onChange={(value) => {
                    onChange(value ? boundValueToPercentRange(parseFloat(value)) : null);
                }}
                unit={Unit}
            />
        </MeasurementInputWrapper>
    );
};

const boundValueToPercentRange = (value: number) => Math.max(0, Math.min(value, 100));

export { PercentField };
