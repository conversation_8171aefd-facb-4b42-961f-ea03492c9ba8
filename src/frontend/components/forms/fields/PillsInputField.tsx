import { use<PERSON><PERSON>roller } from 'react-hook-form';

import { Pill, PillsInput, PillsInputProps } from '@mantine/core';
import { useState } from 'react';

type PillFieldProps = PillsInputProps & {
    name: string;
    inputPlaceholder?: string;
    validate?: (inputText: string) => true | string;
};

const PillsInputField = ({ name, inputPlaceholder, validate, ...props }: PillFieldProps) => {
    const {
        field: { onChange, value: _values, ...field },
    } = useController({ name });

    const values: string[] = _values ?? [];

    const [inputText, setInputText] = useState('');
    const [localError, setLocalError] = useState<string | null>('');

    const onAdd = (inputText: string) => {
        if (values.includes(inputText)) {
            setLocalError(`${inputText} already added`);
            return;
        }

        if (validate) {
            const errorStringOrTrue = validate(inputText);

            if (typeof errorStringOrTrue === 'string') {
                setLocalError(errorStringOrTrue);
                return;
            }
        }

        onChange([...values, inputText]);

        setInputText('');
    };

    return (
        <PillsInput {...props} {...field} error={localError}>
            <Pill.Group gap={4}>
                {values.map((value) => (
                    <Pill
                        key={value}
                        withRemoveButton
                        onRemove={() => {
                            onChange(values.filter((valueInState) => valueInState !== value));
                        }}
                    >
                        {value}
                    </Pill>
                ))}

                <PillsInput.Field
                    value={inputText}
                    onChange={(event) => {
                        setLocalError(null);
                        setInputText(event.target.value);
                    }}
                    placeholder={inputPlaceholder}
                    onKeyDown={(event) => {
                        const key = event.key;

                        if (inputText && (key === 'Enter' || key === 'Tab')) {
                            event.preventDefault();

                            onAdd(inputText);
                        } else if (!inputText && key === 'Backspace') {
                            onChange(values.slice(0, -1));
                        }
                    }}
                />
            </Pill.Group>
        </PillsInput>
    );
};

export { PillsInputField };
