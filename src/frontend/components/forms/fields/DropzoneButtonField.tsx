import { useController } from 'react-hook-form';
import { DropzoneButton, DropzoneButtonProps } from 'components/dropzone-button/DropzoneButton';
import { IoCheckmarkCircle, IoCloudUploadOutline } from 'react-icons/io5';

type DropzoneFieldProps = DropzoneButtonProps & {
    name: string;
};

const DropzoneButtonField = ({ name, label, ...rest }: DropzoneFieldProps) => {
    const {
        field: { value, onChange },
        fieldState: { error },
    } = useController({ name });

    return (
        <DropzoneButton
            {...rest}
            onChange={onChange}
            error={error?.message}
            label={value ? 'File selected' : label}
            buttonProps={{
                leftSection: value ? <IoCheckmarkCircle /> : <IoCloudUploadOutline />,
            }}
        />
    );
};

export { DropzoneButtonField };
