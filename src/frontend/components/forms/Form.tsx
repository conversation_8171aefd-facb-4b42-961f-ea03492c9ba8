import React, {
    BaseSyntheticEvent,
    ReactNode,
    createContext,
    useCallback,
    useContext,
    useEffect,
    useRef,
    useState,
} from 'react';

import { FieldValues, FormProvider, UseFormProps, useForm, UseFormReturn } from 'react-hook-form';
import { ZodSchema } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { get, keys, shake, uid } from 'radash';

type SetSubmitError = (message: string) => void;

type FormOnSubmit<FV extends FieldValues> = (
    data: FV,
    formState: UseFormReturn<FV> & {
        setSubmitError: SetSubmitError;
        getUpdates: () => Record<string, unknown>;
        hasUpdates: boolean;
    },
) => Promise<void> | void;

type FormProps<FV extends FieldValues> = {
    onSubmit: FormOnSubmit<FV>;
    zodSchema?: ZodSchema;
    children: ReactNode;
    formStyle?: React.CSSProperties;
    disableSyncDefaultValues?: boolean;
} & Omit<UseFormProps<FV>, 'resolver'>;

const SubmitFormContext = createContext<(event?: BaseSyntheticEvent) => Promise<void>>(async () => {});

export const FormContext = createContext({
    id: '',
    isSubmitted: false,
});

type PreSubmitContextType = {
    registerPreSubmitHook: (fn: () => void) => void;
};

const PreSubmitContext = createContext<PreSubmitContextType>({
    // biome-ignore lint/suspicious/noEmptyBlockStatements: it's a placeholder
    registerPreSubmitHook: () => {},
});

export const useFormPreSubmit = () => useContext(PreSubmitContext);

const Form = <FV extends FieldValues>({
    onSubmit,
    children,
    zodSchema,
    formStyle,
    defaultValues,
    disableSyncDefaultValues,
    ...props
}: FormProps<FV>) => {
    // Give all our forms an unique ID, this helps submitting in nested forms
    const [id, setId] = useState<string | null>(null);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const preSubmitHooksRef = useRef<(() => void)[]>([]);

    const registerPreSubmitHook = useCallback((fn: () => void) => {
        preSubmitHooksRef.current.push(fn);
    }, []);

    const form = useForm({
        ...props,
        resolver: zodSchema ? zodResolver(zodSchema) : undefined,
        defaultValues,
        // The values prop will react to changes and update the form values, which is useful when your form needs to be updated by external state or server data.
        values: defaultValues as FV,
        resetOptions: {
            // False by default: when `values` changes, default values (internally cached) will be overwritten
            keepDefaultValues: disableSyncDefaultValues ?? false,
        },
    });

    const { errors } = form.formState;

    if (Object.keys(errors).length) {
        console.log('Form Validation Errors', form.formState.errors);
    }

    useEffect(() => {
        // only set ID client side
        setId(uid(4));
    }, []);

    const { handleSubmit, setError } = form;
    const setSubmitError = useSetSubmitError(setError);

    const submitWithFormState = useCallback(
        async (event?: BaseSyntheticEvent) => {
            if (event?.target && event.target.id !== id) {
                return;
            }

            await handleSubmit(async () => {
                await Promise.all((preSubmitHooksRef.current || []).map((fn) => fn()));

                // get the form values after pre-submit hooks have run
                const data = form.getValues();

                const getUpdates = () => {
                    const updatesAsObject = shake(form.formState.dirtyFields, (a) => !a);
                    const updatedKeys = keys(updatesAsObject);

                    const updates = Object.fromEntries(updatedKeys.map((key) => [key, get(data, key)]));

                    return updates;
                };

                const hasUpdates = Object.keys(form.formState.dirtyFields).length > 0;

                await onSubmit(data, { ...form, setSubmitError, getUpdates, hasUpdates });
            })(event);

            setIsSubmitted(true);
        },
        [id, form, setSubmitError, handleSubmit, onSubmit, setIsSubmitted],
    );

    return id ? (
        <FormProvider {...form}>
            <FormContext.Provider value={{ id, isSubmitted }}>
                <SubmitFormContext.Provider value={submitWithFormState}>
                    <PreSubmitContext.Provider value={{ registerPreSubmitHook }}>
                        <form onSubmit={submitWithFormState} style={formStyle} {...props} id={id}>
                            {children}
                        </form>
                    </PreSubmitContext.Provider>
                </SubmitFormContext.Provider>
            </FormContext.Provider>
        </FormProvider>
    ) : null;
};

const useSetSubmitError =
    <FV extends FieldValues>(setError: UseFormReturn<FV>['setError']): SetSubmitError =>
    (message) =>
        setError('root.submitError', { message });

const useSubmitForm = () => useContext(SubmitFormContext);

export { Form, useSubmitForm };
export type { FormOnSubmit };
