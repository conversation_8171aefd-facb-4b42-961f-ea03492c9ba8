import React, { FC } from 'react';

import Link from 'next/link';

import { ActionIcon, Anchor, Box, Group, Stack, Text } from '@mantine/core';
import { IoBagHandleOutline, IoGlobeOutline, IoLinkOutline } from 'react-icons/io5';

import { CompanyProfile, PublishedStatus } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useProfilesByIds } from 'hooks/use-profiles-by-ids';
import { useComponentContext } from '../../hooks/use-component-context';

interface GeneralItemProps {
    key?: string;
    icon: React.ReactNode;
    label?: string;
    value?: React.ReactNode;
}

const GeneralView = () => {
    const { component } = useComponentContext();

    const { profiles: distributors } = useProfilesByIds(
        component.distributorsDetails?.map(({ distributor }) => distributor) ?? [],
    );

    const generalItems: GeneralItemProps[] = [
        {
            key: 'website',
            icon: <IoLinkOutline />,
            value: component.website ? (
                <Anchor href={component.website} target="_blank" fw={500}>
                    Visit the website
                </Anchor>
            ) : null,
        },
        {
            key: 'regionAvailability',
            icon: <IoGlobeOutline />,
            label: 'Region Availability',
            value: component.regionAvailability.map((region) => region).join(', '),
        },
    ];

    return (
        <Stack gap={8}>
            {generalItems.map((item) => (
                <GeneralItem key={item.key} icon={item.icon} label={item.label} value={item.value} />
            ))}

            {!!distributors?.length && (
                <GeneralItem
                    icon={<IoBagHandleOutline />}
                    label="Distributed by"
                    value={distributors?.map((distributor, index) => {
                        return (
                            <>
                                <GeneralDistributorLink key={distributor.id} distributor={distributor} />
                                {index < component.distributorsDetails.length - 1 && ', '}
                            </>
                        );
                    })}
                />
            )}
        </Stack>
    );
};

const GeneralItem = ({ icon, label, value }: GeneralItemProps) => {
    if (!value) return null;

    return (
        <Group gap={4} align="center">
            <ActionIcon component="span" radius={99} variant="light" color="primary">
                {icon}
            </ActionIcon>
            {label && (
                <Text ml={2} fw={500}>
                    {label}:
                </Text>
            )}
            <Box>{value}</Box>
        </Group>
    );
};

const GeneralDistributorLink: FC<{ distributor: CompanyProfile }> = ({ distributor }) => {
    if (distributor.status === PublishedStatus.PUBLISHED) {
        return (
            <Anchor inherit component={Link} href={CompanyProfileHelpers.urls.view(distributor.slug)}>
                {distributor.name}
            </Anchor>
        );
    }

    return (
        <Text inherit span>
            {distributor.name}
        </Text>
    );
};

export { GeneralView };
