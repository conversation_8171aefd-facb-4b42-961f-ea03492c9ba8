import React, { FC } from 'react';

import { Box, Divider, Overlay, Stack } from '@mantine/core';

import { Component } from 'models';
import { useCurrentUser } from 'hooks/use-current-user';

import { HEADER_HEIGHT } from 'components/diagram';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';

import { ComponentDatasheetHeader } from './ComponentDatasheetHeader';

import {
    Communication,
    Electrical,
    Environmental,
    General,
    Files,
    Mechanical,
    Performance,
    Standards,
    Images,
    ReferenceDesigns,
    CompatibleWith,
    Meta,
    Questions,
} from './sections';
import { LoginWall } from 'components/component-datasheet/components/sections/LoginWall';

type ComponentDatasheetProps = {
    component?: Component;
    initialMode?: DatasheetMode;
    scrollable?: boolean;
    showHeader?: boolean;
};

const ComponentDatasheet: FC<ComponentDatasheetProps> = ({ component, showHeader }) => {
    const mode = useDatasheetMode();
    const user = useCurrentUser();

    const Sections = (
        <>
            <Stack gap="xl">
                <CompatibleWith />
                <ReferenceDesigns />
            </Stack>

            <General />
            <Electrical />
            <Performance />
            <Communication />
            <Standards />
            <Mechanical />
            <Environmental />
            {mode !== DatasheetMode.VIEW && <Images />}
            <Files />
            {component && <Questions component={component} />}
        </>
    );

    return (
        <Stack gap="xl" w="100%">
            {showHeader && <ComponentDatasheetHeader />}
            <Meta />

            <Divider label="Datasheet" />

            {user ? (
                Sections
            ) : (
                <Box pos="relative" p="lg" style={{ borderRadius: 'var(--mantine-radius-sm)' }}>
                    <Overlay
                        blur={1.5}
                        p="lg"
                        backgroundOpacity={0.1}
                        style={{ borderRadius: 'var(--mantine-radius-sm)' }}
                        zIndex={99}
                    >
                        <LoginWall component={component} pos="sticky" top={HEADER_HEIGHT * 2} />
                    </Overlay>

                    <Stack gap="xl">{Sections}</Stack>
                </Box>
            )}
        </Stack>
    );
};

const WrappedComponentDatasheet: FC<ComponentDatasheetProps> = ({
    initialMode = DatasheetMode.VIEW,
    scrollable,
    component,
    ...rest
}) => (
    <Stack gap="xl" w="100%" bg="white" p="lg" style={{ borderRadius: 'var(--mantine-radius-sm)' }}>
        <Datasheet initialMode={initialMode} scrollable={scrollable}>
            <ComponentDatasheet initialMode={initialMode} component={component} {...rest} />
        </Datasheet>
    </Stack>
);

export { WrappedComponentDatasheet as ComponentDatasheet };
