import React, { FC, useState } from 'react';
import { Component } from 'models';

import { Button, Tooltip } from '@mantine/core';
import type { ButtonProps } from '@mantine/core';

import { InternalTrackingService } from 'services/InternalTrackingService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { CopyPasteService } from 'components/diagram/services/CopyPasteService';

const CopyComponentButton: FC<
    ButtonProps & {
        component: Component;
    }
> = ({ component, ...props }) => {
    const [copied, setCopied] = useState(false);

    const handle = () => {
        InternalTrackingService.track('product.copy');

        const componentInstance = ComponentInstanceService.initialize(component.type, component.id, {
            // @ts-ignore .. this works
            electrical: component.electrical,
        });

        CopyPasteService.saveToClipboard({
            type: 'dcide-copy-paste',
            componentInstances: [componentInstance],
            connections: [],
        }).then();

        LocalNotificationService.showInfo({
            title: 'Component copied',
            message: 'Paste it in a diagram to start using it',
        });

        setCopied(true);

        setTimeout(() => {
            setCopied(false);
        }, 2000);
    };

    return (
        <Tooltip label="Copy this component and paste it in an existing diagram">
            <Button onClick={handle} {...props}>
                {copied ? 'Copied' : 'Copy component'}
            </Button>
        </Tooltip>
    );
};

export { CopyComponentButton };
