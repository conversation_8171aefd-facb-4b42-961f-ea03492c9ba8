import React from 'react';

import Link from 'next/link';

import { Anchor, Button, SimpleGrid, Stack, Text } from '@mantine/core';
import { IoAddSharp, IoEyeOutline } from 'react-icons/io5';
import { BsPencil } from 'react-icons/bs';

import { PublishedStatus } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SignupLayout } from 'components/signup-layout/SignupLayout';
import { CompanySignupProps } from 'components/company-signup/CompanySignup';
import { FinishAction } from 'components/signup-layout/components/FinishAction';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { ProjectHelpers } from 'helpers/ProjectHelpers';

type Props = Omit<CompanySignupProps, 'progress' | 'active'>;

const Finish = ({ company }: Props) => {
    const companyUrl = company && CompanyProfileHelpers.urls.view(company.slug);

    const createRoute = ComponentHelpers.urls.create({ company });
    const projectCreateRoute = ProjectHelpers.urls.create({
        isReferenceDesign: true,
        manufacturer: company?.id,
        name: '[reference design]',
    });

    let introText = (
        <>
            <SignupLayout.Title>Thank you for your signup!</SignupLayout.Title>
            <Text>We&apos;re excited to have you on board. What&apos;s next?</Text>
        </>
    );

    if (company?.status === PublishedStatus.PUBLISHED) {
        introText = (
            <>
                <SignupLayout.Title>Your profile has been updated</SignupLayout.Title>
                <Text>What&apos;s next?</Text>
            </>
        );
    }

    return (
        <>
            <SignupLayout.Content>
                <Stack maw={600}>
                    {introText}

                    <SimpleGrid cols={4} spacing={8}>
                        {createRoute && (
                            <FinishAction isFocus href={createRoute} icon={<IoAddSharp />}>
                                Add a product
                            </FinishAction>
                        )}

                        <FinishAction href={projectCreateRoute} icon={<BsPencil />}>
                            Create a reference design
                        </FinishAction>
                        {companyUrl && (
                            <FinishAction href={companyUrl} icon={<IoEyeOutline />}>
                                Visit your profile
                            </FinishAction>
                        )}
                    </SimpleGrid>
                    <Text>
                        If you have any questions, please don&apos;t hesitate to contact us at{' '}
                        <Anchor href="mailto:<EMAIL>"><EMAIL></Anchor>.
                    </Text>
                </Stack>
            </SignupLayout.Content>
            <SignupLayout.Actions
                rightActions={
                    <>
                        {createRoute && (
                            <Button component={Link} href={createRoute} leftSection={<IoAddSharp />}>
                                Add a product
                            </Button>
                        )}
                        <Button variant="outline" component={Link} href={projectCreateRoute} leftSection={<BsPencil />}>
                            Create a reference design
                        </Button>
                        {companyUrl && (
                            <Button variant="outline" component={Link} href={companyUrl} leftSection={<IoEyeOutline />}>
                                Visit your profile
                            </Button>
                        )}
                    </>
                }
            />
        </>
    );
};

export { Finish };
