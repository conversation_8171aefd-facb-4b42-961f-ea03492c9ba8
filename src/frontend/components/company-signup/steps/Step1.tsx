import React, { useEffect, useState } from 'react';

import { assign, pick } from 'radash';
import { Alert, Button, SimpleGrid, Stack, Text } from '@mantine/core';

import { IoArrowBack, IoArrowForward } from 'react-icons/io5';

import {
    CompanyProfile,
    CompanyProfileSchema,
    CompanyService,
    Compliance,
    DEFAULT_POWER_LEVEL,
    UserType,
} from 'models';

import { useRTE } from 'hooks/use-rte';
import { useRouterQuery } from 'hooks/use-router-query';
import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { useCompanyApplicationTags } from 'hooks/use-company-application-tags';

import { addUniqueNameValidator } from 'helpers/add-unique-name-validator';

import { RouterService } from 'services/RouterService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SignupLayout } from 'components/signup-layout/SignupLayout';
import { CompanySignupProps } from 'components/company-signup/CompanySignup';
import { TeamAndUsersButton } from 'components/team-users/TeamAndUsersButton';

import { FormSubmit } from 'components/forms/FormSubmit';
import { CompanyForm } from 'components/company-form/CompanyForm';
import { CompanyFormServiceTags } from 'components/company-form/CompanyFormServiceTags';

import { URLField } from 'components/forms/fields/URLField';
import { TagsField } from 'components/forms/fields/TagsField';
import { ImageField } from 'components/forms/fields/ImageField';
import { CompliancesField } from 'components/forms/fields/CompliancesField';
import { PowerLevelField } from 'components/component-fields/PowerLevelField';
import { ProjectBudgetField } from 'components/component-fields/ProjectBudgetField';
import { CompanyFormNameField } from 'components/company-form/CompanyFormNameField';
import { ProfileServicesField } from 'components/forms/fields/ProfileServicesFields';
import { AddressAutocompleteField } from 'components/forms/fields/AddressAutocompleteField';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { useDefaultValues } from 'components/company-signup/hooks/use-default-values';

type Props = Omit<CompanySignupProps, 'progress' | 'active'>;

const Step1Schema = CompanyProfileSchema.pick({
    name: true,
    website: true,
    socials: true,
    about: true,
    locations: true,
    logos: true,
    cover: true,
    services: true,
    serviceTags: true,
    applicationTags: true,
    systemSize: true,
    projectBudget: true,
    compliance: true,
    partners: true,
});

const Step1 = ({ company: incomingCompany, nextStep, prevStep }: Props) => {
    const user = useCurrentUser();
    const currentTeam = useCurrentTeam();
    const { mutate: mutateCompanies } = useCurrentTeamCompanies();

    const query = useRouterQuery<{
        name: string;
        redirect?: string;
        partner?: string;
    }>();

    const [created, setCreated] = useState(false);

    const { applicationTags } = useCompanyApplicationTags();
    const { companies: existingCompanies } = useCompanyProfiles();
    const { company } = useCompanyProfile(incomingCompany ?? null);

    const { signup: { systemSize: userSystemSize } = {} } = user ?? {};
    const hasUserSystemSize = userSystemSize && userSystemSize.min && userSystemSize.max;

    const { editor, RTEField } = useRTE(company?.about);

    const zodSchema = addUniqueNameValidator(
        Step1Schema,
        existingCompanies
            .filter((existingCompany) => existingCompany.id !== company?.id)
            .map((existingCompany) => existingCompany.name),
        ' ',
    );

    const initialValues: Partial<CompanyProfile> = {
        name: (query.name as string) ?? user?.signup?.company ?? '',
        services: user?.type === UserType.MANUFACTURER ? [CompanyService.MANUFACTURING] : [],
        systemSize: hasUserSystemSize ? userSystemSize : DEFAULT_POWER_LEVEL,
    };

    if (query.partner) {
        initialValues.partners = [{ company: query.partner }];
    }

    const defaultValues = useDefaultValues(
        assign(
            initialValues,
            company
                ? {
                      ...pick(company, [
                          'name',
                          'website',
                          'socials',
                          'locations',
                          'color',
                          'services',
                          'serviceTags',
                          'systemSize',
                          'projectBudget',
                          'compliance',
                          'applicationTags',
                      ]),
                      logos: {
                          small:
                              typeof company.logos.small === 'string' ? company.logos.small : company.logos.small?.id,
                      },
                      cover: typeof company.cover === 'string' ? company.cover : company.cover?.id,
                  }
                : {},
        ),
        editor,
    );

    useEffect(() => {
        if (defaultValues.about && editor) {
            console.log('setting it!');
            editor.commands.setContent(defaultValues.about);
        }
    }, [defaultValues.about, editor]);

    return (
        <CompanyForm
            syncDefaultValues
            companyId={incomingCompany?.id ?? null}
            defaultValues={defaultValues}
            zodSchema={zodSchema}
            aboutEditor={editor}
            onSubmitSuccess={async (createdCompany) => {
                if (incomingCompany?.id || company?.id) {
                    nextStep();
                    return;
                }

                // refresh user companies
                mutateCompanies();

                setCreated(true);

                if (query.redirect) {
                    await RouterService.replace(decodeURIComponent(query.redirect as string), undefined, {
                        shallow: false,
                    });

                    return;
                }

                // redirect
                const url = CompanyProfileHelpers.urls.create({ id: createdCompany.id, step: 3 });

                await RouterService.replace(url, undefined, {
                    shallow: false,
                });
            }}
        >
            <SignupLayout.Content maw={700}>
                <Stack gap="lg">
                    <Text>
                        The sign up process will take a few minutes. You can always come back and finish your profile
                        later. Also your team members will be able to help you with this.
                    </Text>

                    {currentTeam && currentTeam.users.length > 1 && (
                        <Alert>
                            <TeamAndUsersButton
                                team={currentTeam}
                                label="Beware that your current team members will be able to view and edit this profile."
                            />
                        </Alert>
                    )}

                    <CompanyFormNameField
                        savedNameForThisCompany={company?.name}
                        companyIsPublished={Boolean(company?.publishedAt)}
                    />

                    <Stack gap={0}>
                        <ProfileServicesField
                            name="services"
                            label="Type of services your company provides"
                            excludeServices={[CompanyService.IN_APP_SUPPORT]}
                            description="Check all that apply. Multiple options are allowed."
                        />

                        <CompanyFormServiceTags />
                    </Stack>

                    <PowerLevelField
                        name="systemSize"
                        label="System size"
                        description="What power levels are you comfortable working with?"
                    />

                    <ProjectBudgetField
                        name="projectBudget"
                        label="Project Budget"
                        description="What size of projects do you typically work on?"
                    />

                    <TagsField
                        name="applicationTags"
                        label="Application"
                        placeholder="Specify the applications you are active in"
                        data={applicationTags}
                    />

                    {editor && RTEField && <RTEField editor={editor} label="About the company" />}

                    <URLField name="website" label="Website" />

                    <SimpleGrid cols={{ base: 1, xs: 2 }} spacing="xs">
                        <URLField name="socials.linkedin" label="LinkedIn" />
                        <URLField name="socials.facebook" label="Facebook" />
                        <URLField name="socials.twitter" label="X" />
                        <URLField name="socials.youtube" label="YouTube" />
                    </SimpleGrid>

                    <AddressAutocompleteField name="locations.0.address" label="Address/location" />

                    <ImageField
                        name="logos.small"
                        label="Logo"
                        buttonLabel="Upload a logo"
                        group="manufacturers:images"
                        buttonProps={{ variant: 'outline' }}
                        description="Choose a logo with a transparent background. Recommended size: 200 x 200"
                    />

                    <ImageField
                        name="cover"
                        label="Cover image"
                        buttonLabel="Upload a cover image"
                        group="manufacturers:images"
                        buttonProps={{ variant: 'outline' }}
                        description="Choose an image without text, to show a feel of your company. Recommended size: 1920 x 150"
                    />

                    <CompliancesField
                        name="compliance"
                        label="We are a member of the following organizations"
                        description="Check all that apply. Multiple options are allowed."
                        showCompliances={[Compliance.CURRENT_OS, Compliance.EMERGE, Compliance.ODCA, Compliance.OTHER]}
                    />
                </Stack>
            </SignupLayout.Content>

            <SignupLayout.Actions
                leftActions={
                    <Button variant="outline" onClick={prevStep} leftSection={<IoArrowBack />}>
                        Back
                    </Button>
                }
                rightActions={
                    <FormSubmit rightSection={<IoArrowForward />} disableIfErrors disabled={created} loading={created}>
                        Save and proceed
                    </FormSubmit>
                }
            />
        </CompanyForm>
    );
};

export { Step1 };
