import React from 'react';

import { useRouter } from 'next/router';

import { Button, Group, Space, Stack, Text, Title } from '@mantine/core';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';

import { CompanyProfile, CompanySubscription, getCompanySubscriptionData } from 'models';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { ImagePreview } from 'components/image-preview/ImagePreview';

import { Form, FormOnSubmit } from 'components/forms/Form';
import { AutoSave } from 'components/forms/AutoSave';

import { SignupLayout } from 'components/signup-layout/SignupLayout';
import { CompanySignupProps } from 'components/company-signup/CompanySignup';
import { CompanySubscriptionOptions } from 'components/subscriptions/CompanySubscriptionOptions';
import { SubscriptionUpdateWrapper } from 'components/subscriptions/SubscriptionUpdateWrapper';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { useCurrentTeam } from 'hooks/use-current-team';
import { SetupYourProfileAlert } from '../components/SetupYourProfileAlert';
import { CompanySubscriptionUpdgradeCopy } from 'components/company-subscription/CompanySubscriptionUpgradeCopy';

type Props = Omit<CompanySignupProps, 'progress' | 'active'>;

const Step3 = ({ prevStep, company }: Props) => {
    const team = useCurrentTeam();
    const router = useRouter();

    if (!team) return null;

    const subscription = getCompanySubscriptionData(team?.subscriptions)?.subscription ?? CompanySubscription.NONE;

    const companyUrl = company && CompanyProfileHelpers.urls.view(company.slug);

    const navigateToProfile = async () => {
        if (!companyUrl) return;

        CompanyProfileService.finishLocalSignup(team.id);
        router.replace(companyUrl).then();
    };

    if (!company) return null;

    return (
        <>
            <SignupLayout.Content>
                <Stack>
                    <Title order={3} size="h2" fw={600}>
                        Select a plan
                    </Title>

                    <Text>
                        <CompanySubscriptionUpdgradeCopy />
                    </Text>

                    <SetupYourProfileAlert />

                    <Space />
                    <SubscriptionUpdateWrapper
                        toSubscription={CompanySubscription.PREMIUM}
                        currentSubscription={subscription}
                        team={team}
                        redirectUrl={CompanyProfileHelpers.urls.view(company.slug)}
                    >
                        <CompanySubscriptionOptions />
                    </SubscriptionUpdateWrapper>
                    <Space />

                    <Title>Premium Feature Preview</Title>
                    <Group justify="space-around" align="top">
                        <ImagePreview
                            withShadow
                            src="/images/analytics_products_preview.png"
                            alt="Analytics Products Preview"
                        />
                        <ImagePreview
                            withShadow
                            src="/images/lead_management_preview.png"
                            alt="Lead Management Preview"
                        />
                    </Group>
                </Stack>
            </SignupLayout.Content>
            <SignupLayout.Actions
                leftActions={
                    <Button variant="outline" onClick={prevStep} leftSection={<IoArrowBack />}>
                        Back
                    </Button>
                }
                rightActions={
                    <Button onClick={navigateToProfile} variant="outline" rightSection={<IoArrowForward />}>
                        {subscription === CompanySubscription.PREMIUM
                            ? 'View profile'
                            : 'Continue to profile and subscribe later'}
                    </Button>
                }
            />
        </>
    );
};

const WrappedStep3 = (props: Props) => {
    const team = useCurrentTeam();
    const { company } = useCompanyProfile(props.company ?? null);

    const onSubmit: FormOnSubmit<CompanyProfile> = async (values) => {
        if (company) {
            await CompanyProfileService.update(company.id, values);

            if (team) {
                CompanyProfileService.finishLocalSignup(team.id);
            }
        }
    };

    if (!company) return null;

    return (
        <Form
            onSubmit={onSubmit}
            defaultValues={{ services: company.services, users: company.users }}
            disableSyncDefaultValues
            data-content-wrapper
        >
            <AutoSave />
            <Step3 {...props} />
        </Form>
    );
};

export { WrappedStep3 as Step3 };
