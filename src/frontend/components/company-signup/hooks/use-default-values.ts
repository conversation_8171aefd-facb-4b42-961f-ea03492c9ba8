import { useEffect, useState } from 'react';

import { Editor } from '@tiptap/react';

import { useRouterQuery } from 'hooks/use-router-query';

import { FileService } from 'services/FileService';

import { CompanyProfile } from 'models';

import { ApiService } from 'services/ApiService';
import { GeoService } from 'services/GeoService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { publicConfig } from '@public-config';
import { textToHtml } from 'helpers/text-to-html';

const useDefaultValues = (incomingDefaultValues: Partial<CompanyProfile>, aboutEditor?: Editor) => {
    const [defaultValues, setDefaultValues] = useState<Partial<CompanyProfile>>(incomingDefaultValues);

    const { linkedin } = useRouterQuery<{
        linkedin?: string;
    }>();

    useEffect(() => {
        const execute = async () => {
            LocalNotificationService.showInfo({
                message: `Prefilling company: ${linkedin}`,
            });

            const scraper = await ApiService.get(`${publicConfig.urls.api}/manufacturers/prefill?linkedin=${linkedin}`);
            const name = scraper.name;

            let profileImage = null;
            let coverImage = null;

            if (scraper.profile_pic_url) {
                const { id } = await FileService.createFromUrl(scraper.profile_pic_url, `${name}-logo`, name);
                profileImage = id;
            }

            if (scraper.background_cover_image_url) {
                const { id } = await FileService.createFromUrl(
                    scraper.background_cover_image_url,
                    `${name}-cover`,
                    name,
                );
                coverImage = id;
            }

            const locationQuery = [
                scraper.hq?.country,
                scraper.hq?.state,
                scraper.hq?.city,
                scraper.hq?.postal_code,
                scraper.hq?.line_1,
            ].join(' ');

            const geo = await GeoService.geocode(locationQuery);

            if (aboutEditor && scraper.description) {
                aboutEditor.commands.setContent(textToHtml(scraper.description));
            }

            setDefaultValues({
                name,
                website: scraper.website,
                socials: {
                    linkedin: `https://linkedin.com/company/${linkedin}`,
                    twitter: scraper.extra?.twitter_id ? `https://x.com/${scraper.extra.twitter_id}` : '',
                    facebook: scraper.extra?.facebook_id ? `https://facebook.com/${scraper.extra.facebook_id}` : '',
                    youtube: '',
                },
                logos: {
                    small: profileImage,
                    large: profileImage,
                },
                cover: coverImage,
                locations: geo
                    ? [
                          {
                              name,
                              image: null,
                              address: {
                                  name: geo.full_address,
                                  //@ts-ignore it exists
                                  street: geo.context.address?.street_name ?? '',
                                  //@ts-ignore it exists
                                  number: geo.context.address?.address_number ?? '',
                                  postalCode: geo.context.postcode?.name ?? '',
                                  city: geo.context.place?.name ?? '',
                                  state: geo.context.region?.name ?? '',
                                  country: geo.context.country?.name ?? '',
                                  coordinates: [geo.coordinates.longitude, geo.coordinates.latitude],
                              },
                              isHeadquarter: true,
                              contactInformation: {
                                  email: scraper.extra?.contact_email ?? undefined,
                                  phone: scraper.extra?.phone_number ?? undefined,
                              },
                          },
                      ]
                    : [],
                imported: true,
            });

            LocalNotificationService.showInfo({
                message: `Prefilling finished`,
            });
        };

        if (linkedin) {
            execute().then();
        }
    }, [linkedin, aboutEditor]);

    return defaultValues;
};

export { useDefaultValues };
