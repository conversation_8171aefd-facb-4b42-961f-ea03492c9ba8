import React, { FC, useEffect, useState } from 'react';

import { useRouter } from 'next/router';

import { CompanyProfile } from 'models';

import { Page } from 'components/page';
import { RightSide } from 'components/company-signup/components/RightSide';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';
import { ModalService } from 'services/ModalService';
import { Steps } from 'components/company-signup/components/Steps';
import { SignupLayout } from 'components/signup-layout/SignupLayout';

import { Step0 } from './steps/Step0';
import { Step1 } from 'components/company-signup/steps/Step1';
import { Step2 } from 'components/company-signup/steps/Step2';
import { Step3 } from 'components/company-signup/steps/Step3';
import { CompanyProfileService } from 'services/CompanyProfileService';

export const COMPANY_SIGNUP_STEPS = [Step0, Step1, Step2, Step3];
export const COMPANY_SIGNUP_NB_STEPS = COMPANY_SIGNUP_STEPS.length;

export type CompanySignupProps = {
    company?: CompanyProfile;
    active: number;
    nextStep: () => void;
    prevStep: () => void;
    progress?: number;
};

const CompanySignup: FC<{
    title: string;
    company?: CompanyProfile;
}> = ({ title, company }) => {
    const user = useCurrentUser();
    const team = useCurrentTeam();
    const router = useRouter();

    const [active, setActive] = useState(0);
    const nextStep = () => setActive((current) => (current < COMPANY_SIGNUP_NB_STEPS ? current + 1 : current));
    const prevStep = () => setActive((current) => (current > 0 ? current - 1 : current));

    useEffect(() => {
        const { step, ...updatedQuery } = router.query;

        if (step) {
            setActive(parseInt(step as string) - 1);
            router.replace({ query: updatedQuery }, undefined, { shallow: true }).then();
        }
    }, [router]);

    useEffect(() => {
        if (user) return;

        ModalService.openLoginModal({
            message: 'You need to be logged in to create a company profile',
            closeOnClickOutside: false,
            closeOnEscape: false,
        });

        return () => ModalService.closeLoginModal();
    }, [user]);

    useEffect(() => {
        if (!team) return;

        if (company) {
            CompanyProfileService.setLocalSignup(team.id, company.id, active + 1);
        } else {
            CompanyProfileService.setLocalSignup(team.id);
        }
    }, [team, active]);

    return (
        <Page hideFooter hideLicenseAgreement title={title}>
            <Page.FullScreenContent>
                <SignupLayout>
                    <SignupLayout.LeftSide>
                        <SignupLayout.Header title={title} active={active} nbSteps={COMPANY_SIGNUP_NB_STEPS} />
                        <Steps active={active} nextStep={nextStep} prevStep={prevStep} company={company} />
                    </SignupLayout.LeftSide>
                    <SignupLayout.RightSide>
                        <RightSide />
                    </SignupLayout.RightSide>
                </SignupLayout>
            </Page.FullScreenContent>
        </Page>
    );
};

export { CompanySignup };
