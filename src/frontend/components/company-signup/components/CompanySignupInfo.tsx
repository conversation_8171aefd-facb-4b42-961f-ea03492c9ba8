import React from 'react';

import Link from 'next/link';

import { Anchor, Badge, Flex, FlexProps, Stack, ThemeIcon } from '@mantine/core';
import { IoAnalyticsSharp, IoChatbubblesSharp, IoSearchOutline, IoShapesSharp } from 'react-icons/io5';
import { BsBoxFill } from 'react-icons/bs';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { HighlightedInfoItem } from 'components/dashboard/components/Dashboard.HighlightedInfo';

export const PROFILE_SIGNUP_ITEMS: (HighlightedInfoItem & { premium?: boolean })[] = [
    {
        value: 'products',
        title: 'Showcase your products',
        icon: <BsBoxFill />,
        description: 'Share your products with your profile',
    },
    {
        value: 'designs',
        title: 'Share reference designs',
        icon: <IoShapesSharp />,
        description: (
            <>
                Publish{' '}
                <Anchor component={Link} href={RouterHelpers.urls.searchTab('designs')} target="_blank">
                    reference designs
                </Anchor>{' '}
                to inspire and collaborate
            </>
        ),
    },
    {
        value: 'catalog',
        title: 'Increase visibility',
        icon: <IoSearchOutline />,
        description: (
            <>
                Appear in{' '}
                <Anchor component={Link} href={RouterHelpers.urls.searchTab('products')} target="_blank">
                    AI-powered search
                </Anchor>{' '}
                results
            </>
        ),
        premium: true,
    },
    {
        value: 'customers',
        title: 'Chat with customers',
        icon: <IoChatbubblesSharp />,
        description: 'Engage directly with customers and manage inquiries',
        premium: true,
    },
    {
        value: 'insights',
        title: 'Insights and analytics',
        icon: <IoAnalyticsSharp />,
        description: 'Track performance of your products and profile',
        premium: true,
    },
];

const CompanySignupInfo = ({
    maxNbShownItems,
    hidePremiumBadges,
}: {
    maxNbShownItems?: number | null;
    hidePremiumBadges?: boolean;
}) => {
    return (
        <Stack gap="lg">
            {PROFILE_SIGNUP_ITEMS.filter((_, index) => !maxNbShownItems || index < maxNbShownItems).map((item) => (
                <CompanySignupItem key={item.value} {...item} premium={item.premium && !hidePremiumBadges}>
                    {item.description}
                </CompanySignupItem>
            ))}
        </Stack>
    );
};

const CompanySignupItem = ({
    icon,
    title,
    premium = false,
    children,
    ...rest
}: {
    icon: React.ReactNode;
    title: string;
    premium?: boolean;
    children: React.ReactNode;
} & FlexProps) => {
    return (
        <Flex align="center" gap="xs" {...rest}>
            <ThemeIcon color="primary" size="lg" radius="xl">
                {icon}
            </ThemeIcon>

            <Stack gap={0}>
                <Flex justify="space-between" align="center">
                    <b>{title}</b>
                    {premium && (
                        <Badge px={5} size="xs" radius="xs">
                            Premium
                        </Badge>
                    )}
                </Flex>
                <div>{children}</div>
            </Stack>
        </Flex>
    );
};

export { CompanySignupInfo };
