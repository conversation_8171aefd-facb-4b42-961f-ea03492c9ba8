import { FC } from 'react';

import { z } from 'zod';
import { Button, ButtonProps, Modal, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoCheckmark } from 'react-icons/io5';

import { CompanyProfile, PublishedStatus } from 'models';

import { Form } from 'components/forms/Form';
import { CheckboxField } from 'components/forms/fields/CheckboxField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { PublishedBadge } from 'components/published-badge/PublishedBadge';

import { getPublishCopy } from 'helpers/get-publish-copy';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyProfileService } from 'services/CompanyProfileService';

const PublishValidator = z.object({
    verify: z.boolean(),
});

const PublishButton: FC<{ company: CompanyProfile; showAsBadge?: React.ReactNode } & ButtonProps> = ({
    company,
    showAsBadge,
    ...props
}) => {
    const [opened, handlers] = useDisclosure();

    const { mutate: mutateCompanies } = useCurrentTeamCompanies();

    const handlePublish = async () => {
        await CompanyProfileService.update(company.id, { status: PublishedStatus.REVIEW });
        mutateCompanies();
        handlers.close();
    };

    const isDraft = company.status === PublishedStatus.DRAFT;

    return (
        <>
            {showAsBadge ? (
                <PublishedBadge
                    size="md"
                    status={company.status}
                    withTooltip={isDraft && 'Click to publish your profile'}
                    onClick={isDraft ? handlers.open : undefined}
                />
            ) : (
                <Button variant="filled" onClick={handlers.open} leftSection={<IoCheckmark size={12} />} {...props}>
                    Publish profile
                </Button>
            )}

            <Modal opened={opened} onClose={handlers.close} title="Request publish profile?">
                <Form onSubmit={handlePublish} zodSchema={PublishValidator}>
                    <Stack maw={600}>
                        <Text>
                            Please confirm that all information is accurate and complete by checking the checkbox below.
                            Subsequently, click the Request publish profile button to make your profile live.
                        </Text>

                        <CheckboxField name="verify" required label={<Text>{getPublishCopy()}</Text>} />

                        <Text>
                            We will review your profile <b>within 24 hours</b> and notify you once your profile has been
                            reviewed and published.
                        </Text>

                        <FormSubmit>Request publish profile</FormSubmit>
                    </Stack>
                </Form>
            </Modal>
        </>
    );
};

export { PublishButton };
