import { ManufacturerProfile } from 'models';

import { useCurrentUser } from 'hooks/use-current-user';

export const useIsUserPartOfCompany = (company: ManufacturerProfile | undefined): boolean => {
    const currentUser = useCurrentUser();

    if (!company || !currentUser) {
        return false;
    }

    const isCompanyTeamMember = company.users.some((user) => user === currentUser.id);

    return isCompanyTeamMember;
};
