import type { IntercomChannel } from 'models';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { intercomState } from '../state/intercom';

const useIntercom = (channels: IntercomChannel[] = []) => {
    const state = useSnapshot(intercomState);

    return {
        ...state,
        channel: channels.find((channel) => channel.id === state.activeChannel),
    };
};

export { useIntercom };
