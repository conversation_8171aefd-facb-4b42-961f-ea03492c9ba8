import useSWR from 'swr';

import { IntercomService } from 'services/IntercomService';
import { IntercomHelpers } from 'helpers/IntercomHelpers';

const useIntercomChannelForTeam = (type: 'project' | 'component' | 'company', id: string, teamId?: string) => {
    const key = teamId ? IntercomHelpers.swr.channelsForTeam[type](id, teamId) : null;
    const fetcher = teamId ? async () => IntercomService.getChannelForTeam(type, id, teamId) : null;

    const swr = useSWR(key, fetcher);

    return {
        ...swr,
        channel: swr?.data?.channel || null,
    };
};

export { useIntercomChannelForTeam };
