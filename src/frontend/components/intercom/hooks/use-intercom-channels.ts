import useSWR from 'swr';

import { IntercomService } from 'services/IntercomService';
import { IntercomHelpers } from 'helpers/IntercomHelpers';

const useIntercomChannels = (type: 'project' | 'component' | 'company', id: string) => {
    const key = IntercomHelpers.swr.channels[type](id);
    const fetcher = async () => IntercomService.getChannels(type, id);

    const swr = useSWR(key, fetcher);

    return {
        ...swr,
        channels: swr?.data?.docs || [],
    };
};

export { useIntercomChannels };
