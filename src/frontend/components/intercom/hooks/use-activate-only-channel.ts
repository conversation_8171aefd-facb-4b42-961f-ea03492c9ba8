import { useEffect } from 'react';
import { IntercomChannel } from 'models';

import { intercomState } from 'components/intercom/state/intercom';

const useActivateOnlyChannel = (channels: IntercomChannel[]) => {
    useEffect(() => {
        if (channels.length === 1) {
            intercomState.activeChannel = channels[0].id;
        }
    }, [channels.length]);

    useEffect(() => {
        return () => {
            intercomState.activeChannel = null;
        };
    }, []);
};

export { useActivateOnlyChannel };
