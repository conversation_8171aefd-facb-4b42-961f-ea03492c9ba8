import useSWR from 'swr';

import { IntercomService } from 'services/IntercomService';
import { IntercomHelpers } from 'helpers/IntercomHelpers';

const useIntercomChannelMessages = (channelId: string) => {
    const key = IntercomHelpers.swr.messages(channelId);
    const fetcher = async () => IntercomService.getChannelMessages(channelId);

    const swr = useSWR(key, fetcher);

    return {
        ...swr,
        messages: swr?.data?.docs || [],
    };
};

export { useIntercomChannelMessages };
