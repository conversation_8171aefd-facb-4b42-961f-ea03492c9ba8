import React, { FC } from 'react';
import { Manufacturer, Team } from 'models';

import { Box, Group, Tooltip, UnstyledButton } from '@mantine/core';
import { IoClose } from 'react-icons/io5';
import { TbChevronLeft } from 'react-icons/tb';
import { Avatar, AvatarGroup } from 'components/avatar/Avatar';

import { IntercomService } from 'services/IntercomService';

import cx from './Intercom.module.scss';

const IntercomNavigation: FC<{
    title?: string;
    handleBackClick?: () => void;
    team?: Team;
    company?: Manufacturer;
}> = ({ title, handleBackClick, team, company }) => (
    <Box className={cx.navigation} data-has-back-icon={!!handleBackClick}>
        {(title || handleBackClick) && (
            <Group gap={0} wrap="nowrap">
                {handleBackClick && (
                    <UnstyledButton className={cx.navigationAction} onClick={handleBackClick}>
                        <TbChevronLeft style={{ fill: 'none' }} />
                    </UnstyledButton>
                )}
                <Box style={{ width: 200, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                    {title}
                </Box>
            </Group>
        )}
        <Group gap={0} wrap="nowrap">
            {team && company && (
                <Tooltip
                    label={
                        <Box>
                            This conversation is only visible for users in {team.name} and {company.name}.
                        </Box>
                    }
                    position="bottom-end"
                    w={200}
                    multiline
                    withArrow
                    arrowOffset={9}
                    color="blue"
                >
                    <AvatarGroup spacing="xs">
                        {team.users.slice(0, 3).map((user) => (
                            <Avatar user={user.user} size="22" key={user.user} />
                        ))}
                        <Avatar company={company} size="22" />
                    </AvatarGroup>
                </Tooltip>
            )}
            <UnstyledButton className={cx.navigationAction} onClick={IntercomService.close}>
                <IoClose />
            </UnstyledButton>
        </Group>
    </Box>
);

export { IntercomNavigation };
