import React, { FC } from 'react';

import { ButtonProps } from '@mantine/core';

import { ManufacturerProfile, Project } from 'models';

import { Intercom } from 'components/intercom/Intercom';
import { IntercomCreate } from 'components/intercom/Intercom.Create';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';
import { IntercomSuggestions } from 'components/intercom/Intercom.Suggestions';

import { CompanyLogo } from 'components/company-logo';

import { IntercomService } from 'services/IntercomService';

import { useIntercomChannels } from '../hooks/use-intercom-channels';
import { useActivateOnlyChannel } from 'components/intercom/hooks/use-activate-only-channel';
import { useIsUserPartOfCompany } from 'components/intercom/hooks/use-is-user-part-of-company';

const ProjectIntercom: FC<{
    company: ManufacturerProfile;
    project: Project;
    buttonProps?: Partial<ButtonProps>;
}> = ({ company, project, buttonProps }) => {
    const { channels } = useIntercomChannels('project', project.id);
    const userPartOfCompany = useIsUserPartOfCompany(company);

    useActivateOnlyChannel(channels);

    const create = async (content: any, files: any) => {
        await IntercomService.createChannel({
            type: 'project',
            companyId: company.id,
            projectId: project.id,
            content,
            files,
            userPartOfCompany,
        });
    };

    const tooltip = <>Collaborate with {company.name} on this project.</>;

    const suggestions = [
        'Could we collaborate to optimize the system for my needs?',
        'What steps should I take to adapt your design to my application?',
        'Can you provide pricing information for your products and services?',
    ];

    return (
        <Intercom
            channels={channels}
            tooltip={tooltip}
            tooltipType="project"
            company={company}
            buttonProps={buttonProps}
        >
            <IntercomCreate onCreate={create}>
                <IntercomEmpty>
                    <IntercomEmpty.Header>
                        <CompanyLogo logos={company.logos} width={80} />
                        Collaborate with {company.name} on this project
                    </IntercomEmpty.Header>
                    <IntercomEmpty.Content>
                        <IntercomSuggestions suggestions={suggestions} />
                    </IntercomEmpty.Content>
                </IntercomEmpty>
            </IntercomCreate>
        </Intercom>
    );
};

export { ProjectIntercom };
