import React, { FC } from 'react';

import { Component, ManufacturerProfile } from 'models';

import { Intercom } from 'components/intercom/Intercom';
import { IntercomCreate } from 'components/intercom/Intercom.Create';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';
import { IntercomSuggestions } from 'components/intercom/Intercom.Suggestions';

import { CompanyLogo } from 'components/company-logo/CompanyLogo';

import { IntercomService } from 'services/IntercomService';

import { useActivateOnlyChannel } from '../hooks/use-activate-only-channel';
import { useIntercomChannels } from '../hooks/use-intercom-channels';
import { useIsUserPartOfCompany } from '../hooks/use-is-user-part-of-company';

const ComponentIntercom: FC<{
    component: Component;
    company: ManufacturerProfile;
}> = ({ component, company }) => {
    const { channels } = useIntercomChannels('component', component.id);
    const userPartOfCompany = useIsUserPartOfCompany(company);

    useActivateOnlyChannel(channels);

    const create = async (content: any, files: any) => {
        await IntercomService.createChannel({
            type: 'component',
            companyId: company.id,
            componentId: component.id,
            content,
            files,
            userPartOfCompany,
        });
    };

    const tooltip = !userPartOfCompany && <>Ask your product questions to {company.name}.</>;

    const suggestions = [
        'Can you help me configure this product for my application?',
        'What additional components are required to integrate this product into my design?',
        'Do you have any reference designs or case studies relevant to my application?',
    ];

    return (
        <Intercom
            channels={channels}
            tooltip={tooltip}
            tooltipType="component"
            userPartOfCompany={userPartOfCompany}
            company={company}
        >
            <IntercomCreate onCreate={create}>
                <IntercomEmpty>
                    <IntercomEmpty.Header>
                        <CompanyLogo logos={company.logos} width={80} />
                        Connect with our team — we are here to help!
                    </IntercomEmpty.Header>
                    <IntercomEmpty.Content>
                        <IntercomSuggestions suggestions={suggestions} />
                    </IntercomEmpty.Content>
                </IntercomEmpty>
            </IntercomCreate>
        </Intercom>
    );
};

export { ComponentIntercom };
