import React, { FC } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Text } from '@mantine/core';
import { IoBulbOutline } from 'react-icons/io5';

import { ManufacturerProfile } from 'models';

import { Intercom } from 'components/intercom/Intercom';
import { IntercomCreate } from 'components/intercom/Intercom.Create';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';
import { IntercomSuggestions } from 'components/intercom/Intercom.Suggestions';

import { CompanyLogo } from 'components/company-logo';

import { IntercomService } from 'services/IntercomService';

import { useIntercomChannels } from '../hooks/use-intercom-channels';
import { useActivateOnlyChannel } from '../hooks/use-activate-only-channel';
import { useIsUserPartOfCompany } from '../hooks/use-is-user-part-of-company';

const CompanyIntercom: FC<{
    company: ManufacturerProfile;
}> = ({ company }) => {
    const { channels } = useIntercomChannels('company', company.id);
    const userPartOfCompany = useIsUserPartOfCompany(company);

    useActivateOnlyChannel(channels);

    const create = async (content: any, files: any) => {
        await IntercomService.createChannel({
            type: 'company',
            companyId: company.id,
            content,
            files,
            userPartOfCompany,
        });
    };

    return (
        <Intercom channels={channels} userPartOfCompany={userPartOfCompany} company={company}>
            <IntercomCreate
                placeholder={`Send a message to ${company.name}`}
                onCreate={userPartOfCompany ? undefined : create}
            >
                {userPartOfCompany ? <CompanyIntercomEmptyCompany /> : <CompanyIntercomEmptyUser company={company} />}
            </IntercomCreate>
        </Intercom>
    );
};

const CompanyIntercomEmptyUser: FC<{
    company: ManufacturerProfile;
}> = ({ company }) => {
    const suggestions = [
        'What solutions or products would you recommend for my project?',
        'Do you offer technical support or installation guidance for this products?',
        'Is there a minimum order quantity for your products?',
    ];

    return (
        <IntercomEmpty>
            <IntercomEmpty.Header>
                <CompanyLogo logos={company.logos} width={80} />
                How may we help you?
            </IntercomEmpty.Header>
            <IntercomEmpty.Content>
                <IntercomSuggestions suggestions={suggestions} />
            </IntercomEmpty.Content>
        </IntercomEmpty>
    );
};

const CompanyIntercomEmptyCompany = () => {
    return (
        <IntercomEmpty>
            <IntercomEmpty.Header>
                <IoBulbOutline />
                <Text fz={16} fw={700} lh={1.4}>
                    Welcome to your
                    <br />
                    Customer chat.
                </Text>
                <div>When someone reaches out, their messages will appear here.</div>
                <div>
                    <CopyButton value={window.location.href}>
                        {({ copied, copy }) => (
                            <Button variant="gradient" onClick={copy}>
                                {copied ? 'Profile URL Copied' : 'Share Your Profile'}
                            </Button>
                        )}
                    </CopyButton>
                </div>
            </IntercomEmpty.Header>
        </IntercomEmpty>
    );
};

export { CompanyIntercom };
