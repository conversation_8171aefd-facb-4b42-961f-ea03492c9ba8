import React, { FC, useState } from 'react';

import { ManufacturerProfile } from 'models';

import { ActionIcon, Box, Popover, PopoverProps } from '@mantine/core';
import { IoChatbubblesOutline } from 'react-icons/io5';

import { ChatService } from 'components/diagram/services/ChatService';

import cx from 'components/intercom/Intercom.module.scss';
import { useChatChannels } from 'components/diagram/hooks/use-chat-channels';

const FakeProjectIntercom: FC<{
    company: ManufacturerProfile;
}> = ({ company }) => {
    const [showTooltip, setShowTooltip] = useState(false);
    const { channels } = useChatChannels();

    const popoverProps: PopoverProps = {
        position: 'top-end',
        withArrow: true,
        arrowOffset: 16,
        arrowSize: 12,
    };

    const activate = () => {
        const companyChannel = channels.find((channel) => {
            return channel.access.company === company.id;
        });

        if (companyChannel) {
            ChatService.activateChannel(companyChannel.id);
        }

        setShowTooltip(true);

        setTimeout(() => {
            setShowTooltip(false);
        }, 4000);
    };

    return (
        <Popover opened={showTooltip} withinPortal={false} {...popoverProps}>
            <Popover.Target>
                <Box className={`${cx.chatBubbleWrapper} animation-border-spin`}>
                    <ActionIcon size="xl" radius="xl" variant="gradient" onClick={activate}>
                        <IoChatbubblesOutline size={24} />
                    </ActionIcon>
                </Box>
            </Popover.Target>
            <Popover.Dropdown className={cx.tooltip}>
                We moved your conversation with
                <br />
                <strong>{company.name}</strong> to the sidebar.
            </Popover.Dropdown>
        </Popover>
    );
};

export { FakeProjectIntercom };
