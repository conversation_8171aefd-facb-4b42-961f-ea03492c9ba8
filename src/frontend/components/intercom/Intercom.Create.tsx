import React, { <PERSON> } from 'react';

import { IntercomNavigation } from 'components/intercom/Intercom.Navigation';
import { IntercomContent } from 'components/intercom/Intercom.Content';
import { IntercomComposer } from 'components/intercom/Intercom.Composer';

const IntercomCreate: FC<{
    placeholder?: string;
    onCreate?: (content: any, files: any) => void;
    children?: React.ReactNode;
}> = ({ placeholder, onCreate, children }) => (
    <React.Fragment>
        <IntercomNavigation />
        <IntercomContent>{children}</IntercomContent>
        {onCreate && <IntercomComposer onSubmit={onCreate} placeholder={placeholder} />}
    </React.Fragment>
);

export { IntercomCreate };
