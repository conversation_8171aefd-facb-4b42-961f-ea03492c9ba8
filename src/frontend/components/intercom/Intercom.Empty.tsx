import React, { <PERSON> } from 'react';

import { Box } from '@mantine/core';

import cx from './Intercom.module.scss';

const IntercomEmpty: FC<{
    children: React.ReactNode;
}> & {
    Header: FC<{
        children: React.ReactNode;
    }>;
    Content: FC<{
        children: React.ReactNode;
    }>;
} = ({ children }) => <Box className={cx.empty}>{children}</Box>;

IntercomEmpty.Header = ({ children }) => <Box className={cx.emptyHeader}>{children}</Box>;
IntercomEmpty.Header.displayName = 'IntercomEmpty.Header';

IntercomEmpty.Content = ({ children }) => <Box className={cx.emptyContent}>{children}</Box>;
IntercomEmpty.Content.displayName = 'IntercomEmpty.Content';

export { IntercomEmpty };
