import React, { FC, useEffect, useRef } from 'react';
import type { IntercomChannel, IntercomMessage } from 'models';

import { ActionIcon, Box, Loader, ScrollArea, UnstyledButton } from '@mantine/core';
import { TbMoodPlus } from 'react-icons/tb';

import { IntercomContent } from 'components/intercom/Intercom.Content';
import { IntercomComposer } from 'components/intercom/Intercom.Composer';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';

import { Avatar } from 'components/avatar/Avatar';
import { TipTapViewer } from 'components/tiptap/TipTapViewer';

import { IntercomService } from 'services/IntercomService';

import { useIntercomChannelMessages } from './hooks/use-intercom-channel-messages';
import { useUser } from 'hooks/use-user';

import { DateService } from 'services/DateService';
import { useTeam } from 'hooks/use-team';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useIsUserPartOfCompany } from 'components/intercom/hooks/use-is-user-part-of-company';
import { useCurrentUser } from 'hooks/use-current-user';
import { intercomState } from 'components/intercom/state/intercom';
import { IntercomNavigation } from 'components/intercom/Intercom.Navigation';
import { Reactions } from 'components/reactions/Reactions';
import { AddReaction } from 'components/reactions/AddReaction';

import cx from './Intercom.module.scss';

const IntercomChannel: FC<{ channel: IntercomChannel }> = ({ channel }) => {
    const viewport = useRef<HTMLDivElement>(null);
    const { messages, isLoading } = useIntercomChannelMessages(channel.id);

    const { team } = useTeam(channel.team);
    const { company } = useCompanyProfile(channel.access.company);

    const userPartOfCompany = useIsUserPartOfCompany(company);

    let title = channel.name || '';

    if (userPartOfCompany && team) {
        title = `Chat with ${team.name}`;
    }

    if (!userPartOfCompany && company) {
        title = `Chat with ${company.name}`;
    }

    const back = () => {
        intercomState.activeChannel = null;
    };

    const reply = async (content: any, files: any) => {
        await IntercomService.reply({
            channelId: channel.id,
            content,
            files,
            type: channel.type,
            userPartOfCompany: Boolean(userPartOfCompany),
        });
    };

    useEffect(() => {
        if (channel.viewed === false) {
            IntercomService.viewChannel(channel.id).then();
        }
    }, [channel.viewed, messages.length]);

    useEffect(() => {
        setTimeout(() => {
            if (viewport.current) {
                viewport.current.scrollTo({
                    top: viewport.current.scrollHeight,
                    behavior: 'smooth',
                });
            }
        }, 250);
    }, [messages.length]);

    return (
        <React.Fragment>
            {team && (
                <IntercomNavigation
                    title={title}
                    handleBackClick={userPartOfCompany ? back : undefined}
                    team={team}
                    company={company}
                />
            )}
            <IntercomContent>
                {isLoading ? (
                    <IntercomEmpty>
                        <Loader />
                    </IntercomEmpty>
                ) : (
                    <ScrollArea type="scroll" h={315} viewportRef={viewport}>
                        <Box className={cx.messages}>
                            {messages.map((message) => (
                                <Message message={message} key={message.id} />
                            ))}
                        </Box>
                    </ScrollArea>
                )}
            </IntercomContent>
            <IntercomComposer onSubmit={reply} />
        </React.Fragment>
    );
};

const Message: FC<{ message: IntercomMessage }> = ({ message }) => {
    const currentUser = useCurrentUser();

    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    const { content, files } = message.content[0];

    const unlink = async () => {
        await IntercomService.unlinkCompany(message.id);
    };

    return (
        <Box className={cx.message}>
            {company ? <Avatar company={company} size={32} /> : <Avatar user={message.createdBy} size={32} />}
            <Box>
                <Box className={cx.messageHeader}>
                    {company?.name ?? user?.name}
                    <small>{DateService.formatDistanceToNow(message.createdAt)}</small>
                    <Box className={cx.messageActions}>
                        {currentUser?.internal && company && (
                            <UnstyledButton onClick={unlink}>Unlink company</UnstyledButton>
                        )}
                        <AddReaction collection="intercomMessages" id={message.id}>
                            <ActionIcon variant="transparent" size={20}>
                                <TbMoodPlus size={16} strokeWidth={1.5} />
                            </ActionIcon>
                        </AddReaction>
                    </Box>
                </Box>
                <TipTapViewer content={JSON.parse(content)} files={files} key={message.id} />
                <Reactions reactions={message.reactions} collection="intercomMessages" id={message.id} mt={4} />
            </Box>
        </Box>
    );
};

export { IntercomChannel };
