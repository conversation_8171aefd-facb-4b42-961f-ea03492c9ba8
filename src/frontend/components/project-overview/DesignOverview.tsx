import React, { FC } from 'react';

import { Loader, Stack } from '@mantine/core';

import { ProjectGrid } from './components';

import { useRecentProjects } from 'hooks/use-recent-projects';

import { Page } from 'components/page';
import { ProjectSearch } from 'components/project-search/ProjectSearch';
import { DashboardTitle } from 'components/dashboard/components/Dashboard.Title';
import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';

const DesignOverview: FC = () => {
    const recentProjects = useRecentProjects();

    return (
        <Page
            title="My reference designs"
            hideFooter
            showBackground
            breadcrumbs={{
                rightSection: <ReferenceDesignButton variant="filled" size="xs" />,
            }}
        >
            <Page.WideContent>
                {recentProjects?.length > 0 && (
                    <Stack gap="xs">
                        <DashboardTitle>Recent projects</DashboardTitle>
                        <ProjectGrid projects={recentProjects.slice(0, 4)} />
                    </Stack>
                )}

                <Stack gap="xs">
                    <DashboardTitle>All reference designs</DashboardTitle>
                    <ProjectSearch defaultFilters={{ team: null, referenceDesigns: true }}>
                        <Stack gap="xs">
                            <ProjectSearch.Results>
                                {({ projects, isLoading, isLoadingMore, mutate }) => (
                                    <React.Fragment>
                                        <ProjectGrid
                                            type="reference design"
                                            projects={projects}
                                            isLoading={isLoading}
                                            showCreate
                                            showFeatureTracker
                                            onProjectDelete={async () => {
                                                mutate();
                                            }}
                                        />
                                        {isLoadingMore && <Loader size="sm" />}
                                    </React.Fragment>
                                )}
                            </ProjectSearch.Results>
                        </Stack>
                    </ProjectSearch>
                </Stack>
            </Page.WideContent>

            <Page.Footer />
        </Page>
    );
};

export { DesignOverview };
