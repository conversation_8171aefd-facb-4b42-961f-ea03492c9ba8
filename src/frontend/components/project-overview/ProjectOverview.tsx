import React, { FC } from 'react';

import { Group, Loader, Stack } from '@mantine/core';

import { ProjectCreateButton, ProjectGrid } from './components';

import { useRecentProjects } from 'hooks/use-recent-projects';

import { Page } from 'components/page';
import { Dashboard } from 'components/dashboard/Dashboard';
import { ProjectSearch } from 'components/project-search/ProjectSearch';
import { DashboardTitle } from 'components/dashboard/components/Dashboard.Title';

const ProjectOverview: FC = () => {
    const recentProjects = useRecentProjects();

    return (
        <Page
            title="Projects"
            hideFooter
            showBackground
            breadcrumbs={{
                rightSection: <ProjectCreateButton />,
            }}
        >
            <Page.WideContent>
                <Dashboard.Checklist />

                {recentProjects?.length > 0 && (
                    <Stack gap="xs">
                        <DashboardTitle>Recent projects</DashboardTitle>
                        <ProjectGrid projects={recentProjects.slice(0, 4)} />
                    </Stack>
                )}
                <Stack gap="xs">
                    <DashboardTitle>All projects</DashboardTitle>
                    <ProjectSearch fullWidth>
                        <Stack gap="xs">
                            <Group gap="xs">
                                <ProjectSearch.Query />
                                <ProjectSearch.Teams />
                                <ProjectSearch.Customers />
                                <ProjectSearch.Tags />
                                <ProjectSearch.ReferenceDesigns />
                            </Group>
                            <ProjectSearch.Results>
                                {({ projects, isLoading, isLoadingMore, mutate }) => (
                                    <React.Fragment>
                                        <ProjectGrid
                                            projects={projects}
                                            isLoading={isLoading}
                                            showCreate
                                            showFeatureTracker
                                            onProjectDelete={async () => {
                                                mutate();
                                            }}
                                        />
                                        {isLoadingMore && <Loader size="sm" />}
                                    </React.Fragment>
                                )}
                            </ProjectSearch.Results>
                        </Stack>
                    </ProjectSearch>
                </Stack>
            </Page.WideContent>

            <Page.Footer />
        </Page>
    );
};

export { ProjectOverview };
