import { FC } from 'react';

import { But<PERSON> } from '@mantine/core';
import { TbPlus } from 'react-icons/tb';

import { useCurrentUser } from 'hooks/use-current-user';

import { ProjectService } from 'services/ProjectService';
import { UserService } from 'services/UserService';

const ProjectCreateButton: FC<{ label?: string }> = ({ label }) => {
    const user = useCurrentUser();
    const currentTeam = user?.team;

    const redirectToCreate = async (team?: string) => {
        if (team && team !== currentTeam) {
            await UserService.switchTeam(team);
        }

        await ProjectService.navigate.create();
    };

    return (
        <Button
            onClick={() => redirectToCreate()}
            size="xs"
            variant="filled"
            leftSection={<TbPlus size={14} strokeWidth={1.25} />}
        >
            {label || 'Create project'}
        </Button>
    );
};

export { ProjectCreateButton };
