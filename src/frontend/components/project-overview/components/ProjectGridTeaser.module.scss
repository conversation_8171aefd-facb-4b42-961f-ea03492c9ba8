.wrapper {
    position: relative;

    display: flex;
    align-items: stretch;
    justify-content: stretch;

    min-height: 200px;

    [data-project-action] {
        position: absolute;
        top: var(--mantine-spacing-xs);
        right: var(--mantine-spacing-xs);

        pointer-events: none;
        opacity: 0;

        &[data-expanded='true'] {
            pointer-events: all;
            opacity: 1;
        }
    }

    &:hover {
        [data-project-action] {
            pointer-events: all;
            opacity: 1;
        }
    }
}

.card {
    flex-grow: 1;

    transition: box-shadow 200ms;

    [data-project-name] {
        margin-right: var(--mantine-spacing-lg);
    }
}
