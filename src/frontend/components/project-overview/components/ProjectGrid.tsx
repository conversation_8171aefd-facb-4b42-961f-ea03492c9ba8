import React, { FC } from 'react';

import { Box, Group, MantineStyleProps, Text } from '@mantine/core';

import { FeatureLimit, getSubscriptionForUnlimitedFeature, Project } from 'models';

import { GridSection, GridSectionProps } from 'components/section/GridSection';
import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

import { ProjectGridTeaser } from './ProjectGridTeaser';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';

import { DashboardEmptyProjects } from 'components/dashboard/components/Dashboard.EmptyProjects';
import { DashboardEmptyReferenceDesigns } from 'components/dashboard/components/Dashboard.EmptyReferenceDesigns';

const ProjectGrid: FC<{
    projects: Project[];
    isLoading?: boolean;
    showCreate?: boolean;
    showFeatureTracker?: boolean;
    onProjectDelete?: () => Promise<void>;
    nbCols?: GridSectionProps['nbCols'];
    cols?: GridSectionProps['cols'];
    type?: 'project' | 'reference design';
}> = ({
    projects,
    isLoading = false,
    showCreate = true,
    showFeatureTracker = false,
    onProjectDelete,
    nbCols = 4,
    cols,
    type = 'project',
}) => {
    const plan = useCurrentTeamSubscription();
    const unlimitedPlan = getSubscriptionForUnlimitedFeature(FeatureLimit.SIMULATIONS);

    if (!showCreate && projects.length === 0) {
        return null;
    }

    return (
        <Box style={{ width: '100%' }}>
            {!projects.length &&
                !isLoading &&
                (type === 'project' ? <DashboardEmptyProjects /> : <DashboardEmptyReferenceDesigns />)}

            <GridSection nbCols={nbCols} cols={cols}>
                {!!projects.length && plan !== unlimitedPlan && showFeatureTracker && (
                    <FeatureLimitTracker feature={FeatureLimit.PROJECTS} p="md" h="100%" />
                )}

                {projects.map((project) => (
                    <ProjectGridTeaser key={project.id} project={project} onProjectDelete={onProjectDelete} />
                ))}
            </GridSection>
        </Box>
    );
};

export const ProjectMetaItem: FC<{
    icon: React.ReactNode;
    label: string;
    c?: MantineStyleProps['c'];
}> = ({ icon, label, c = 'default' }) => {
    return (
        <Group gap="xs" align="center" c={c}>
            {icon}
            <Text style={{ lineHeight: '1rem' }} fz="sm" inherit>
                {label}
            </Text>
        </Group>
    );
};

export { ProjectGrid };
