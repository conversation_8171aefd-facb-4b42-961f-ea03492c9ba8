import { DateService } from 'services/DateService';
import { Diagram, DiagramTeaser } from 'models';

const getDiagramMeta = (diagram: Diagram | DiagramTeaser) => {
    const { updatedAt, createdAt } = diagram;

    const createdAtLabel = createdAt ? DateService.formatDistanceToNow(createdAt) : '';
    const updatedAtLabel = updatedAt ? DateService.formatDistanceToNow(updatedAt) : null;
    const updatedWhen = updatedAtLabel || createdAtLabel;

    return {
        updatedWhen,
    };
};

export { getDiagramMeta };
