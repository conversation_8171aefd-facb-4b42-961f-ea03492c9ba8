import { getId } from 'helpers/getId';
import { DateService } from 'services/DateService';
import { Project } from 'models';
import { useUser } from 'hooks/use-user';
import { useTeam } from 'hooks/use-team';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

const useProjectMeta = (project: Project) => {
    const { updatedBy, createdBy, updatedAt, createdAt, team: teamId } = project;

    const { team } = useTeam(teamId);

    const projectUrl = ProjectHelpers.urls.editor(project.id);

    const updatedById = updatedBy ? getId(updatedBy) : getId(createdBy);
    const { user: updatedByUser } = useUser(updatedById!);

    const createdAtLabel = createdAt ? DateService.formatDistanceToNow(createdAt) : null;
    const updatedAtLabel = updatedAt ? DateService.formatDistanceToNow(updatedAt) : null;
    const updatedWhen = updatedAtLabel || createdAtLabel;

    return {
        projectUrl,
        updatedBy: updatedById ? (updatedByUser?.name ?? updatedByUser?.email) : '',
        updatedWhen,
        teamName: team?.name,
    };
};

export { useProjectMeta };
