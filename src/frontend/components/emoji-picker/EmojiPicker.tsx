import React, { <PERSON> } from 'react';

import { EmojiPicker } from 'frimousse';
import { Popover, ScrollArea } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import cx from './EmojiPicker.module.css';

const InternalEmojiPicker: FC<{
    onEmojiSelect: (emoji: string) => void;
    children: React.ReactNode;
}> = ({ onEmojiSelect, children }) => {
    const [opened, { toggle, close }] = useDisclosure();

    return (
        <Popover
            opened={opened}
            onChange={() => {
                close();
            }}
            withArrow
        >
            {/* @ts-expect-error: Mantine types are wrong */}
            <Popover.Target onClick={toggle}>{children}</Popover.Target>
            <Popover.Dropdown className={cx.dropdown}>
                <EmojiPicker.Root
                    onEmojiSelect={(emoji) => {
                        onEmojiSelect(emoji.emoji);
                        close();
                    }}
                >
                    <EmojiPicker.Search />
                    <ScrollArea mt={4} h={320}>
                        <EmojiPicker.Viewport>
                            <EmojiPicker.Loading>Loading…</EmojiPicker.Loading>
                            <EmojiPicker.Empty>No emoji found.</EmojiPicker.Empty>
                            <EmojiPicker.List />
                        </EmojiPicker.Viewport>
                    </ScrollArea>
                </EmojiPicker.Root>
            </Popover.Dropdown>
        </Popover>
    );
};

export { InternalEmojiPicker as EmojiPicker };
