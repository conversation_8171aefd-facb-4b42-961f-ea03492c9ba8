import React, { FC } from 'react';

import { Badge, Card, Group, Modal, Stack } from '@mantine/core';

import { IntercomChannel, IntercomMessage } from 'models';

import { useUser } from 'hooks/use-user';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useIntercomChannelMessages } from 'components/intercom/hooks/use-intercom-channel-messages';

import { DateService } from 'services/DateService';

import { TipTapViewer } from 'components/tiptap/TipTapViewer';

const IntercomChannelModal: FC<{ channel: IntercomChannel; closeChannel: () => void }> = ({
    channel,
    closeChannel,
}) => {
    const { messages } = useIntercomChannelMessages(channel.id);

    return (
        <Modal opened onClose={closeChannel} size="xl" withCloseButton={false}>
            <Stack gap="md">
                {messages.map((message) => (
                    <IntercomChannelMessage key={message.id} message={message} />
                ))}
            </Stack>
        </Modal>
    );
};

const IntercomChannelMessage: FC<{ message: IntercomMessage }> = ({ message }) => {
    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    const { content, files } = message.content[0];

    const renderName = () => {
        if (company) {
            return (
                <>
                    <Badge>{`${company.name}`}</Badge> {user?.name} ∙ {user?.email}
                </>
            );
        } else if (user) {
            return (
                <>
                    <Badge variant="outline">{`${user.name}`}</Badge> {user?.email}
                </>
            );
        }
    };

    return (
        <Card withBorder bg="gray.0" radius="xs">
            <Group gap={6} fz="xs" c="dimmed" mb="xs">
                {renderName()} ∙ {DateService.format(message.createdAt)}
            </Group>
            <TipTapViewer content={JSON.parse(content)} files={files} />
        </Card>
    );
};

export { IntercomChannelModal };
