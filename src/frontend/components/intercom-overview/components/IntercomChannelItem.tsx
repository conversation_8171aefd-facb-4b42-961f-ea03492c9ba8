import React, { FC } from 'react';

import { <PERSON><PERSON>, <PERSON>ton, Card, Grid, Group, Stack, Text } from '@mantine/core';
import { IoBriefcaseOutline, IoCalendarOutline, IoChatbubblesOutline, IoPersonOutline } from 'react-icons/io5';

import { IntercomChannel, IntercomMessage } from 'models';

import { useUser } from 'hooks/use-user';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useIntercomChannelMessages } from 'components/intercom/hooks/use-intercom-channel-messages';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { DateService } from 'services/DateService';

import { IconWithText } from 'elements/IconWithText';
import { TipTapViewer } from 'components/tiptap/TipTapViewer';

const IntercomChannelItem = ({ channel, openChannel }: { channel: IntercomChannel; openChannel: () => void }) => {
    const { messages } = useIntercomChannelMessages(channel.id);

    const { user } = useUser(channel.createdBy);
    const { company } = useCompanyProfile(channel.company);

    return (
        <Card>
            <Grid gutter="xl" w="100%" columns={10}>
                <Grid.Col span={2}>
                    <Stack gap="md" align="start">
                        <Stack gap={0}>
                            <IconWithText icon={<IoCalendarOutline />} text={DateService.format(channel.createdAt)} />

                            <IconWithText icon={<IoPersonOutline />} text={`${user?.name} (${user?.email})`} />

                            {company && (
                                <IconWithText
                                    icon={<IoBriefcaseOutline />}
                                    text={
                                        <Anchor
                                            href={CompanyProfileHelpers.urls.view(company.slug)}
                                            fw={600}
                                            target="_blank"
                                        >
                                            {company.name}
                                        </Anchor>
                                    }
                                />
                            )}

                            <IconWithText icon={<IoChatbubblesOutline />} text={`${messages.length} message(s)`} />
                        </Stack>
                    </Stack>
                </Grid.Col>

                <Grid.Col span={6}>
                    <IntercomChannelMessages channel={channel} />

                    <Button size="xs" variant="outline" onClick={openChannel} mt="md">
                        View full conversation
                    </Button>
                </Grid.Col>
            </Grid>
        </Card>
    );
};

const IntercomChannelMessages: FC<{ channel: IntercomChannel }> = ({ channel }) => {
    const { messages } = useIntercomChannelMessages(channel.id);

    const latest3Messages = messages.slice(-3);
    const hasMoreMessages = messages.length > 3;

    return (
        <Stack gap="md">
            {hasMoreMessages && (
                <Text c="dimmed" fz="xs">
                    +{messages.length - 3} older messages
                </Text>
            )}

            {latest3Messages.map((message) => (
                <IntercomChannelMessage key={message.id} message={message} />
            ))}
        </Stack>
    );
};

const IntercomChannelMessage: FC<{ message: IntercomMessage }> = ({ message }) => {
    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    return (
        <Stack gap={4}>
            <Group gap="xs" fw={600}>
                {company?.name || user?.name}
                <Text span c="dimmed" fz="xs">
                    {DateService.format(message.createdAt)}
                </Text>
            </Group>

            <TipTapViewer content={JSON.parse(message.content[0].content)} files={message.content[0].files} />
        </Stack>
    );
};

export { IntercomChannelItem };
