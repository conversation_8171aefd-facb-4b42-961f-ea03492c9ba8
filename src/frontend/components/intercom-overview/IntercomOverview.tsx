import React, { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';

import { Button, Group, Loader, Pagination, Stack, Switch, Text } from '@mantine/core';
import { useDebouncedCallback } from '@mantine/hooks';

import { IntercomChannel, IntercomChannelListParams } from 'models';

import { useIntercomChannels } from 'components/intercom-overview/hooks/use-intercom-channels';

import { RouterService } from 'services/RouterService';

import { IntercomChannelItem } from 'components/intercom-overview/components/IntercomChannelItem';
import { IntercomChannelModal } from 'components/intercom-overview/components/IntercomChannelModal';

type IntercomChannelFilters = Partial<IntercomChannelListParams>;

const IntercomOverview: FC = () => {
    const { query } = useRouter();

    const [filters, setFilters] = useState<IntercomChannelFilters>({
        withoutInternal: false,
        page: 1,
    });

    const debouncedRouterUpdate = useDebouncedCallback((filters: IntercomChannelFilters) => {
        RouterService.setQuery('filters', JSON.stringify(filters), 'replace');
    }, 300);

    const { withoutInternal, page } = filters;

    useEffect(() => {
        const filters = query.filters ? JSON.parse(query.filters as string) : {};

        setFilters((currentFilters) => ({
            ...currentFilters,
            ...filters,
            page: filters.page ?? 1,
        }));
    }, [query.filters]);

    const intercomChannelFilters: IntercomChannelFilters = {
        page,
        withoutInternal,
    };

    const { intercomChannels, totalPages, totalDocs, isLoading } = useIntercomChannels(intercomChannelFilters);

    const [openedChannel, setOpenedChannel] = useState<IntercomChannel | null>(null);

    useEffect(() => {
        const channelId = query.intercomChannel;

        if (!channelId) {
            setOpenedChannel(null);
            return;
        }

        const channel = intercomChannels.find((channel) => channel.id === channelId);

        if (!channel) {
            setOpenedChannel(null);
            return;
        }

        setOpenedChannel(channel);
    }, [query.intercomChannel, intercomChannels]);

    if (!intercomChannels) return null;

    const setFilter = (
        key: keyof IntercomChannelFilters,
        value: IntercomChannelFilters[keyof IntercomChannelFilters],
    ) => {
        const newFilters: IntercomChannelFilters = {
            ...filters,
            page: 1,
            [key]: value,
        };

        setFilters(newFilters);
        debouncedRouterUpdate(newFilters);
    };

    const setPage = (newPage: number) => {
        setFilter('page', newPage);
        debouncedRouterUpdate({ ...filters, page: newPage });
    };

    const openChannel = (channel: IntercomChannel) => {
        RouterService.setQuery('intercomChannel', channel.id, 'replace');
    };

    const closeChannel = () => {
        RouterService.removeQuery('intercomChannel', 'replace');
    };

    return (
        <Stack gap="xs">
            <Button.Group mx="auto">
                <Button variant="filled">Intercom chats</Button>
                <Button variant="outline" component={Link} href="/admin/diagram-chats">
                    Diagram chats
                </Button>
            </Button.Group>

            <Group>
                <Switch
                    checked={withoutInternal}
                    onChange={(e) => setFilter('withoutInternal', e.currentTarget.checked)}
                    label="Not started by internal"
                />
            </Group>

            {totalDocs === 0 ? (
                <Text c="dimmed" fz="xs">
                    No results
                </Text>
            ) : (
                <Text c="dimmed" fz="xs">
                    {totalDocs} results
                </Text>
            )}

            {isLoading && <Loader size="sm" my="md" />}

            {!isLoading &&
                intercomChannels.map((channel) => (
                    <IntercomChannelItem key={channel.id} channel={channel} openChannel={() => openChannel(channel)} />
                ))}

            {totalPages > 1 && <Pagination size="sm" mt="md" value={page} onChange={setPage} total={totalPages} />}

            {openedChannel && <IntercomChannelModal channel={openedChannel} closeChannel={closeChannel} />}
        </Stack>
    );
};

export { IntercomOverview };
