import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { IntercomChannel, IntercomChannelListParams } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { IntercomService } from 'services/IntercomService';

type UseIntercomChannels = (props: Partial<IntercomChannelListParams>) => SWRResponse & {
    intercomChannels: IntercomChannel[];
    totalPages: number;
    totalDocs: number;
};

const useIntercomChannels: UseIntercomChannels = (props) => {
    const fetcher = async () => {
        return IntercomService.adminList(props);
    };

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/intercomChannels/admin-list?query=${JSON.stringify(props)}`,
            fetcher,
            condition: true,
        }),
    );

    return {
        ...swr,
        intercomChannels: swr?.data?.docs || [],
        totalPages: swr?.data?.totalPages || 1,
        totalDocs: swr?.data?.totalDocs || 0,
    };
};

export { useIntercomChannels };
