'use client';

import { useEffect, useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Button, Divider, Flex, Group, Modal, Stack, Text, TextInput } from '@mantine/core';

import { CompanyProfile, ExhibitorMatch, SearchThread } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { ExhibitorMatchService } from 'services/ExhibitorMatchService';

import { useAction } from 'hooks/use-action';
import { useRTE } from 'hooks/use-rte';
import { useUser } from 'hooks/use-user';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { ManufacturerInput } from 'components/inputs/ManufacturerInput';

const ExhibitorMatchShareLead = ({
    companies,
    exhibitorMatch,
    searchThread,
}: {
    companies?: CompanyProfile[];
    exhibitorMatch?: ExhibitorMatch;
    searchThread: SearchThread;
}) => {
    const [shareWithCompany, setShareWithCompany] = useState<string | null>(null);

    if (!searchThread.email && !searchThread.createdBy) {
        return <Text c="orange">No email or user associated with this lead</Text>;
    }

    if (!exhibitorMatch) {
        return <Text c="orange">User hasn't requested to connect with companies</Text>;
    }

    const extraCompanies = exhibitorMatch.sentToCompanies?.map((company) => company.company);

    return (
        <Stack>
            <Group fw={600} gap={8}>
                <Badge size="xs" radius="xs" variant="gradient">
                    Suggested
                </Badge>
                Share lead with{' '}
            </Group>

            {!companies?.length && (
                <Text c="dimmed" fz="sm">
                    No suggested companies
                </Text>
            )}

            {companies?.map((company) => (
                <Company
                    key={company.id}
                    companyId={company.id}
                    exhibitorMatch={exhibitorMatch}
                    handleShare={setShareWithCompany}
                />
            ))}

            <Divider />
            <Text fw={600}>Also shared with</Text>

            {extraCompanies?.map((id) => (
                <Company key={id} companyId={id} exhibitorMatch={exhibitorMatch} handleShare={setShareWithCompany} />
            ))}

            <ManufacturerInput
                value=""
                showClaimed
                onOptionSubmit={async (value) => {
                    setShareWithCompany(value);
                }}
            />

            {shareWithCompany && (
                <ShareLeadModal
                    companyId={shareWithCompany}
                    handleClose={() => setShareWithCompany(null)}
                    exhibitorMatch={exhibitorMatch}
                    searchThread={searchThread}
                />
            )}
        </Stack>
    );
};

const Company = ({
    companyId,
    exhibitorMatch,
    handleShare,
}: {
    companyId: string;
    exhibitorMatch: ExhibitorMatch;
    handleShare: (companyId: string) => void;
}) => {
    const { company } = useCompanyProfile(companyId);

    if (!company) return null;

    const isSharedWithCompany = exhibitorMatch.sentToCompanies?.some((company) => company.company === companyId);

    return (
        <Flex align="center" gap="xs">
            <div style={{ wordBreak: 'break-word' }}>
                <Anchor fw={600} href={CompanyProfileHelpers.urls.view(company.slug)} target="_blank">
                    {company.name}{' '}
                </Anchor>
                {!company.internal && (
                    <Text span c="green" fw={600} fz="xs" td="none">
                        Claimed
                    </Text>
                )}
            </div>

            <Button
                size="compact-xs"
                color="green"
                onClick={() => handleShare(company.id)}
                disabled={isSharedWithCompany}
                ml="auto"
                style={{
                    flexShrink: 0,
                }}
            >
                {isSharedWithCompany ? 'Shared' : 'Share'}
            </Button>
        </Flex>
    );
};

const ShareLeadModal = ({
    companyId,
    handleClose,
    exhibitorMatch,
    searchThread,
}: {
    companyId: string;
    handleClose: () => void;
    exhibitorMatch: ExhibitorMatch;
    searchThread: SearchThread;
}) => {
    const { RTEField, editor } = useRTE();
    const text = editor?.getText();

    const { company } = useCompanyProfile(companyId);

    const { user: createdBy } = useUser(searchThread.createdBy);

    const requirements = exhibitorMatch.requirements;

    const email = createdBy?.email ?? searchThread.email;

    useEffect(() => {
        if (editor && requirements) {
            editor.commands.setContent(requirements);
        }
    }, [editor, requirements]);

    const [handleSendLead, sending] = useAction(async () => {
        await ExhibitorMatchService.createExhibitorMatchLead({
            exhibitorMatch: exhibitorMatch.id,
            company: companyId,
            requirements: editor?.getJSON(),
            email,
            event: exhibitorMatch.event,
        });

        handleClose();
    });

    if (!company) {
        return null;
    }

    return (
        <Modal opened onClose={handleClose} title={`Share lead with ${company.name}`} size="xl">
            <Stack>
                {email ? (
                    <>
                        <TextInput type="email" label="Email" disabled defaultValue={email} />
                        <Alert color="orange">
                            We will never share the email directly with the company. The company will be able to start a
                            chat conversation with the lead if they have a DCIDE account. Otherwise they will be able to
                            invite the lead to the DCIDE platform.
                        </Alert>
                    </>
                ) : (
                    <Alert color="orange">
                        No email was provided for this lead, you can still share the requirements with the company.
                    </Alert>
                )}

                {editor && <RTEField editor={editor} />}
                <Alert color="orange">
                    We will send the lead with the above text to {company.name}. The company will get a notification, an
                    email and will be able to see this lead in their lead management center.
                </Alert>

                <Button fullWidth size="xl" disabled={!text} onClick={handleSendLead} loading={sending}>
                    Send lead to {company.name}
                </Button>
            </Stack>
        </Modal>
    );
};

export { ExhibitorMatchShareLead };
