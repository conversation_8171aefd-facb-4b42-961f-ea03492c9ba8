import { Text } from '@mantine/core';

import { Article, CompanyProfile, Component, SearchThread } from 'models';

import { useArticlesByIds } from 'hooks/use-artices-by-ids';
import { useProfilesByIds } from 'hooks/use-profiles-by-ids';
import { useProductsByIds } from 'hooks/use-products-by-ids';
import { useExhibitorMatch } from 'components/exhibitor-match/hooks/use-exhibitor-match';

import { HorizontalTabs } from 'components/horizontal-tabs';
import { GridSection } from 'components/section/GridSection';
import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';
import { ArticleTeaser } from 'components/teasers/ArticleTeaser';
import { ComponentOverviewHit } from 'components/component-overview';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';
import { ExhibitorMatchSearchThread } from 'components/exhibitor-match/components/ExhibitorMatchSearchThread';

const ExhibitorMatchModal = ({ searchThread }: { searchThread: SearchThread }) => {
    const { exhibitorMatch } = useExhibitorMatch(searchThread.exhibitorMatch);

    const summary = exhibitorMatch?.requirements;
    const analysis = exhibitorMatch?.summarizeMatchResult;

    const { components: products } = useProductsByIds(exhibitorMatch?.productSearchResults ?? []);
    const { profiles: companies } = useProfilesByIds(exhibitorMatch?.companySearchResults ?? []);
    const { articles: caseStudies } = useArticlesByIds(exhibitorMatch?.caseStudiesResults ?? []);

    return (
        <HorizontalTabs
            tabs={[
                {
                    value: 'chat',
                    label: 'Chat',
                    content: <ExhibitorMatchSearchThread thread={searchThread} />,
                },
                {
                    value: 'requirements',
                    label: 'Requirements',
                    content: summary ? <AIMarkdown>{summary}</AIMarkdown> : <Text c="dimmed">No requirements</Text>,
                    disabled: !summary,
                },
                {
                    value: 'analysis',
                    label: 'Analysis',
                    content: analysis ? <AIMarkdown>{analysis}</AIMarkdown> : <Text c="dimmed">No analysis</Text>,
                    disabled: !analysis,
                },
                {
                    value: 'products',
                    label: 'Products',
                    content: <Products products={products} />,
                    disabled: !products?.length,
                },
                {
                    value: 'companies',
                    label: 'Companies',
                    content: <Companies companies={companies} />,
                    disabled: !companies?.length,
                },
                {
                    value: 'caseStudies',
                    label: 'Case Studies',
                    content: <CaseStudies caseStudies={caseStudies} />,
                    disabled: !caseStudies?.length,
                },
            ]}
        />
    );
};

const Products = ({ products }: { products?: Component[] }) => {
    if (!products?.length) {
        return <Text c="dimmed">No products</Text>;
    }

    return (
        <GridSection nbCols={2}>
            {products?.map((product) => (
                <ComponentOverviewHit key={product.id} component={product} />
            ))}
        </GridSection>
    );
};

const Companies = ({ companies }: { companies?: CompanyProfile[] }) => {
    if (!companies?.length) {
        return <Text c="dimmed">No companies</Text>;
    }

    return (
        <GridSection nbCols={2}>
            {companies?.map((company) => (
                <CompanyProfileTeaser key={company.id} company={company} />
            ))}
        </GridSection>
    );
};

const CaseStudies = ({ caseStudies }: { caseStudies?: Article[] }) => {
    if (!caseStudies?.length) {
        return <Text c="dimmed">No case studies</Text>;
    }

    return (
        <GridSection nbCols={2}>
            {caseStudies?.map((caseStudy) => (
                <ArticleTeaser key={caseStudy.id} article={caseStudy} />
            ))}
        </GridSection>
    );
};

export { ExhibitorMatchModal };
