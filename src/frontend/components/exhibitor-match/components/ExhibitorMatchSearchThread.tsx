import React from 'react';

import { Bad<PERSON>, Card, Stack } from '@mantine/core';

import { SearchThread } from 'models';

import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';
import { SearchAgentHelpers } from 'helpers/SearchAgentHelpers';

const ExhibitorMatchSearchThread = ({ thread }: { thread: SearchThread }) => {
    return (
        <Stack gap="md">
            {thread.thread?.input
                .filter((msg) => msg.role === 'assistant' || msg.role === 'user')
                .map((msg) => {
                    const { markdownText } = SearchAgentHelpers.parseTextToMarkdownAndReferences(msg.content ?? '');

                    return (
                        <Card withBorder bg="gray.0" radius="xs" key={msg.id}>
                            <Badge mb="xs">{msg.role}</Badge>
                            <AIMarkdown>{markdownText}</AIMarkdown>
                        </Card>
                    );
                })}
        </Stack>
    );
};

export { ExhibitorMatchSearchThread };
