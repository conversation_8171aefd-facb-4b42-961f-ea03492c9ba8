import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { SearchThread } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { SearchAgentService, SearchThreadListProps } from 'services/AISearchAgentService';

type UseSearchThreads = (props?: SearchThreadListProps) => SWRResponse & {
    searchThreads: SearchThread[];
    totalPages: number;
    totalDocs: number;
};

const useSearchThreads: UseSearchThreads = (props = {}) => {
    const fetcher = async () => {
        return SearchAgentService.list(props);
    };

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: SearchAgentService.swr.searchThreads(SearchAgentService.getListParams(props)),
            fetcher,
            condition: true,
        }),
    );

    return {
        ...swr,
        searchThreads: swr?.data?.docs || [],
        totalPages: swr?.data?.totalPages || 1,
        totalDocs: swr?.data?.totalDocs || 0,
    };
};

export { useSearchThreads };
