import { useSnapshot } from 'hooks/use-safe-snapshot';

import { exhibitorMatchState } from 'components/exhibitor-match/state/exhibitor-match-state';

const useExhibitorMatchState = () => {
    const setCheckedInternal = (id: string, checked: boolean) => {
        exhibitorMatchState.checkedInternal = {
            ...exhibitorMatchState.checkedInternal,
            [id]: checked,
        };
    };

    const snapshot = useSnapshot(exhibitorMatchState);

    return {
        ...snapshot,
        setCheckedInternal,
    };
};

export { useExhibitorMatchState };
