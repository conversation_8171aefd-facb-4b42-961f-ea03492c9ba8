import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { ExhibitorMatch } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { ExhibitorMatchService } from 'services/ExhibitorMatchService';

type UseSearchThreads = (exhibitorMatchId?: string | null) => SWRResponse & {
    exhibitorMatch?: ExhibitorMatch;
};

const useExhibitorMatch: UseSearchThreads = (exhibitorMatchId) => {
    const fetcher = async () => {
        return ExhibitorMatchService.getExhibitorMatch(exhibitorMatchId!);
    };

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: ExhibitorMatchService.swr.exhibitorMatch(exhibitorMatchId!),
            fetcher: fetcher,
            condition: !!exhibitorMatchId,
        }),
    );

    return {
        ...swr,
        exhibitorMatch: swr?.data,
    };
};

export { useExhibitorMatch };
