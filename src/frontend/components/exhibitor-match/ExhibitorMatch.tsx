import React, { FC, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import DayJS from 'dayjs';

import {
    Button,
    Card,
    Checkbox,
    Grid,
    Group,
    Kbd,
    Loader,
    Modal,
    Pagination,
    Select,
    Stack,
    Switch,
    Text,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useDebouncedCallback } from '@mantine/hooks';

import { IoCalendarOutline, IoPersonOutline } from 'react-icons/io5';

import { SearchThread } from 'models';

import { DateService } from 'services/DateService';
import { RouterService } from 'services/RouterService';
import { SearchAgentService, SearchThreadListProps } from 'services/AISearchAgentService';

import { useUser } from 'hooks/use-user';
import { useEvent } from 'hooks/use-event';
import { useEvents } from 'hooks/use-events';
import { useProfilesByIds } from 'hooks/use-profiles-by-ids';
import { useSearchThreads } from 'components/exhibitor-match/hooks/use-search-threads';
import { useExhibitorMatch } from 'components/exhibitor-match/hooks/use-exhibitor-match';
import { useExhibitorMatchState } from 'components/exhibitor-match/hooks/use-exhibitor-match-state';

import { IconWithText } from 'elements/IconWithText';
import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';
import { ExhibitorMatchModal } from 'components/exhibitor-match/components/ExhibitorMatchModal';
import { ExhibitorMatchShareLead } from 'components/exhibitor-match/components/ExhibitorMatchShareLead';

import '@mantine/dates/styles.css';

type ExhibitorMatchFilters = {
    withoutInternal: boolean;
    withExhibitorMatch: boolean;
    removeEmptyThreads: boolean;
    onlyWithEmail: boolean;
    dateRange: [Date | null, Date | null];
    eventId?: string | null;
    page: number;
};

const ExhibitorMatch: FC = () => {
    const { query } = useRouter();
    const { events } = useEvents();

    const [filters, setFilters] = useState<ExhibitorMatchFilters>({
        withoutInternal: true,
        withExhibitorMatch: true,
        removeEmptyThreads: true,
        onlyWithEmail: false,
        dateRange: [DayJS().endOf('day').subtract(14, 'days').toDate(), DayJS().endOf('day').toDate()],
        eventId: null,
        page: 1,
    });

    const debouncedRouterUpdate = useDebouncedCallback((filters: ExhibitorMatchFilters) => {
        RouterService.setQuery('filters', JSON.stringify(filters), 'replace');
    }, 300);

    const { withoutInternal, withExhibitorMatch, removeEmptyThreads, onlyWithEmail, dateRange, page, eventId } =
        filters;

    useEffect(() => {
        const filters = query.filters ? JSON.parse(query.filters as string) : {};

        const { dateRange, ...rest } = filters;

        setFilters((currentFilters) => ({
            ...currentFilters,
            ...rest,
            dateRange: dateRange
                ? [dateRange[0] ? new Date(dateRange[0]) : null, dateRange[1] ? new Date(dateRange[1]) : null]
                : currentFilters.dateRange,
            eventId: filters.eventId,
            page: filters.page ?? 1,
        }));
    }, [query.filters]);

    const searchThreadParams: SearchThreadListProps = {
        page,
        withExhibitorMatch,
        withoutInternal,
        removeEmptyThreads,
        onlyWithEmail,
        fromDate: dateRange[0]?.getTime(),
        toDate: dateRange[1]?.getTime(),
        eventId,
    };

    const { searchThreads, totalPages, totalDocs, isLoading } = useSearchThreads(searchThreadParams);

    const [openedThread, setOpenedThread] = useState<SearchThread | null>(null);

    useEffect(() => {
        const threadId = query.thread;

        if (!threadId) {
            setOpenedThread(null);
            return;
        }

        const thread = searchThreads.find((thread) => thread.id === threadId);

        if (!thread) {
            setOpenedThread(null);
            return;
        }

        setOpenedThread(thread);
    }, [query.thread, searchThreads]);

    if (!searchThreads) return null;

    const setFilter = (key: keyof ExhibitorMatchFilters, value: ExhibitorMatchFilters[keyof ExhibitorMatchFilters]) => {
        const newFilters: ExhibitorMatchFilters = {
            ...filters,
            page: 1,
            [key]: value,
        };

        setFilters(newFilters);
        debouncedRouterUpdate(newFilters);
    };

    const setPage = (newPage: number) => {
        setFilter('page', newPage);
        debouncedRouterUpdate({ ...filters, page: newPage });
    };

    const openThread = (thread: SearchThread) => {
        RouterService.setQuery('thread', thread.id, 'replace');
    };

    const closeThread = () => {
        RouterService.removeQuery('thread', 'replace');
    };

    return (
        <Stack gap="xs">
            <Group>
                <Switch
                    checked={withExhibitorMatch}
                    onChange={(e) => setFilter('withExhibitorMatch', e.currentTarget.checked)}
                    label="Only with requirements"
                />

                <Switch
                    checked={withoutInternal}
                    onChange={(e) => setFilter('withoutInternal', e.currentTarget.checked)}
                    label="Without internal"
                />

                <Switch
                    checked={removeEmptyThreads}
                    onChange={(e) => setFilter('removeEmptyThreads', e.currentTarget.checked)}
                    label="Remove empty threads"
                />

                <Switch
                    checked={onlyWithEmail}
                    onChange={(e) => setFilter('onlyWithEmail', e.currentTarget.checked)}
                    label="Only with email"
                />

                <Select
                    placeholder="Select event"
                    value={filters.eventId}
                    onChange={(value) => setFilter('eventId', value)}
                    data={
                        events?.map((event) => ({
                            value: event.id,
                            label: event.name,
                        })) || []
                    }
                    clearable
                />

                <DatePickerInput
                    type="range"
                    value={dateRange}
                    onChange={(range) => {
                        const from = range[0] ? new Date(range[0]) : null;
                        const to = range[1] ? new Date(range[1]) : null;

                        setFilter('dateRange', [from, to]);
                    }}
                    allowSingleDateInRange
                    placeholder="Select a date range"
                    leftSection={<IoCalendarOutline />}
                />
            </Group>

            {totalDocs === 0 ? (
                <Text c="dimmed" fz="xs">
                    No results
                </Text>
            ) : (
                <Text c="dimmed" fz="xs">
                    {totalDocs} results
                </Text>
            )}

            {isLoading && <Loader size="sm" my="md" />}

            {!isLoading &&
                searchThreads.map((thread) => (
                    <ExhibitorMatchItem key={thread.id} searchThread={thread} openThread={() => openThread(thread)} />
                ))}

            {totalPages > 1 && <Pagination size="sm" mt="md" value={page} onChange={setPage} total={totalPages} />}

            {openedThread && (
                <Modal opened onClose={closeThread} size="xl" withCloseButton={false}>
                    <ExhibitorMatchModal searchThread={openedThread} />
                </Modal>
            )}
        </Stack>
    );
};

const ExhibitorMatchItem = ({ searchThread, openThread }: { searchThread: SearchThread; openThread: () => void }) => {
    const { user } = useUser(searchThread.createdBy);

    const { exhibitorMatch } = useExhibitorMatch(searchThread.exhibitorMatch);

    const { event } = useEvent(searchThread?.event);

    const firstMessage = searchThread.thread?.input.find((msg) => msg.role === 'user');

    const requirements = exhibitorMatch?.requirements;

    const { profiles: companies } = useProfilesByIds(exhibitorMatch?.companySearchResults ?? undefined);

    const { checkedInternal, setCheckedInternal } = useExhibitorMatchState();

    const isInternal = useMemo(() => {
        const createdByInternal = user?.internal;
        if (createdByInternal) return true;

        const localInternal = checkedInternal[searchThread.id];
        if (localInternal !== undefined) return localInternal;

        return searchThread.internal;
    }, [checkedInternal, searchThread.id, searchThread.internal, user?.internal]);

    return (
        <Card>
            <Grid gutter="xl" w="100%" columns={10}>
                <Grid.Col span={2}>
                    <Stack gap="md" align="start">
                        <Kbd c={firstMessage ? 'black' : 'red'}>{firstMessage?.content ?? 'No user message'}</Kbd>

                        <Stack gap={0}>
                            <IconWithText
                                icon={<IoCalendarOutline />}
                                text={DateService.format(searchThread.createdAt)}
                            />

                            {event && <IconWithText icon={<IoCalendarOutline />} text={event?.name} />}

                            <IconWithText
                                icon={<IoPersonOutline />}
                                text={
                                    user?.email ??
                                    (searchThread.email ? `${searchThread.email} (not logged in)` : 'anonymous')
                                }
                            />
                        </Stack>

                        <Checkbox
                            mt="md"
                            label={user?.internal ? 'Internal' : 'Mark as internal'}
                            checked={isInternal}
                            disabled={user?.internal}
                            onChange={async (e) => {
                                SearchAgentService.update(searchThread.id, {
                                    internal: e.currentTarget.checked,
                                });

                                setCheckedInternal(searchThread.id, e.currentTarget.checked);
                            }}
                        />
                    </Stack>
                </Grid.Col>
                <Grid.Col span={5}>
                    {requirements ? <AIMarkdown>{requirements}</AIMarkdown> : <Text c="dimmed">No requirements</Text>}
                    <Button size="xs" variant="outline" onClick={openThread} mt="xs">
                        See full analysis
                    </Button>
                </Grid.Col>
                <Grid.Col span={3}>
                    <ExhibitorMatchShareLead
                        exhibitorMatch={exhibitorMatch}
                        companies={companies}
                        searchThread={searchThread}
                    />
                </Grid.Col>
            </Grid>
        </Card>
    );
};

export { ExhibitorMatch };
