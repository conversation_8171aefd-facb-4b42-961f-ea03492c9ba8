import { proxy, subscribe } from 'valtio';

import { deepClone } from 'helpers/deep-clone';

import { DesignLibrarySearch } from 'components/design-library/types';
import { DesignLibraryService } from 'services/DesignLibraryService';

export const DEFAULT_DESIGN_LIBRARY_SEARCH: DesignLibrarySearch = {
    docs: [],
    page: 0,
    totalPages: 1,
    totalDesigns: 0,
    isLoading: false,
    error: false,
    searchBarQuery: undefined,
    filters: {
        search: undefined,
        manufacturer: undefined,
        inAppSupport: undefined,
        tags: [],
        grid: undefined,
        generation: undefined,
        load: undefined,
        storage: undefined,
    },
    showFilters: {
        grid: false,
        generation: false,
        load: false,
        storage: false,
    },
};

export type DesignLibrarySearchFilterKey = keyof DesignLibrarySearch['filters'];
export type DesignLibrarySearchShowFiltersKey = keyof DesignLibrarySearch['showFilters'];

const designLibrarySearch = proxy<DesignLibrarySearch>(deepClone(DEFAULT_DESIGN_LIBRARY_SEARCH));

subscribe(designLibrarySearch, (changes) => {
    changes.forEach((change) => {
        const [type, path] = change;

        if (type === 'set' && path.includes('filters')) {
            designLibrarySearch.page = 0;
            DesignLibraryService.debouncedSearch();
        }

        if (type === 'set' && path.join('.') === 'page') {
            DesignLibraryService.debouncedSearch();
        }
    });
});

export { designLibrarySearch };
