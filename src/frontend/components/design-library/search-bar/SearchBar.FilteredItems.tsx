import React from 'react';

import { Menu, Box, Group, Pill } from '@mantine/core';
import { IoCheckmark } from 'react-icons/io5';

import { TextHelpers } from 'helpers/TextHelpers';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';
import { SearchBarItem } from 'components/design-library/types';

import cx from './SearchBar.module.scss';

const SearchBarFilteredItems = ({
    label,
    items,
    showAsPills,
}: {
    label?: string;
    items: SearchBarItem[];
    showAsPills?: boolean;
}) => {
    const { searchBarQuery } = useDesignLibrarySearch();

    const filteredItems = items.filter(({ label }) => {
        return !searchBarQuery || TextHelpers.textMatches(label, searchBarQuery);
    });

    if (filteredItems.length === 0) return null;

    return (
        <Box className={cx.section}>
            {label && <Menu.Label>{label}</Menu.Label>}
            {showAsPills ? (
                <Group p="xs" pt={0} gap={6}>
                    {filteredItems.map(({ label, selected, ...props }, index) => (
                        <Pill
                            key={index}
                            {...props}
                            size="sm"
                            style={{
                                cursor: 'pointer',
                            }}
                            bg={selected ? 'gray.2' : 'gray.1'}
                            withRemoveButton={selected}
                            onRemove={props.onClick}
                        >
                            {label}
                        </Pill>
                    ))}
                </Group>
            ) : (
                <>
                    {filteredItems.map(({ label, selected, ...props }, index) => (
                        <Menu.Item
                            key={index}
                            {...props}
                            rightSection={selected ? <IoCheckmark /> : ''}
                            bg={selected ? 'gray.0' : 'transparent'}
                        >
                            {label}
                        </Menu.Item>
                    ))}
                </>
            )}
        </Box>
    );
};

export { SearchBarFilteredItems };
