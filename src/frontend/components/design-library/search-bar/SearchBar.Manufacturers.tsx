import React from 'react';

import { Box } from '@mantine/core';

import { CompanyLogo } from 'components/company-logo';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';
import { useDesignLibraryManufacturers } from 'components/design-library/hooks/use-design-library-manufacturers';

import { SearchBar } from './SearchBar';

const SearchBarManufcaturers = () => {
    const {
        filters: { manufacturer: selectedManufacturer },
    } = useDesignLibrarySearch();
    const { manufacturers, isLoading } = useDesignLibraryManufacturers();

    if (!manufacturers.length || isLoading) return null;

    return (
        <SearchBar.FilteredItems
            label="Profiles"
            items={manufacturers.map(({ id, name, logos }) => ({
                label: name,
                onClick: () => {
                    DesignLibraryService.setFilter('manufacturer', id);
                    DesignLibraryService.clearSearchBarQuery();
                },
                leftSection: <CompanyLogo logos={logos} width={20} fallback={<Box w={20} h={20} bg="gray.1" />} />,
                selected: selectedManufacturer === id,
            }))}
        />
    );
};

export { SearchBarManufcaturers };
