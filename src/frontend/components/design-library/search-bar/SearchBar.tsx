import React, { FC } from 'react';

import { Menu } from '@mantine/core';

import { useDisclosure } from '@mantine/hooks';
import { useHotkeys } from 'react-hotkeys-hook';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { SearchBox } from 'components/search-box';

import { SearchBarTags } from './SearchBar.Tags';
import { SearchBarSearch } from './SearchBar.Search';
import { SearchBarManufcaturers } from './SearchBar.Manufacturers';
import { SearchBarFilteredItems } from './SearchBar.FilteredItems';
import { SearchBarOtherFilters } from './SearchBar.OtherFilters';

const SearchBar: FC & {
    FilteredItems: typeof SearchBarFilteredItems;
    Search: typeof SearchBarSearch;
    Manufacturers: typeof SearchBarManufcaturers;
    Tags: typeof SearchBarTags;
    Other: typeof SearchBarOtherFilters;
} = () => {
    const [opened, handlers] = useDisclosure();
    const { searchBarQuery } = useDesignLibrarySearch();

    const submit = () => {
        DesignLibraryService.setFilter('search', searchBarQuery);
        DesignLibraryService.clearSearchBarQuery();
        handlers.close();
    };

    useHotkeys('enter', submit, {
        enableOnFormTags: ['input'],
    });

    return (
        <Menu opened={opened} position="bottom-start" offset={4} width="target" trapFocus={false} shadow="xl">
            <Menu.Target>
                <div>
                    <SearchBox
                        size="lg"
                        value={searchBarQuery ?? ''}
                        placeholder="Filter by tag, manufacturer, name etc."
                        autoComplete="off"
                        onFocus={handlers.open}
                        onBlur={handlers.close}
                        onChange={(event) => {
                            DesignLibraryService.setSearchBarQuery(event.target.value);
                        }}
                    />
                </div>
            </Menu.Target>

            <SearchBarDropdown />
        </Menu>
    );
};

const SearchBarDropdown = () => {
    return (
        <Menu.Dropdown>
            <SearchBar.Search />
            <SearchBar.Tags />
            <SearchBar.Other />
            <SearchBar.Manufacturers />
        </Menu.Dropdown>
    );
};

SearchBar.FilteredItems = SearchBarFilteredItems;
SearchBar.Tags = SearchBarTags;
SearchBar.Search = SearchBarSearch;
SearchBar.Manufacturers = SearchBarManufcaturers;
SearchBar.Other = SearchBarOtherFilters;

export { SearchBar };
