import React from 'react';

import { BsLightningCharge } from 'react-icons/bs';
import { IoBatteryHalfOutline, IoChatbubblesOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { SearchBar } from './SearchBar';
import { FormatHelpers } from 'helpers/formatters';
import { energyConverter, powerConverter } from 'models';

const SearchBarOtherFilters = () => {
    const {
        filters: { grid, generation, storage, load, inAppSupport },
    } = useDesignLibrarySearch();

    const formattedGrid = FormatHelpers.formatValue(grid, powerConverter);
    const formattedGeneration = FormatHelpers.formatValue(generation, powerConverter);
    const formattedLoad = FormatHelpers.formatValue(load, powerConverter);
    const formattedStorage = FormatHelpers.formatValue(storage, energyConverter);

    const items = [
        {
            leftSection: <IoChatbubblesOutline />,
            label: 'Provides in-app support',
            onClick: () => {
                DesignLibraryService.setFilter('inAppSupport', !inAppSupport);
                DesignLibraryService.clearSearchBarQuery();
            },
            selected: inAppSupport,
        },
        {
            leftSection: <IoBatteryHalfOutline />,
            label: formattedStorage ? `Storage: ${formattedStorage}` : 'Filter by storage',
            onClick: () => {
                DesignLibraryService.toggleInlineFilter('storage', true);
                DesignLibraryService.clearSearchBarQuery();
            },
            selected: !!storage,
        },
        {
            leftSection: <BsLightningCharge />,
            label: formattedGrid ? `Grid: ${formattedGrid}` : 'Filter by grid power',
            onClick: () => {
                DesignLibraryService.toggleInlineFilter('grid', true);
                DesignLibraryService.clearSearchBarQuery();
            },
            selected: !!grid,
        },
        {
            leftSection: <BsLightningCharge />,
            label: formattedGeneration ? `Generation: ${formattedGeneration}` : 'Filter by generated power',
            onClick: () => {
                DesignLibraryService.toggleInlineFilter('generation', true);
                DesignLibraryService.clearSearchBarQuery();
            },
            selected: !!generation,
        },
        {
            leftSection: <BsLightningCharge />,
            label: formattedLoad ? `Load: ${formattedLoad}` : 'Filter by load power',
            onClick: () => {
                DesignLibraryService.toggleInlineFilter('load', true);
                DesignLibraryService.clearSearchBarQuery();
            },
            selected: !!load,
        },
    ];

    return <SearchBar.FilteredItems items={items} />;
};

export { SearchBarOtherFilters };
