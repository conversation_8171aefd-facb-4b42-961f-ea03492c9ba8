import React from 'react';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibraryTags } from 'components/design-library/hooks/use-design-library-tags';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { SearchBar } from './SearchBar';

const SearchBarTags = () => {
    const {
        filters: { tags: selectedTags },
    } = useDesignLibrarySearch();

    const { tags, isLoading } = useDesignLibraryTags();

    if (!tags.length || isLoading) return null;

    return (
        <SearchBar.FilteredItems
            showAsPills
            label="Filter by tag"
            items={tags.map((tag) => ({
                label: tag,
                onClick: () => {
                    DesignLibraryService.setFilter(
                        'tags',
                        selectedTags?.includes(tag)
                            ? selectedTags.filter((_tag) => _tag !== tag)
                            : [...(selectedTags ?? []), tag],
                    );
                    DesignLibraryService.clearSearchBarQuery();
                },
                selected: selectedTags?.includes(tag),
            }))}
        />
    );
};

export { SearchBarTags };
