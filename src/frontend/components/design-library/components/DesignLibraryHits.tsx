import React from 'react';

import { Project } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { Loader } from 'components/search/components/Loader';
import { GridSection } from 'components/section/GridSection';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { DesignLibraryTeaser } from 'components/design-library-teaser/DesignLibraryTeaser';

const DesignLibraryHits = ({ designs = [], isLoading }: { designs: Project[]; isLoading?: boolean }) => {
    if (isLoading) {
        return <Loader />;
    }

    return designs.length === 0 ? (
        <EmptyMessage>No designs found matching your criteria</EmptyMessage>
    ) : (
        <GridSection nbCols={2}>
            {designs.map((project) => (
                <DesignLibraryTeaser
                    key={project.id}
                    project={project}
                    handleTagClick={(tag) => DesignLibraryService.setFilter('tags', [tag])}
                />
            ))}
        </GridSection>
    );
};

export { DesignLibraryHits };
