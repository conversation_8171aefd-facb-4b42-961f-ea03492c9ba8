import React, { useEffect } from 'react';

import { Stack, Title } from '@mantine/core';

import { Page } from 'components/page';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';
import { DesignLibraryService } from 'services/DesignLibraryService';
import { SearchBar } from 'components/design-library/search-bar/SearchBar';
import { InlineFilters } from 'components/design-library/inline-filters/InlineFilters';
import { DesignLibraryHits } from 'components/design-library/components/DesignLibraryHits';

const DesignLibrarySearch = () => {
    const { docs, isLoading } = useDesignLibrarySearch();

    // initial search
    useEffect(() => {
        DesignLibraryService.debouncedSearch();
    }, []);

    return <DesignLibraryHits designs={docs} isLoading={isLoading} />;
};

const WrappedDesignLibrarySearch = () => {
    return (
        <Page
            showBackground
            breadcrumbs={{
                type: 'floating.fullWidth',
            }}
            title="Reference designs"
        >
            <Page.Hero>
                <Title>Reference Designs</Title>

                <Stack mt="xl" maw={1000} mx="auto" gap={8}>
                    <SearchBar />
                    <InlineFilters size="lg" />
                </Stack>
            </Page.Hero>
            <Page.WideContent>
                <DesignLibrarySearch />
            </Page.WideContent>
        </Page>
    );
};

export { WrappedDesignLibrarySearch as DesignLibrarySearch };
