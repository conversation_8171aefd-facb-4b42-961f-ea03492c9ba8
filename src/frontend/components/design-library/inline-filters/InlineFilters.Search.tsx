import React from 'react';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { InlineFiltersSearch as Component } from 'components/inline-filters/InlineFilters.Search';

const InlineFiltersSearch = () => {
    const {
        filters: { search },
    } = useDesignLibrarySearch();

    const handleRemove = () => {
        DesignLibraryService.setFilter('search', undefined);
    };

    const handleChange = (value: string) => {
        DesignLibraryService.setFilter('search', value);
    };

    return <Component search={search} onRemove={handleRemove} onChange={handleChange} />;
};

export { InlineFiltersSearch };
