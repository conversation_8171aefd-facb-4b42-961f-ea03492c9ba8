import React from 'react';

import { IoChatbubblesOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { InlineFilters } from './InlineFilters';

const InlineFiltersInAppSupport = () => {
    const {
        filters: { inAppSupport },
    } = useDesignLibrarySearch();

    if (!inAppSupport) return null;

    return (
        <InlineFilters.SectionWithIcon
            icon={<IoChatbubblesOutline />}
            onRemove={() => DesignLibraryService.setFilter('inAppSupport', undefined)}
            body="Provides in-app support"
            active
        />
    );
};
export { InlineFiltersInAppSupport };
