import type { FC } from 'react';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { InlineFiltersMoreButton as Component } from 'components/inline-filters/InlineFilters.MoreButton';

const InlineFiltersMoreButton: FC = () => {
    const { filters } = useDesignLibrarySearch();

    const showClearButton = !DesignLibraryService.isInitialSearch(filters);

    if (!showClearButton) return null;

    return <Component showClearButton onClear={DesignLibraryService.resetFilters} />;
};

export { InlineFiltersMoreButton };
