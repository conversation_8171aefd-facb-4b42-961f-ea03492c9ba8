import React, { FC } from 'react';

import { Form } from 'components/forms/Form';

import { InlineFiltersSectionRemove } from 'components/inline-filters/InlineFilters.SectionRemove';
import { InlineFiltersWrapper, InlineFiltersWrapperProps } from 'components/inline-filters/InlineFilters.Wrapper';
import { InlineFiltersSection, InlineFiltersSectionWithPopover } from 'components/inline-filters/InlineFilters.Section';
import { InlineFiltersMeasurementWrapper } from 'components/inline-filters/InlineFilters.MeasurementWrapper';
import { InlineFiltersSectionWithIcon } from 'components/inline-filters/InlineFilters.SectionWithIcon';

import { InlineFiltersTags } from './InlineFilters.Tags';
import { InlineFiltersPower } from './InlineFilters.Power';
import { InlineFiltersSearch } from './InlineFilters.Search';
import { InlineFiltersMoreButton } from './InlineFilters.MoreButton';
import { InlineFiltersInAppSupport } from './InlineFilters.InAppSupport';
import { InlineFiltersManufacturer } from './InlineFilters.Manufacturer';
import { InlineFiltersEnergyCapacity } from './InlineFilters.EnergyCapacity';

const InlineFilters: FC<InlineFiltersWrapperProps> & {
    Wrapper: typeof InlineFiltersWrapper;
    MeasurementWrapper: typeof InlineFiltersMeasurementWrapper;
    Section: typeof InlineFiltersSection;
    SectionWithPopover: typeof InlineFiltersSectionWithPopover;
    SectionWithIcon: typeof InlineFiltersSectionWithIcon;
    RemoveButton: typeof InlineFiltersSectionRemove;
    Manufacturer: typeof InlineFiltersManufacturer;
    InAppSupport: typeof InlineFiltersInAppSupport;
    Search: typeof InlineFiltersSearch;
    Tags: typeof InlineFiltersTags;
    Power: typeof InlineFiltersPower;
    EnergyCapacity: typeof InlineFiltersEnergyCapacity;
    More: typeof InlineFiltersMoreButton;
} = (props) => {
    return (
        <Form onSubmit={() => {}}>
            <InlineFilters.Wrapper variant="dark" {...props}>
                <InlineFilters.Search />
                <InlineFilters.Tags />
                <InlineFilters.EnergyCapacity />
                <InlineFilters.Power name="grid" label="Grid power" shortLabel="Grid" />
                <InlineFilters.Power name="generation" label="Generated power" shortLabel="Generated" />
                <InlineFilters.Power name="load" label="Load power" shortLabel="Load" />
                <InlineFilters.Manufacturer />
                <InlineFilters.More />
            </InlineFilters.Wrapper>
        </Form>
    );
};

InlineFilters.Wrapper = InlineFiltersWrapper;
InlineFilters.MeasurementWrapper = InlineFiltersMeasurementWrapper;
InlineFilters.Section = InlineFiltersSection;
InlineFilters.SectionWithPopover = InlineFiltersSectionWithPopover;
InlineFilters.SectionWithIcon = InlineFiltersSectionWithIcon;
InlineFilters.RemoveButton = InlineFiltersSectionRemove;

InlineFilters.Manufacturer = InlineFiltersManufacturer;
InlineFilters.InAppSupport = InlineFiltersInAppSupport;
InlineFilters.Search = InlineFiltersSearch;
InlineFilters.Tags = InlineFiltersTags;
InlineFilters.Power = InlineFiltersPower;
InlineFilters.EnergyCapacity = InlineFiltersEnergyCapacity;
InlineFilters.More = InlineFiltersMoreButton;

export { InlineFilters };
