import React, { FC } from 'react';

import { Menu } from '@mantine/core';
import { IoCheckmarkOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibraryTags } from 'components/design-library/hooks/use-design-library-tags';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { InlineFilters } from './InlineFilters';

const InlineFiltersTags: FC = () => {
    const {
        filters: { tags },
    } = useDesignLibrarySearch();

    const { tags: allTags, isLoading } = useDesignLibraryTags();

    const onClick = (value: string) => {
        DesignLibraryService.setFilter(
            'tags',
            tags?.includes(value) ? tags.filter((_tag) => _tag !== value) : [...(tags ?? []), value],
        );
    };

    const formattedValue = tags?.join(', ');

    if (isLoading || !allTags?.length) {
        return null;
    }

    return (
        <InlineFilters.Section
            label={!tags?.length ? 'Tags' : ''}
            body={formattedValue}
            onRemove={tags?.length ? () => DesignLibraryService.setFilter('tags', undefined) : undefined}
            popoverContent={
                <>
                    <Menu.Label>Tags</Menu.Label>
                    {allTags.map((tag) => (
                        <Menu.Item
                            key={tag}
                            onClick={() => onClick(tag)}
                            leftSection={tags?.includes(tag) ? <IoCheckmarkOutline /> : null}
                        >
                            {tag}
                        </Menu.Item>
                    ))}
                </>
            }
            active={!!tags?.length}
        />
    );
};

export { InlineFiltersTags };
