import React, { useEffect } from 'react';
import { useWatch } from 'react-hook-form';

import { BsLightningCharge } from 'react-icons/bs';

import { FormatHelpers } from 'helpers/formatters';
import { powerConverter } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';
import { DesignLibrarySearch } from 'components/design-library/types';

import { PowerField } from 'components/component-fields/PowerField';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { InlineFilters } from './InlineFilters';

const InlineFiltersPower = ({
    name,
    label,
    shortLabel,
}: {
    name: keyof DesignLibrarySearch['filters'];
    label: string;
    shortLabel?: string;
}) => {
    const powerInput = useWatch({
        name,
    });

    const { filters } = useDesignLibrarySearch();

    const filterValue = filters[name] as number;

    useEffect(() => {
        if (powerInput?.value) {
            DesignLibraryService.setFilter(name, powerInput.value);
        }
    }, [powerInput]);

    const formattedPower = filterValue ? FormatHelpers.formatValue(filterValue, powerConverter) : '';

    return (
        <InlineFilters.SectionWithIcon
            icon={<BsLightningCharge />}
            label={filterValue ? shortLabel : ''}
            onRemove={
                filterValue
                    ? () => {
                          DesignLibraryService.setFilter(name, undefined);
                      }
                    : undefined
            }
            body={
                <InlineFilters.MeasurementWrapper placeholder={label} label={formattedPower}>
                    <PowerField name={name} size="xs" hideIcons fields={['value']} />
                </InlineFilters.MeasurementWrapper>
            }
            active={!!filters[name]}
        />
    );
};
export { InlineFiltersPower };
