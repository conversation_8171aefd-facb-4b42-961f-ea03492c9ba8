import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Manufacturer } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';

const useDesignLibraryManufacturers = (): SWRResponse & {
    manufacturers: Manufacturer[];
} => {
    const swr = useSWRImmutable<{
        manufacturers: Manufacturer[];
    }>(`/api/projects/marketplace-manufacturers`, () => {
        return DesignLibraryService.getManufacturers();
    });

    return {
        ...swr,
        manufacturers: swr?.data?.manufacturers || [],
    };
};

export { useDesignLibraryManufacturers };
