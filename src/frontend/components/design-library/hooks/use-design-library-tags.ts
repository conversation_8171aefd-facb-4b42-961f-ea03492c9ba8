import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { DesignLibraryService } from 'services/DesignLibraryService';

const useDesignLibraryTags = (): SWRResponse & {
    tags: string[];
} => {
    const swr = useSWRImmutable<{
        tags: string[];
    }>(`/api/projects/marketplace/tags`, () => {
        return DesignLibraryService.getTags();
    });

    return {
        ...swr,
        tags: swr?.data?.tags || [],
    };
};

export { useDesignLibraryTags };
