import { Project, ReferenceDesignQuery } from 'models';

export type DesignLibrarySearch = {
    docs: Project[];
    page: number;
    totalPages: number;
    totalDesigns: number;
    isLoading: boolean;
    error: boolean;
    searchBarQuery: string | undefined;
    filters: ReferenceDesignQuery;
    showFilters: {
        grid: boolean;
        generation: boolean;
        storage: boolean;
        load: boolean;
    };
};

export type SearchBarItem = {
    onClick: () => void;
    label: string;
    selected?: boolean;
    leftSection?: React.ReactNode;
};
