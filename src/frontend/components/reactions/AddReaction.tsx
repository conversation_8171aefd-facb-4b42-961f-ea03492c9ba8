import React, { FC } from 'react';

import { EmojiPicker } from 'components/emoji-picker/EmojiPicker';

import { ReactionService } from 'services/ReactionService';

const AddReaction: FC<{
    collection: string;
    id: string;
    onReact?: () => void;
    children?: React.ReactNode;
}> = ({ collection, id, onReact, children }) => {
    const react = async (emoji: string) => {
        await ReactionService.react(collection, id, emoji);
        onReact?.();
    };

    return <EmojiPicker onEmojiSelect={react}>{children}</EmojiPicker>;
};

export { AddReaction };
