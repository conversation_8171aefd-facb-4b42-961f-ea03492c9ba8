.reaction,
.add {
    display: inline-flex;
    align-items: center;

    height: 24px;
    padding: 4px 8px;

    font-size: 16px;
    font-weight: 600;
    line-height: 1;

    background-color: #ffffff;

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: 12px;

    small {
        margin-left: 6px;

        font-size: 12px;
    }

    &:hover {
        background-color: var(--mantine-color-gray-0);
    }
}

.add {
    padding: 4px;
}
