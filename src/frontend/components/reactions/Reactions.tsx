import React, { FC } from 'react';

import { Box, Group, GroupProps, Tooltip, UnstyledButton } from '@mantine/core';
import { TbMoodPlus } from 'react-icons/tb';

import { EmojiPicker } from 'components/emoji-picker/EmojiPicker';

import { useCurrentUser } from 'hooks/use-current-user';
import { ReactionService, Reaction as ReactionType } from 'services/ReactionService';

import cx from './Reactions.module.css';
import { useUsers } from 'hooks/use-users';

const Reactions: FC<
    GroupProps & {
        reactions: ReactionType[];
        collection: string;
        id: string;
        onReact?: () => void;
    }
> = ({ reactions, collection, id, onReact, ...rest }) => {
    const currentUser = useCurrentUser();

    const grouped: {
        [key: ReactionType['reaction']]: ReactionType[];
    } = {};

    reactions.forEach((reaction) => {
        grouped[reaction.reaction] = grouped[reaction.reaction] || [];
        grouped[reaction.reaction].push(reaction);
    });

    const react = async (emoji: string) => {
        await ReactionService.react(collection, id, emoji);
        onReact?.();
    };

    return reactions.length > 0 ? (
        <Group gap={4} {...rest}>
            {Object.keys(grouped).map((reaction) => (
                <Reaction reaction={reaction} reactions={grouped[reaction]} react={react} key={reaction} />
            ))}
            {currentUser && (
                <EmojiPicker onEmojiSelect={react}>
                    <UnstyledButton className={cx.add}>
                        <TbMoodPlus />
                    </UnstyledButton>
                </EmojiPicker>
            )}
        </Group>
    ) : null;
};

const Reaction: FC<{
    reaction: ReactionType['reaction'];
    reactions: ReactionType[];
    react: (emoji: string) => void;
}> = ({ reaction, reactions, react }) => {
    const userIds = reactions.map((reaction) => reaction.createdBy);
    const { users } = useUsers(userIds);
    const tooltip = users.map((user) => user.name).join(', ');

    return (
        <Tooltip label={tooltip} disabled={!tooltip}>
            <UnstyledButton
                className={cx.reaction}
                onClick={() => {
                    react(reaction);
                }}
            >
                {reaction}
                <small>{reactions.length}</small>
            </UnstyledButton>
        </Tooltip>
    );
};

export { Reactions };
