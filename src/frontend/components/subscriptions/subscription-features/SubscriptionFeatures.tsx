import React, { FC } from 'react';

import { Anchor, SegmentedControl, Space, Stack, Text, Tooltip } from '@mantine/core';

import { SUBSCRIPTION_FEATURES, DesignerSubscription, getDesignerSubscriptionData } from 'models';

import classes from './SubscriptionFeatures.module.scss';

import { SubscriptionButton } from '../components/SubscriptionButton';
import { SubscriptionHeader } from '../components/SubscriptionHeader';

import { SubscriptionBillingCycleControl } from '../components/SubscriptionBillingCycleControl';

import { useTeamFromURL } from 'hooks/use-team';
import { useCurrentTeam } from 'hooks/use-current-team';
import { SubscriptionUpdateWrapper } from '../SubscriptionUpdateWrapper';
import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { SubscriptionUpdateAction } from '../types';
import { ContactSales } from '../components/ContactSales';

const { wrapper, root, switcher, column, header, highlight2, featureTitle, section, feature, footer } = classes;

const SubscriptionFeatures: FC = () => {
    const { team: teamFromURL } = useTeamFromURL();
    const currentTeam = useCurrentTeam();
    const team = teamFromURL ?? currentTeam;

    if (!team) {
        return null;
    }

    return (
        <SubscriptionUpdateWrapper
            currentSubscription={
                getDesignerSubscriptionData(team.subscriptions)?.subscription ?? DesignerSubscription.FREE
            }
            team={team}
        >
            <Stack className={wrapper} align="center">
                <TierControl />
                <SubscriptionBillingCycleControl />
                <div className={root}>
                    <Header />

                    {SUBSCRIPTION_FEATURES.map(({ title, features }, index) => (
                        <Feature key={index} title={title} features={features} index={index} />
                    ))}

                    <Footer />
                </div>

                <ContactSales />
                <Space h="xl" />
            </Stack>
        </SubscriptionUpdateWrapper>
    );
};

const TierControl = () => {
    const dispatch = useSubscriptionUpdateDispatch();
    const { toSubscription } = useSubscriptionUpdateState();

    return (
        <SegmentedControl
            className={switcher}
            defaultValue={toSubscription}
            onChange={(value) =>
                dispatch({
                    type: SubscriptionUpdateAction.SET_SUBSCRIPTION,
                    payload: value as DesignerSubscription,
                })
            }
            data={[
                { label: 'Free', value: DesignerSubscription.FREE },
                { label: 'Plus', value: DesignerSubscription.PLUS },
                { label: 'Pro', value: DesignerSubscription.PRO },
            ]}
        />
    );
};

const Header = () => {
    const { toSubscription, currentBillingCycle, toBillingCycle } = useSubscriptionUpdateState();

    const billingCycleToShow = currentBillingCycle ?? toBillingCycle;

    return (
        <>
            <span className={column} />
            <span className={`${header} ${highlight2}`} data-active={toSubscription === DesignerSubscription.FREE}>
                <SubscriptionHeader subscription={DesignerSubscription.FREE} billingCycle={billingCycleToShow} />
                <SubscriptionButton subscriptionToSetOnClick={DesignerSubscription.FREE} />
            </span>
            <span className={`${header} ${highlight2}`} data-active={toSubscription === DesignerSubscription.PLUS}>
                <SubscriptionHeader subscription={DesignerSubscription.PLUS} billingCycle={billingCycleToShow} />
                <Stack gap="sm">
                    <SubscriptionButton subscriptionToSetOnClick={DesignerSubscription.PLUS} />
                    <Anchor variant="dimmed" component="a" href="mailto:<EMAIL>" ta="center">
                        Contact Sales
                    </Anchor>
                </Stack>
            </span>
            <span className={`${header} ${highlight2}`} data-active={toSubscription === DesignerSubscription.PRO}>
                <SubscriptionHeader subscription={DesignerSubscription.PRO} billingCycle={billingCycleToShow} />
                <Stack gap="sm">
                    <SubscriptionButton subscriptionToSetOnClick={DesignerSubscription.PRO} />
                    <Anchor variant="dimmed" component="a" href="mailto:<EMAIL>" ta="center">
                        Contact Sales
                    </Anchor>
                </Stack>
            </span>
        </>
    );
};

type FeatureProps = {
    title: (typeof SUBSCRIPTION_FEATURES)[number]['title'];
    features: (typeof SUBSCRIPTION_FEATURES)[number]['features'];
    index: number;
};
const Feature = ({ title, features, index }: FeatureProps) => {
    const { toSubscription } = useSubscriptionUpdateState();

    return (
        <>
            <span className={`${section} ${column}`}>{title}</span>
            <span className={highlight2} data-active={toSubscription === DesignerSubscription.FREE} />
            <span className={highlight2} data-active={toSubscription === DesignerSubscription.PLUS} />
            <span className={highlight2} data-active={toSubscription === DesignerSubscription.PRO} />
            {features.map(({ title, description, highlight, free, plus, pro }, j) => (
                <React.Fragment key={`section-${index}-feature-${j}`}>
                    <span className={`${column} ${featureTitle}`} data-description={!!description}>
                        <Tooltip label={description} position="right" hidden={!description}>
                            <Text
                                className={highlight ? 'gradient-cyan-green' : undefined}
                                fw={highlight ? 500 : undefined}
                            >
                                {title}
                            </Text>
                        </Tooltip>
                    </span>
                    <span
                        className={`${feature} ${highlight2}`}
                        data-checked={free.checkmark}
                        data-active={toSubscription === DesignerSubscription.FREE}
                    >
                        <div>{free.description}</div>
                    </span>
                    <span
                        className={`${feature} ${highlight2}`}
                        data-checked={plus.checkmark}
                        data-active={toSubscription === DesignerSubscription.PLUS}
                    >
                        <div>{plus.description}</div>
                    </span>
                    <span
                        className={`${feature} ${highlight2}`}
                        data-checked={pro.checkmark}
                        data-active={toSubscription === DesignerSubscription.PRO}
                    >
                        <div>{pro.description}</div>
                    </span>
                </React.Fragment>
            ))}
        </>
    );
};

const Footer = () => {
    const { toSubscription } = useSubscriptionUpdateState();

    return (
        <>
            <span className={column} />
            <span className={`${footer} ${highlight2}`} data-active={toSubscription === DesignerSubscription.FREE} />
            <span className={`${footer} ${highlight2}`} data-active={toSubscription === DesignerSubscription.PLUS} />
            <span className={`${footer} ${highlight2}`} data-active={toSubscription === DesignerSubscription.PRO} />
        </>
    );
};

export { SubscriptionFeatures };
