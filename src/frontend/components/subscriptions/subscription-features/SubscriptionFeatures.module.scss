.wrapper {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.root {
    display: grid;
    grid-template-columns: auto repeat(3, minmax(0px, 1fr));
    grid-auto-rows: min-content;
    column-gap: var(--mantine-spacing-xs);
    margin: var(--mantine-spacing-md);

    width: 100%;

    & > * {
        padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
    }

    @media (max-width: $mantine-breakpoint-sm) {
        font-size: var(--mantine-font-size-sm);

        grid-template-columns: minmax(0px, 40%) minmax(0px, 60%);
        column-gap: 1rem;

        & > [data-active='false'] {
            display: none;
        }
    }
}

.switcher {
    display: none;

    @media (max-width: $mantine-breakpoint-sm) {
        display: flex;
    }
}

.feature {
    padding-top: 0;
    padding-bottom: 0;

    &.highlight {
        color: var(--mantine-color-gray-6);
    }

    & > div {
        border-bottom: 1px solid var(--mantine-color-gray-2);
        height: 100%;

        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: var(--mantine-spacing-sm);

        padding-left: var(--mantine-spacing-xs);
    }

    &[data-checked='true'] > div {
        &::before {
            background-color: var(--mantine-color-primary-6);

            mask-position: center;
            mask-size: 1rem;
            mask-image: url('/icons/checkmark.svg');
            content: '';

            mask-repeat: no-repeat;

            height: 1rem;
            width: 1rem;
        }
    }

    &[data-checked='false'] > div {
        &::before {
            background-color: var(--mantine-color-gray-5);

            mask-position: center;
            mask-size: 1rem;
            mask-image: url('/icons/cross.svg');
            content: '';

            mask-repeat: no-repeat;

            height: 1rem;
            width: 1rem;
        }
    }
}

.featureTitle {
    & > div {
        width: 100%;
        text-wrap: wrap;
    }

    &[data-description='true'] {
        border-bottom: 1px dashed var(--mantine-color-gray-3);
        cursor: help;
    }
}

.section {
    color: var(--mantine-color-black);
    font-weight: 600;

    padding-top: 3rem;
}

.column {
    padding-left: var(--mantine-spacing-xs);
}

.header {
    position: relative;

    padding: var(--mantine-spacing-xl) var(--mantine-spacing-lg);

    display: flex;
    flex-direction: column;

    text-align: center;

    > * + * {
        margin-top: var(--mantine-spacing-lg);
    }

    &.highlight,
    &.highlight2 {
        border-top-left-radius: var(--mantine-radius-md);
        border-top-right-radius: var(--mantine-radius-md);
    }
}

.planBadge {
    position: absolute;
    top: 0;
    left: 50%;

    margin-top: 0;

    transform: translate(-50%, -50%);
}

.footer {
    display: grid;

    padding-top: 4rem;

    &.highlight,
    &.highlight2 {
        border-bottom-left-radius: var(--mantine-radius-md);
        border-bottom-right-radius: var(--mantine-radius-md);
    }
}

.highlight {
    background-color: var(--mantine-color-gray-1);

    @media (max-width: $mantine-breakpoint-sm) {
        background-color: var(--mantine-color-white);
    }
}

.highlight2 {
    background-color: var(--mantine-color-white);
}
