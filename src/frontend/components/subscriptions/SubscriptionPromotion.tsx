import { Text } from '@mantine/core';
import { CompanySubscription, Subscription } from 'models';

export const SubscriptionPromotion = ({ subscription }: { subscription: Subscription }) => {
    if (subscription === CompanySubscription.PREMIUM) {
        return (
            <Text c="dimmed" fz="sm">
                Sign up before Sept 30 and unlock the promotional rate of $299/month (until Dec 31). Standard rate of
                $499 per month applies from Jan 1.{' '}
                <Text span fw={600} inherit c="gray.7">
                    Cancel anytime
                </Text>
                .
            </Text>
        );
    }

    return null;
};
