import React, { FC } from 'react';
import { CompanyProfile, CompanySubscription, getCompanySubscriptionData, PermissionCompany } from 'models';

import { ButtonProps } from '@mantine/core';

import { useTeam } from 'hooks/use-team';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { SubscriptionUpdateWrapper } from './SubscriptionUpdateWrapper';
import { SubscriptionButton } from './components/SubscriptionButton';

const CompanyPremiumSubscriptionButton: FC<{
    company: CompanyProfile;
    buttonProps?: ButtonProps;
    buttonLabel?: string;
    redirectUrl?: string;
}> = ({ company, buttonProps, buttonLabel, redirectUrl }) => {
    const { team } = useTeam(company.team);
    const canEdit = useProfilePermission(PermissionCompany.EDIT, false, company);

    if (!team) {
        return null;
    }

    if (!canEdit) {
        return null;
    }

    const subscription = getCompanySubscriptionData(team.subscriptions)?.subscription ?? CompanySubscription.NONE;

    if (subscription === CompanySubscription.PREMIUM) {
        return null;
    }

    return (
        <SubscriptionUpdateWrapper
            toSubscription={CompanySubscription.PREMIUM}
            currentSubscription={subscription}
            team={team}
            redirectUrl={redirectUrl}
        >
            <SubscriptionButton buttonLabel={buttonLabel} buttonProps={buttonProps} />
        </SubscriptionUpdateWrapper>
    );
};

export { CompanyPremiumSubscriptionButton };
