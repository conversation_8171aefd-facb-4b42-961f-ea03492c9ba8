import React from 'react';

import Link from 'next/link';

import { ContextModalProps } from '@mantine/modals';
import { Button, Stack, Text } from '@mantine/core';

import { ModalTitle } from 'components/modals/ModalTitle';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';
import { DesignerSubscription } from 'models';

const SimpleSubscriptionModal = ({
    context,
    id,
    innerProps,
}: ContextModalProps<{
    message?: React.ReactNode;
    free?: React.ReactNode;
}>) => {
    const subscription = useCurrentTeamSubscription();

    return (
        <Stack pb="xs" pt="xs" py="xl" px="md" align="center">
            <ModalTitle title="Upgrade your subscription" />

            <Text maw={500} ta="center">
                {innerProps.message}
                {subscription === DesignerSubscription.FREE && innerProps.free}
            </Text>

            <Button
                component={Link}
                href={`/account?redirect=${window.location.pathname}#team`}
                fullWidth
                variant="gradient"
                onClick={() => context.closeModal(id)}
            >
                Upgrade your plan
            </Button>
        </Stack>
    );
};

export { SimpleSubscriptionModal };
