import React from 'react';

import { CompanySubscription } from 'models';

import {
    StaticSubscriptionCard,
    SubscriptionCard,
    SubscriptionCardGrid,
} from './components/subscription-card/SubscriptionCard';

const CompanySubscriptionOptions = () => {
    return (
        <SubscriptionCardGrid>
            <SubscriptionCard subscriptionToSetOnClick={CompanySubscription.PREMIUM} />
            <SubscriptionCard subscriptionToSetOnClick={CompanySubscription.FREE} />
        </SubscriptionCardGrid>
    );
};

export const StaticCompanySubscriptionOptions = () => {
    return (
        <SubscriptionCardGrid>
            <StaticSubscriptionCard subscriptionToSetOnClick={CompanySubscription.PREMIUM} />
            <StaticSubscriptionCard subscriptionToSetOnClick={CompanySubscription.FREE} />
        </SubscriptionCardGrid>
    );
};

export { CompanySubscriptionOptions };
