import React, { FC } from 'react';

import { DesignerSubscription, getDesignerSubscriptionData, Team } from 'models';

import { Flex, Stack, Title, Text } from '@mantine/core';

import { SubscriptionBillingCycleControl } from './components/SubscriptionBillingCycleControl';
import { SubscriptionCard, SubscriptionCardGrid } from './components/subscription-card/SubscriptionCard';
import { SubscriptionUpdateWrapper } from './SubscriptionUpdateWrapper';
import { SubscriptionUpdateNotice } from './components/SubscriptionUpdateNotice';
import { useShowDesignEditor } from 'hooks/use-show-design-editor';

const TeamSubscription: FC<{ team: Team }> = ({ team }) => {
    const showDesignEditor = useShowDesignEditor();

    if (!showDesignEditor) {
        return null;
    }

    const subscriptionData = getDesignerSubscriptionData(team.subscriptions);

    return (
        <SubscriptionUpdateWrapper
            currentSubscription={subscriptionData?.subscription ?? DesignerSubscription.FREE}
            team={team}
        >
            <Stack gap="sm">
                <Title order={2}>Team subscription</Title>

                {subscriptionData?.quantity && (
                    <Text>
                        <Text inherit span fw={500}>
                            Number of seats:
                        </Text>{' '}
                        {subscriptionData.quantity}
                    </Text>
                )}

                <Flex align="center" gap="sm">
                    <SubscriptionBillingCycleControl />
                </Flex>

                <SubscriptionUpdateNotice type="designer" />

                <SubscriptionCardGrid>
                    <SubscriptionCard subscriptionToSetOnClick={DesignerSubscription.FREE} href={'/upgrade'} />
                    <SubscriptionCard subscriptionToSetOnClick={DesignerSubscription.PLUS} href={'/upgrade'} />
                    <SubscriptionCard subscriptionToSetOnClick={DesignerSubscription.PRO} href={'/upgrade'} />
                </SubscriptionCardGrid>
            </Stack>
        </SubscriptionUpdateWrapper>
    );
};

export { TeamSubscription };
