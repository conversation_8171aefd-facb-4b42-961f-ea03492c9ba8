import { Anchor, Text } from '@mantine/core';
import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from './SubscriptionUpdateContext';
import { SubscriptionUpdateAction } from './types';

export const MaxUsersMessage = () => {
    const dispatch = useSubscriptionUpdateDispatch();
    const { isFree } = useSubscriptionUpdateState();

    const open = () => {
        dispatch({ type: SubscriptionUpdateAction.OPEN_MODAL });
    };

    if (isFree) {
        return (
            <Text span>
                <Anchor inline underline="always" onClick={open}>
                    Upgrade your subscription to add more team members
                </Anchor>
            </Text>
        );
    }

    return (
        <Text span>
            You have reached the limit of your subscription seats,{' '}
            <Anchor inline underline="always" onClick={open}>
                add extra seats to invite more team members.
            </Anchor>
        </Text>
    );
};
