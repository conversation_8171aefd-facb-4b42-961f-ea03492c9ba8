import { FC } from 'react';
import { useRouter } from 'next/router';

import { Button, ButtonProps } from '@mantine/core';
import { IoStarSharp } from 'react-icons/io5';

const SubscriptionOnboardingButton: FC<ButtonProps & { onClick?: () => void }> = ({
    variant = 'gradient',
    children = 'Upgrade',
    onClick,
    ...props
}) => {
    const router = useRouter();

    return (
        <Button
            variant={variant}
            color="primary.3"
            onClick={() => {
                onClick?.();
                router.push(`/upgrade?redirect=${router.asPath}`).then();
            }}
            leftSection={<IoStarSharp size={16} />}
            {...props}
        >
            {children}
        </Button>
    );
};

export { SubscriptionOnboardingButton };
