import { useEffect } from 'react';

import { Button } from '@mantine/core';

import { Subscription, SubscriptionBillingCycle, Team } from 'models';

import { useCurrentUser } from 'hooks/use-current-user';

import { ModalService } from 'services/ModalService';
import { SubscriptionModal } from './components/SubscriptionModal';
import {
    getCalculatedDefaultValues,
    SubscriptionUpdateProvider,
    useSubscriptionUpdateDispatch,
} from './SubscriptionUpdateContext';
import { LegalCheck, SubscriptionUpdateAction } from './types';

type SubscriptionProps<T extends Subscription> = {
    currentSubscription: T;
    toSubscription?: T;
    team: Team;
};

type SubscriptionUpdateWrapperProps<T extends Subscription> = SubscriptionProps<T> & {
    redirectUrl?: string;
    postSubmitAction?: () => void;
    children: React.ReactNode;
};

export const SubscriptionUpdateWrapper = <T extends Subscription>({
    currentSubscription,
    toSubscription = currentSubscription,
    team,
    children,
    redirectUrl,
    postSubmitAction,
}: SubscriptionUpdateWrapperProps<T>) => {
    const user = useCurrentUser();

    if (!user) {
        return (
            <Button fullWidth size="md" variant="outline" onClick={() => ModalService.openLoginModal()}>
                Log in
            </Button>
        );
    }

    if (!team) {
        return null;
    }

    const { currentBillingCycle, numberOfSeats } = getCalculatedDefaultValues(currentSubscription, team);

    return (
        <SubscriptionUpdateProvider
            currentSubscription={currentSubscription}
            currentBillingCycle={currentBillingCycle}
            toSubscription={toSubscription}
            toBillingCycle={currentBillingCycle ?? SubscriptionBillingCycle.MONTHLY}
            numberOfSeats={numberOfSeats}
            additionalSubscriptions={[]}
            interestedIn={[]}
            modalOpen={false}
            loading={false}
            legalCheck={LegalCheck.FAIL}
            team={team}
            user={user}
            postSubmitAction={postSubmitAction}
        >
            <SubscriptionModal />
            <SetRedirect redirectUrl={redirectUrl} />
            {children}
        </SubscriptionUpdateProvider>
    );
};

const SetRedirect = ({ redirectUrl }: { redirectUrl?: string }) => {
    const dispatch = useSubscriptionUpdateDispatch();

    useEffect(() => {
        if (redirectUrl) {
            dispatch({ type: SubscriptionUpdateAction.SET_REDIRECT, payload: redirectUrl });
        }
    }, [redirectUrl]);

    return null;
};
