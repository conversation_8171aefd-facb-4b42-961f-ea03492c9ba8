import { Flex, Stack } from '@mantine/core';
import { isDesignerSubscription, Subscription, SubscriptionBillingCycle } from 'models';
import { usePrices } from 'hooks/use-prices';

const PriceTag = ({
    price: _price,
    priceWithDiscount: _priceWithDiscount,
    lines = [],
}: {
    price: number;
    priceWithDiscount?: number;
    lines?: string[];
}) => {
    const price = _price > 0 ? `$${_price}` : '';
    const priceWithDiscount = _priceWithDiscount ? `$${_priceWithDiscount}` : '';

    if (!price) {
        return null;
    }

    const showLines = lines.length > 0;

    const PriceWithDiscount = priceWithDiscount ? <span style={{ lineHeight: 1 }}>{priceWithDiscount}</span> : null;

    const Price = priceWithDiscount ? (
        <span style={{ lineHeight: 1, textDecoration: 'line-through', color: 'var(--mantine-color-dimmed)' }}>
            {price}
        </span>
    ) : (
        <span style={{ lineHeight: 1 }}>{price}</span>
    );

    return (
        <Flex gap={2} align="center" style={{ flexShrink: 0, display: 'inline-flex' }}>
            {Price}
            {PriceWithDiscount}
            {showLines && (
                <Stack gap={0} fz={10} c="dimmed" align="flex-start">
                    {lines.map((line) => (
                        <span key={line} style={{ lineHeight: 0.9 }}>
                            {line}
                        </span>
                    ))}
                </Stack>
            )}
        </Flex>
    );
};

const WrappedPriceTag = ({
    subscription,
    billingCycle,
    priceWithDiscount,
}: {
    subscription: Subscription;
    billingCycle: SubscriptionBillingCycle;
    priceWithDiscount?: number;
}) => {
    const response = usePrices();

    if (!response.data) {
        return null;
    }

    const originalPricePerMonth = response.data[subscription][SubscriptionBillingCycle.MONTHLY];
    const pricePerMonth = response.data[subscription][billingCycle];

    const timeUnit = getBillingCycleTimeUnit(billingCycle);

    const lines = isDesignerSubscription(subscription) ? ['per seat', timeUnit] : [timeUnit];

    const currentPriceIsDiscount = originalPricePerMonth > pricePerMonth;

    if (currentPriceIsDiscount && !priceWithDiscount) {
        return <PriceTag priceWithDiscount={pricePerMonth} price={originalPricePerMonth} lines={lines} />;
    }

    return <PriceTag priceWithDiscount={priceWithDiscount} price={pricePerMonth} lines={lines} />;
};

const getBillingCycleTimeUnit = (billingCycle: SubscriptionBillingCycle) => {
    switch (billingCycle) {
        case SubscriptionBillingCycle.MONTHLY:
            return 'per month';
        case SubscriptionBillingCycle.YEARLY:
            return 'per month'; // Dusan requested to show prices per month
    }
};

export { WrappedPriceTag as PriceTag };
