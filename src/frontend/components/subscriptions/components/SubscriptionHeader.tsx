import { Stack, Flex, Text } from '@mantine/core';

import {
    DesignerSubscriptionConfig,
    Subscription,
    isDesignerSubscription,
    CompanySubscriptionConfig,
    SubscriptionBillingCycle,
} from 'models';

import { PriceTag } from './PriceTag';

export const SubscriptionHeader = ({
    subscription,
    billingCycle,
}: {
    subscription: Subscription;
    billingCycle?: SubscriptionBillingCycle;
}) => {
    const isDesignerSubscription_ = isDesignerSubscription(subscription);

    const { title, subtitle } = isDesignerSubscription_
        ? DesignerSubscriptionConfig[subscription]
        : CompanySubscriptionConfig[subscription];

    return (
        <Stack gap={0} align="flex-start">
            <Flex justify="space-between" align="center" w="100%">
                <Text fz={'xl'} fw={600}>
                    {title}
                </Text>
                {billingCycle && <PriceTag subscription={subscription} billingCycle={billingCycle} />}
            </Flex>

            <Text fz="sm" c="gray.6" mt={0}>
                {subtitle}
            </Text>
        </Stack>
    );
};
