import { Button, ButtonProps } from '@mantine/core';

import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { SubscriptionUpdateAction } from '../types';
import {
    CompanySubscription,
    DesignerSubscription,
    isCompanySubscription,
    isDesignerSubscription,
    isDowngrade,
    Subscription,
} from 'models';

export const SubscriptionButton = ({
    subscriptionToSetOnClick: subscriptionToSetOnClickProp,
    buttonProps = {},
    buttonLabel: buttonLabelOverride,
}: {
    subscriptionToSetOnClick?: Subscription;
    buttonProps?: ButtonProps;
    buttonLabel?: string;
}) => {
    const dispatch = useSubscriptionUpdateDispatch();
    const { toSubscription, toBillingCycle, currentSubscription, currentBillingCycle, loading } =
        useSubscriptionUpdateState();

    const subscriptionToSetOnClick = subscriptionToSetOnClickProp ?? toSubscription;

    const isFree =
        subscriptionToSetOnClick === DesignerSubscription.FREE || subscriptionToSetOnClick === CompanySubscription.FREE;

    const isDowngrade_ = isDowngrade(currentSubscription, subscriptionToSetOnClick);

    const isCurrentSubscription = subscriptionToSetOnClick === currentSubscription;
    const isCurrentBillingCycle = toBillingCycle === currentBillingCycle;

    if (isFree && isCurrentSubscription) {
        return (
            <Button fullWidth variant="filled" disabled>
                Current Subscription
            </Button>
        );
    }

    let buttonLabel = '';
    let disabled = false;

    if (isDesignerSubscription(subscriptionToSetOnClick)) {
        if (isCurrentSubscription) {
            buttonLabel = isCurrentBillingCycle ? 'Add or remove seats' : 'Change plan';
        } else {
            buttonLabel = isDowngrade_ ? 'Change plan' : 'Upgrade';
        }
    } else if (isCompanySubscription(subscriptionToSetOnClick)) {
        if (isCurrentSubscription) {
            buttonLabel = isCurrentBillingCycle ? 'Current plan' : 'Upgrade';
            disabled = isCurrentBillingCycle ? true : false;
        } else {
            buttonLabel = isDowngrade_ ? 'Change plan' : 'Select';
        }
    }

    return (
        <Button
            onClick={() => {
                if (subscriptionToSetOnClick) {
                    dispatch({ type: SubscriptionUpdateAction.SET_SUBSCRIPTION, payload: subscriptionToSetOnClick });
                }

                dispatch({ type: SubscriptionUpdateAction.OPEN_MODAL });
            }}
            fullWidth
            variant={isDowngrade_ || isFree ? 'light' : 'filled'}
            loading={loading}
            disabled={disabled}
            {...buttonProps}
        >
            {buttonLabelOverride || buttonLabel}
        </Button>
    );
};
