import {
    DesignerSubscriptionConfig,
    isCompanySubscription,
    CompanySubscriptionConfig,
    isDesignerSubscription,
} from 'models';

import { DateService } from 'services/DateService';

import { useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { SubscriptionOnboardingMessage } from '../SubscriptionOnboardingMessage';

const SubscriptionUpdateNotice = ({ type }: { type: 'company' | 'designer' }) => {
    const { team } = useSubscriptionUpdateState();

    if (!team.subscriptionUpdateNotice) {
        return null;
    }

    const { date, changes: all } = team.subscriptionUpdateNotice;

    const filter = type === 'company' ? isCompanySubscription : isDesignerSubscription;

    for (const change of all) {
        const { subscription, isNew, isCanceled, quantityChanged, fromQuantity, toQuantity } = change;

        if (!filter(subscription)) {
            continue;
        }

        const { title } = isDesignerSubscription(subscription)
            ? DesignerSubscriptionConfig[subscription]
            : CompanySubscriptionConfig[subscription];

        const formattedDate = DateService.formatToDate(new Date(date * 1000));

        return (
            <SubscriptionOnboardingMessage hideButton>
                {isNew && `On ${formattedDate}, your subscription will change to ${title}.`}
                {isCanceled && `On ${formattedDate}, your ${title} subscription will be canceled.`}
                {quantityChanged &&
                    `On ${formattedDate}, the number of seats on your ${title} plan will change from ${fromQuantity} to ${toQuantity}.`}
            </SubscriptionOnboardingMessage>
        );
    }

    return null;
};

export { SubscriptionUpdateNotice };
