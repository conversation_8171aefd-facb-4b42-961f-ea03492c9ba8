import { FC } from 'react';

import { User } from 'models';

import { useUser } from 'hooks/use-user';
import { Anchor } from '@mantine/core';

export const UserListItem: FC<{ id: User['id']; mailto?: { subject: string; message: string } }> = ({ id, mailto }) => {
    const { user } = useUser(id);

    if (!user) {
        return null;
    }

    const { name, email } = user;

    // Build the mailto link with optional subject and message
    let href = `mailto:${email}`;
    if (mailto) {
        const params = [];
        if (mailto.subject) {
            params.push(`subject=${encodeURIComponent(mailto.subject)}`);
        }
        if (mailto.message) {
            params.push(`body=${encodeURIComponent(mailto.message)}`);
        }
        const queryString = params.join('&');
        if (queryString) {
            href += `?${queryString}`;
        }
    }

    return (
        <span>
            {name} (<Anchor href={href}>{email}</Anchor>)
        </span>
    );
};
