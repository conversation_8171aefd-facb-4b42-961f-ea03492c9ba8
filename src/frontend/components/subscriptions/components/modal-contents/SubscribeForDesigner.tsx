import { Anchor, But<PERSON>, Checkbox, Divider, Flex, NumberInput, Stack, Text, Tooltip } from '@mantine/core';

import { CompanySubscription, DesignerSubscription, DesignerSubscriptionConfig, ManufacturerProfile } from 'models';

import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../../SubscriptionUpdateContext';
import { useSubmitSubscriptionUpdate } from '../useSubmitSubscriptionUpdate';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { PriceTag } from '../PriceTag';
import { SubscribeModalContents } from '../SubscribeModalContents';

import { SubscriptionUpdateAction } from '../../types';
import { RouterService } from 'services/RouterService';

import { useGetSeatDetails } from '../useGetSeatDetails';

export const SubscribeForDesigner = ({ toSubscription }: { toSubscription: DesignerSubscription }) => {
    const { companies } = useCurrentTeamCompanies();
    const company: ManufacturerProfile | undefined = companies[0];

    const dispatch = useSubscriptionUpdateDispatch();
    const submitSubscriptionUpdate = useSubmitSubscriptionUpdate();
    const { numberOfSeats, isDowngrade, loading, team, toBillingCycle } = useSubscriptionUpdateState();

    const { notEnoughSeats, sameNumberOfSeatsOnSameSubscription, numberOfSeatsToAdd, numberOfSeatsToRemove } =
        useGetSeatDetails();

    const buttonLabel = notEnoughSeats
        ? `Purchase at least ${team.users.length} seat${team.users.length > 1 ? 's' : ''}`
        : numberOfSeatsToAdd > 0
          ? `Add ${numberOfSeatsToAdd} seat${numberOfSeatsToAdd > 1 ? 's' : ''}`
          : numberOfSeatsToRemove > 0
            ? `Remove ${numberOfSeatsToRemove} seat${numberOfSeatsToRemove > 1 ? 's' : ''}`
            : isDowngrade
              ? 'Change plan'
              : 'Continue';

    return (
        <SubscribeModalContents
            title={
                isDowngrade
                    ? `Downgrade to ${DesignerSubscriptionConfig[toSubscription].subtitle}`
                    : `Upgrade to ${DesignerSubscriptionConfig[toSubscription].title}`
            }
            subscriptionLabel={DesignerSubscriptionConfig[toSubscription].title}
            SubmitButton={
                <Tooltip
                    disabled={!(notEnoughSeats || sameNumberOfSeatsOnSameSubscription)}
                    label={
                        notEnoughSeats
                            ? 'You must purchase at least as many seats as there are team members.'
                            : sameNumberOfSeatsOnSameSubscription
                              ? 'You already have this many seats.'
                              : ''
                    }
                >
                    <Button
                        variant="gradient"
                        size="md"
                        onClick={submitSubscriptionUpdate}
                        loading={loading}
                        disabled={notEnoughSeats || sameNumberOfSeatsOnSameSubscription}
                    >
                        {buttonLabel}
                    </Button>
                </Tooltip>
            }
        >
            <NumberInput
                label="Add or remove seats on your plan"
                value={numberOfSeats}
                onChange={(value) => {
                    dispatch({
                        type: SubscriptionUpdateAction.UPDATE_NUMBER_OF_SEATS,
                        payload: typeof value === 'number' ? Math.round(value) : 0,
                    });
                }}
                min={team.users.length}
                step={1}
                description={`Purchase a seat for every team member on your team.`}
            />
            <Anchor
                size="sm"
                c="dimmed"
                onClick={() => {
                    dispatch({ type: SubscriptionUpdateAction.CLOSE_MODAL });
                    return RouterService.push(`/account#team-${team.id}`);
                }}
                ta="right"
            >
                Deactivate users to free up seats
            </Anchor>

            {company && company.subscription !== CompanySubscription.PREMIUM && (
                <>
                    <Divider label="Recommended Upgrades" labelPosition="left" />
                    <Stack>
                        <Flex align="flex-start">
                            <Checkbox
                                label={
                                    <Text span inherit>
                                        Upgrade your {company.name} profile
                                    </Text>
                                }
                                description={
                                    <Text span inherit>
                                        Upgrade to boost visibility of your profile in search results and obtain more
                                        leads.
                                    </Text>
                                }
                            />
                            <PriceTag subscription={CompanySubscription.PREMIUM} billingCycle={toBillingCycle} />
                        </Flex>
                    </Stack>
                </>
            )}
        </SubscribeModalContents>
    );
};
