import { But<PERSON>, Divider, <PERSON>, Modal, Stack, Text } from '@mantine/core';

import { ModalTitle } from 'components/modals/ModalTitle';

import { TeamService } from 'services/TeamService';

import { useTeams } from 'hooks/use-teams';
import { UserListItem } from './UserListItem';
import { useSubscriptionUpdateState } from '../../SubscriptionUpdateContext';
import { getTeamOwners } from 'hooks/use-current-team-owners';

export const SubscribeNotAllowed = () => {
    const { team, user } = useSubscriptionUpdateState();
    const teamOwnerIds = getTeamOwners(team);

    const { teams } = useTeams();

    const openSwitchTeamModal = () => {
        TeamService.openSwitchTeamModal({
            create: {
                title: 'Create Team',
                description: <>Create a new team.</>,
            },
            switchy: {
                title: 'Switch Team',
                description: <>Please select a different team to continue.</>,
            },
        });
    };

    const createNewTeam = () => {
        TeamService.openCreateTeam();
    };

    const hasOtherTeams = teams.filter(({ id }) => id !== team?.id).length > 0;

    const mailto = {
        subject: `DCIDE - Upgrade team ${team?.name} subscription`,
        // Formal message
        message:
            `Hello,` +
            `\n\nI would like to upgrade the subscription of ${team?.name} on DCIDE.` +
            `\nCould you please assist me with this?` +
            `\n\nNavigate to the team settings and upgrade the subscription to the desired plan:` +
            `\nhttps://www.dcide.app/account#team` +
            `\n\nBest regards,\n${user?.name}`,
    };

    return (
        <>
            <Modal.Header>
                <Modal.Title>
                    <ModalTitle title="Upgrade Subscription" />
                </Modal.Title>
                <Modal.CloseButton />
            </Modal.Header>
            <Modal.Body>
                <Text>
                    Looks like you don&apos;t have permission to make changes to <strong>{team?.name}&apos;s</strong>{' '}
                    subscription.
                </Text>
                <Stack>
                    <Text>Please reach out to a team owner for help.</Text>
                    <List>
                        {teamOwnerIds.map((id) => (
                            <List.Item key={`owner-${id}`}>
                                <UserListItem id={id} mailto={mailto} />
                            </List.Item>
                        ))}
                    </List>
                </Stack>
                <Divider label="or" my="md" />
                <Stack gap={8}>
                    {hasOtherTeams && (
                        <Button onClick={openSwitchTeamModal}>Switch to a different team to upgrade</Button>
                    )}
                    <Button onClick={createNewTeam} variant={hasOtherTeams ? 'outline' : ''}>
                        Create your own team and upgrade
                    </Button>
                </Stack>
            </Modal.Body>
        </>
    );
};
