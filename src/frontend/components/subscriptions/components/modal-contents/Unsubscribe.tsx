import { Button, Modal, Space, Stack, Text } from '@mantine/core';

import { ModalTitle } from 'components/modals/ModalTitle';
import { Form } from 'components/forms/Form';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

import { TeamService } from 'services/TeamService';

import { SubscriptionFeatureList } from '../../SubscriptionFeatureList';
import { useSubmitSubscriptionUpdate } from '../useSubmitSubscriptionUpdate';
import { useSubscriptionUpdateState } from '../../SubscriptionUpdateContext';
import { ContactSalesButton } from '../ContactSales';

import {
    CompanySubscriptionConfig,
    DesignerSubscriptionConfig,
    isCompanySubscription,
    isDesignerSubscription,
} from 'models';

export const Unsubscribe = () => {
    const submitSubscriptionUpdate = useSubmitSubscriptionUpdate();
    const { loading, currentSubscription } = useSubscriptionUpdateState();

    const { title } = isDesignerSubscription(currentSubscription)
        ? DesignerSubscriptionConfig[currentSubscription]
        : isCompanySubscription(currentSubscription)
          ? CompanySubscriptionConfig[currentSubscription]
          : { title: '' };

    return (
        <>
            <Modal.Header>
                <Modal.Title>
                    <ModalTitle title="Unsubscribe" />
                </Modal.Title>
                <Modal.CloseButton />
            </Modal.Header>
            <Modal.Body>
                <Stack gap="lg">
                    <Text>
                        Please confirm that you want to unsubscribe from the <strong>{title}</strong> plan. Your
                        subscription will be canceled immediately and you will lose access to all premium features:
                    </Text>
                    <SubscriptionFeatureList subscription={currentSubscription} checkmark={false} />
                    <Form<{ feedback: string }>
                        defaultValues={{ feedback: '' }}
                        onSubmit={(values) => {
                            TeamService.sendSubscriptionFeedback(values.feedback).then();

                            submitSubscriptionUpdate();
                        }}
                    >
                        <Stack gap="xs">
                            <MultilineTextField
                                label="Feedback"
                                name="feedback"
                                placeholder="Please share why you decided to unsubscribe"
                            />
                            <Space />
                            <Button variant="filled" color="red" size="md" loading={loading} type="submit">
                                Unsubscribe
                            </Button>
                            <ContactSalesButton />
                        </Stack>
                    </Form>
                </Stack>
            </Modal.Body>
        </>
    );
};
