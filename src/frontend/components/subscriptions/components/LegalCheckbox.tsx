import { Anchor, Checkbox, Text } from '@mantine/core';

import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { useGetSeatDetails } from './useGetSeatDetails';

import { LegalCheck, SubscriptionUpdateAction } from '../types';

export const LegalCheckbox = () => {
    const dispatch = useSubscriptionUpdateDispatch();
    const { legalCheck, isDowngrade } = useSubscriptionUpdateState();
    const { sameNumberOfSeatsOnSameSubscription } = useGetSeatDetails();

    if (isDowngrade || sameNumberOfSeatsOnSameSubscription) {
        return null;
    }

    return (
        <Checkbox
            checked={legalCheck === LegalCheck.PASS}
            onChange={() => {
                dispatch({
                    type: SubscriptionUpdateAction.SET_LEGAL_CHECK,
                    payload: legalCheck === LegalCheck.PASS ? LegalCheck.FAIL : LegalCheck.PASS,
                });
            }}
            label={
                <Text c={legalCheck === LegalCheck.ERROR ? 'red' : ''}>
                    By signing up, you agree to the listed subscription fees and accept the{' '}
                    <Anchor href="/subscription-agreement-designer-ai.pdf" target="_blank">
                        Subscription Agreement
                    </Anchor>
                    .
                </Text>
            }
        />
    );
};
