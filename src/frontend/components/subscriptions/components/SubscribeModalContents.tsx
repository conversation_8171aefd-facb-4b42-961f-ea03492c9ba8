import { Stack, Divider, Modal, Text, Alert } from '@mantine/core';

import { SubscriptionBillingCycleControl } from './SubscriptionBillingCycleControl';
import { ContactSales } from './ContactSales';
import { LegalCheckbox } from './LegalCheckbox';
import { PriceCalculation } from './PriceCalculation';

type SubscribeModalContentsProps = {
    title: string;
    subscriptionLabel: string;
    children: React.ReactNode;
    SubmitButton: React.ReactNode;
    description?: React.ReactNode;
};

export const SubscribeModalContents = ({
    title,
    SubmitButton,
    children,
    subscriptionLabel,
    description,
}: SubscribeModalContentsProps) => (
    <>
        <Modal.Header>
            <Modal.Title>{title}</Modal.Title>
            <Modal.CloseButton />
        </Modal.Header>
        <Modal.Body>
            <Stack gap="lg">
                <Text inherit>Confirm your subscription to unlock all {subscriptionLabel} features.</Text>
                {description && <Alert color="primary">{description}</Alert>}
                <SubscriptionBillingCycleControl />
                <PriceCalculation />
                {children}
                <Divider mt="sm" />
                <LegalCheckbox />

                <Stack gap="xs">
                    {SubmitButton}
                    <ContactSales />
                </Stack>
            </Stack>
        </Modal.Body>
    </>
);
