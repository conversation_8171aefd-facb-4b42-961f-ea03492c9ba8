import { getDesignerSubscriptionData } from 'models';

import { useSubscriptionUpdateState } from '../SubscriptionUpdateContext';

export const useGetSeatDetails = () => {
    const { team, numberOfSeats, toSubscription, currentSubscription } = useSubscriptionUpdateState();
    const subscriptionSeats = getDesignerSubscriptionData(team.subscriptions)?.quantity;

    const notEnoughSeats = numberOfSeats < team.users.length;
    const sameNumberOfSeatsOnSameSubscription =
        numberOfSeats === subscriptionSeats && toSubscription === currentSubscription;

    const currentNumberOfSeats = subscriptionSeats ?? 0;
    const delta = numberOfSeats - currentNumberOfSeats;
    const numberOfSeatsToAdd = delta > 0 ? delta : 0;
    const numberOfSeatsToRemove = delta < 0 ? -delta : 0;

    return {
        notEnoughSeats,
        sameNumberOfSeatsOnSameSubscription,
        numberOfSeatsToAdd,
        numberOfSeatsToRemove,
    };
};
