import { Modal } from '@mantine/core';

import { CompanySubscription, DesignerSubscription, isCompanySubscription, isDesignerSubscription } from 'models';

import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';

import { PermissionServiceSubscription } from 'services/PermissionServiceSubscription';

import { Unsubscribe } from './modal-contents/Unsubscribe';
import { SubscribeForDesigner } from './modal-contents/SubscribeForDesigner';
import { SubscribeNotAllowed } from './modal-contents/SubscribeNotAllowed';
import { SubscribeForCompany } from './modal-contents/SubscribeForCompany';

import { SubscriptionUpdateAction } from '../types';

export const SubscriptionModal = () => {
    const dispatch = useSubscriptionUpdateDispatch();
    const { modalOpen } = useSubscriptionUpdateState();

    return (
        <Modal.Root
            opened={modalOpen}
            onClose={() => dispatch({ type: SubscriptionUpdateAction.CLOSE_MODAL })}
            size="lg"
        >
            <Modal.Overlay />
            <Modal.Content>
                <ModalContents />
            </Modal.Content>
        </Modal.Root>
    );
};

const ModalContents = () => {
    const { isFree, user, team, toSubscription, currentSubscription } = useSubscriptionUpdateState();

    const canPurchase = PermissionServiceSubscription.canPurchaseSubscription(user, team);

    if (canPurchase) {
        if (isDesignerSubscription(toSubscription)) {
            if (isFree) {
                return <Unsubscribe />;
            } else if (toSubscription === DesignerSubscription.PLUS || toSubscription === DesignerSubscription.PRO) {
                return <SubscribeForDesigner toSubscription={toSubscription} />;
            }
        } else if (isCompanySubscription(toSubscription)) {
            if (toSubscription === CompanySubscription.FREE && currentSubscription === CompanySubscription.PREMIUM) {
                return <Unsubscribe />;
            } else {
                return <SubscribeForCompany toSubscription={toSubscription} />;
            }
        }
    }

    return <SubscribeNotAllowed />;
};
