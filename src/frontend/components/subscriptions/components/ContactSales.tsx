import { useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, Stack, Text, Textarea } from '@mantine/core';
import { TicketService } from 'services/TicketService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { useCurrentUser } from 'hooks/use-current-user';
import { SlackNotificationType, User } from 'models';
import { SlackNotificationService } from 'services/SlackNotificationService';

export const ContactSales = () => {
    const [showConfirmRequest, setShowConfirmRequest] = useState(false);

    if (showConfirmRequest) {
        return <ShowConfirmRequest />;
    }

    return (
        <Stack gap="xs" mt="md">
            <Text c="dimmed" ta="center" fz="sm">
                <Anchor variant="dimmed" component="a" onClick={() => setShowConfirmRequest(true)} inherit>
                    Contact Sales
                </Anchor>
                <br />
                for payments other than credit cards or bank transfers
            </Text>
        </Stack>
    );
};

export const ContactSalesButton = () => {
    const [showConfirmRequest, setShowConfirmRequest] = useState(false);

    if (showConfirmRequest) {
        return <ShowConfirmRequest />;
    }

    return (
        <Button variant="default" size="md" component="a" onClick={() => setShowConfirmRequest(true)}>
            Contact Sales
        </Button>
    );
};

const ShowConfirmRequest = () => {
    return (
        <Stack gap="xs" mt="md" pl="lg" pr="lg">
            <RequestTicket />
            <Divider label="or" />
            <Text variant="dimmed" ta="center">
                Email our sales team directly at{' '}
                <Anchor href="mailto:<EMAIL>"><EMAIL></Anchor>
            </Text>
        </Stack>
    );
};

const RequestTicket = () => {
    const [showUserInput, setShowUserInput] = useState(false);

    if (showUserInput) {
        return <TicketForm />;
    }

    return (
        <Anchor variant="dimmed" ta="center" onClick={() => setShowUserInput(true)}>
            Request help from our sales team
        </Anchor>
    );
};

const TicketForm = () => {
    const user = useCurrentUser();
    const [userMessage, setUserMessage] = useState('');
    const [submitted, setShowSubmitted] = useState(false);
    const [error, setError] = useState<string | null>(null);

    if (!user) {
        return null;
    }

    const submitTicket = async () => {
        if (!userMessage) {
            setError('Please provide a message');
            return;
        }

        const [ticketResult, slackNotificationResult] = await Promise.allSettled([
            TicketService.createTicket({
                subject: 'Checkout Sales Request',
                userMessage: appendUserInfo(userMessage, user),
                throwError: true,
            }),
            SlackNotificationService.send({
                type: SlackNotificationType.GENERAL,
                message: appendUserInfo(userMessage, user, true),
            }),
        ]);

        if (ticketResult.status === 'rejected' && slackNotificationResult.status === 'rejected') {
            LocalNotificationService.showError({ title: 'Error', message: 'Failed to submit request' });
        } else {
            if (ticketResult.status === 'rejected' || slackNotificationResult.status === 'rejected') {
                LocalNotificationService.showError({ title: 'Warning', message: 'Request submitted with errors' });
            }

            setShowSubmitted(true);
        }
    };

    if (submitted) {
        return <Text ta="center">Your request has been submitted. We will get back to you shortly!</Text>;
    }

    return (
        <Stack>
            <Textarea
                placeholder="Let us know how we can help"
                value={userMessage}
                onChange={(event) => setUserMessage(event.currentTarget.value)}
                error={error}
                onBlur={() => setError(null)}
            />

            <Button onClick={submitTicket}>Submit Request</Button>
        </Stack>
    );
};

const appendUserInfo = (message: string, user: User, includeMattyCC = false) => {
    const lines = [
        `A user has requested help with subscriptions:`,
        '',
        message,
        '',
        '*** User Info ***',
        `Name: ${user.name}`,
        `Email: ${user.email}`,
        `Phone: ${user.phone}`,
    ];

    if (includeMattyCC) {
        lines.push('', 'CC: @Matthew Leccese');
    }

    return lines.join('\n');
};
