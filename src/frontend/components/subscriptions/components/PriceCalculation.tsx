import { FC, ReactNode } from 'react';
import { Card, List, ListItem, Text, Avatar as MantineAvatar, Loader, Space } from '@mantine/core';

import {
    DesignerSubscription,
    SubscriptionBillingCycle,
    CompanySubscription,
    CompanySubscriptionConfig,
    Subscription,
} from 'models';

import { useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { useUser } from 'hooks/use-user';
import { Avatar } from 'components/avatar/Avatar';
import { usePrices } from 'hooks/use-prices';
import { SubscriptionPromotion } from '../SubscriptionPromotion';

import { PriceTag } from './PriceTag';

export const PriceCalculation = () => {
    const { toSubscription, toBillingCycle, numberOfSeats } = useSubscriptionUpdateState();

    if (toSubscription === CompanySubscription.PREMIUM || toSubscription === CompanySubscription.FREE) {
        return <CompanyPriceCalculation subscription={toSubscription} billingCycle={toBillingCycle} />;
    }

    if (toSubscription === DesignerSubscription.PLUS || toSubscription === DesignerSubscription.PRO) {
        return (
            <DesignerPriceCalculation
                subscription={toSubscription}
                billingCycle={toBillingCycle}
                numberOfSeats={numberOfSeats}
            />
        );
    }

    return null;
};

const DesignerPriceCalculation: FC<{
    subscription: DesignerSubscription.PLUS | DesignerSubscription.PRO;
    billingCycle: SubscriptionBillingCycle;
    numberOfSeats: number;
}> = ({ subscription, billingCycle, numberOfSeats }) => {
    const { data: prices } = usePrices();

    if (!prices) return null;

    const price = prices[subscription][billingCycle];
    const total = price * numberOfSeats * (billingCycle === SubscriptionBillingCycle.MONTHLY ? 1 : 12);

    return (
        <PriceCalculationCard subscription={subscription} total={total} billingCycle={billingCycle}>
            <Text>
                <Text span fw={600}>
                    {numberOfSeats} {numberOfSeats === 1 ? 'seat' : 'seats'}
                </Text>
                {' at '}
                <Text span fw={600}>
                    ${price}
                </Text>{' '}
                per seat/month
            </Text>{' '}
            <TeamMembers />
        </PriceCalculationCard>
    );
};

const CompanyPriceCalculation: FC<{
    subscription: CompanySubscription.PREMIUM | CompanySubscription.FREE;
    billingCycle: SubscriptionBillingCycle;
}> = ({ subscription, billingCycle }) => {
    const { data: prices } = usePrices();

    if (!prices) return null;

    const price = prices[subscription][billingCycle];
    const total = price * (billingCycle === SubscriptionBillingCycle.MONTHLY ? 1 : 12);

    const { title } = CompanySubscriptionConfig[subscription];

    return (
        <PriceCalculationCard subscription={subscription} total={total} billingCycle={billingCycle}>
            <Text>
                <Text span fw={600}>
                    {title} subscription
                </Text>
                {' at '}
                <PriceTag subscription={subscription} billingCycle={billingCycle} />
            </Text>{' '}
        </PriceCalculationCard>
    );
};

const PriceCalculationCard: FC<{
    subscription: Subscription;
    total: number;
    billingCycle: SubscriptionBillingCycle;
    children: ReactNode;
}> = ({ subscription, billingCycle, children }) => (
    <Card withBorder shadow="xs" padding="lg">
        {children}
        <Text c="dimmed" span fz="sm">
            {`Billed ${billingCycle === SubscriptionBillingCycle.MONTHLY ? 'monthly' : 'annually'}`}
        </Text>
        <Space h="md" />
        <SubscriptionPromotion subscription={subscription} />
    </Card>
);

const TeamMembers = () => {
    const { team } = useSubscriptionUpdateState();

    return (
        <>
            <Text mb="xs" mt="lg" fw="bold">
                Team Members:
            </Text>
            <List>
                {team?.users.map(({ user: userId }) => (
                    <UserListItem key={userId} userId={userId} />
                ))}
            </List>
        </>
    );
};

const UserListItem: FC<{ userId: string }> = ({ userId }) => {
    const { user } = useUser(userId);

    if (!user) {
        return (
            <ListItem icon={<MantineAvatar size="xs" />}>
                <Loader type="dots" size="sm" />
            </ListItem>
        );
    }

    return <ListItem icon={<Avatar user={user} size="xs" />}>{user.name ?? user.email}</ListItem>;
};
