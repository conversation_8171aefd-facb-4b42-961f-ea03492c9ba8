import { useCallback } from 'react';

import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { LegalCheck, SubscriptionUpdateAction } from '../types';

import { TeamService } from 'services/TeamService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { LocalStorageService } from 'services/LocalStorageService';

import { POST_SUBSCRIBE_REDIRECT_STORAGE_KEY } from 'pages/post-subscribe';
import { TicketService } from 'services/TicketService';

export const useSubmitSubscriptionUpdate = () => {
    const dispatch = useSubscriptionUpdateDispatch();
    const {
        toSubscription,
        toBillingCycle,
        numberOfSeats,
        additionalSubscriptions,
        isDowngrade,
        legalCheck,
        team,
        interestedIn,
        redirectUrl,
        postSubmitAction,
    } = useSubscriptionUpdateState();

    return useCallback(async () => {
        const legalCheckRequired = !isDowngrade;
        if (legalCheckRequired && legalCheck !== LegalCheck.PASS) {
            dispatch({ type: SubscriptionUpdateAction.SET_LEGAL_CHECK, payload: LegalCheck.ERROR });
            return;
        }

        dispatch({ type: SubscriptionUpdateAction.SET_LOADING, payload: true });

        try {
            if (isDowngrade) {
                await TeamService.subscriptionRequestDowngrade(toSubscription);
            }

            const { redirect, updatedTeam } = await TeamService.subscriptionSignUp(
                team.id,
                toSubscription,
                toBillingCycle,
                numberOfSeats,
                additionalSubscriptions,
                interestedIn,
            );

            if (postSubmitAction) {
                postSubmitAction();
            }

            if (redirect) {
                const returnUrl = new URL(redirectUrl || window.location.href, window.location.origin);
                returnUrl.searchParams.append('subscription', toSubscription);

                LocalStorageService.store(POST_SUBSCRIBE_REDIRECT_STORAGE_KEY, returnUrl.toString());

                window.location = redirect;
            } else if (updatedTeam) {
                LocalNotificationService.showSuccess({ message: 'Subscription updated successfully!' });

                dispatch({ type: SubscriptionUpdateAction.HANDLE_SUBMIT, payload: updatedTeam });

                TeamService.refresh(team.id, updatedTeam);
            } else {
                throw new Error('Neither updated team nor redirect URL returned from subscription update');
            }
        } catch (error) {
            console.error('Error changing subscription', error);
            LocalNotificationService.showError({ message: 'Error changing subscription' });

            TicketService.createTicket({
                subject: 'User Encountered Error Changing Subscription',
                userMessage: [
                    `Team: ${team.name} (${team.id})`,
                    `Subscription Upgrade Attempted: ${toSubscription}`,
                    `Seat Count Attempted: ${numberOfSeats}`,
                    `Additional Subscriptions Attempted: ${additionalSubscriptions.join(', ')}`,
                    `Error message: ${JSON.stringify(error)}`,
                ].join('\n'),
            });
        }

        dispatch({ type: SubscriptionUpdateAction.SET_LOADING, payload: false });
        dispatch({ type: SubscriptionUpdateAction.CLOSE_MODAL });
        dispatch({ type: SubscriptionUpdateAction.SET_LEGAL_CHECK, payload: LegalCheck.FAIL });
        dispatch({ type: SubscriptionUpdateAction.RESET_REDIRECT });
    }, [
        toSubscription,
        toBillingCycle,
        numberOfSeats,
        additionalSubscriptions,
        isDowngrade,
        legalCheck,
        dispatch,
        team.id,
    ]);
};
