import React, { <PERSON> } from 'react';

import { BoxProps, Card, Flex, Box } from '@mantine/core';

import { SubscriptionOnboardingButton } from 'components/subscriptions/SubscriptionOnboardingButton';

const SubscriptionOnboardingMessage: FC<
    {
        hideButton?: boolean;
        children: React.ReactNode;
        inline?: boolean;
    } & Omit<BoxProps, 'className'>
> = ({ hideButton = false, inline = true, children, ...props }) => (
    <Card radius="sm" bg="blue.0" shadow="none" {...props}>
        {inline ? (
            <Flex justify="space-between" align="center">
                <Box>{children}</Box>
                {!hideButton && (
                    <Box>
                        <SubscriptionOnboardingButton variant="filled" color="gray">
                            Upgrade
                        </SubscriptionOnboardingButton>
                    </Box>
                )}
            </Flex>
        ) : (
            <>
                <Box>{children}</Box>
                {!hideButton && (
                    <Box mt="xs">
                        <SubscriptionOnboardingButton variant="filled" color="gray">
                            Upgrade
                        </SubscriptionOnboardingButton>
                    </Box>
                )}
            </>
        )}
    </Card>
);

export { SubscriptionOnboardingMessage };
