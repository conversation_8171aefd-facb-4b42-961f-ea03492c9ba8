import React from 'react';

import { ContextModalProps } from '@mantine/modals';

import { FeatureLimit } from 'models';

import { FeatureLimitTracker, DiagramFeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

const FeatureLimitTrackerModal = ({
    context,
    id,
    innerProps,
}: ContextModalProps<
    | { feature: Exclude<FeatureLimit, FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS> }
    | {
          feature: FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS;
          diagramId: string;
      }
    | {
          feature: FeatureLimit.DESIGN_FILE_UPLOADS;
          diagramId: string;
      }
>) => {
    const onUpgradeClick = () => {
        context.closeModal(id);
    };

    switch (innerProps.feature) {
        case FeatureLimit.PROJECTS:
        case FeatureLimit.SIMULATIONS:
        case FeatureLimit.AI_REQUESTS:
            return <FeatureLimitTracker feature={innerProps.feature} onUpgradeClick={onUpgradeClick} />;

        case FeatureLimit.IMAGES:
        case FeatureLimit.DESIGN_FILE_UPLOADS:
            return (
                <DiagramFeatureLimitTracker
                    onUpgradeClick={onUpgradeClick}
                    diagramId={innerProps.diagramId}
                    feature={innerProps.feature}
                />
            );
    }
};

export { FeatureLimitTrackerModal };
