import { FC, type JSX } from 'react';

import { Text, List, Tooltip, Box } from '@mantine/core';

import { IoCheckmark, IoClose } from 'react-icons/io5';

import { DesignerSubscriptionConfig, CompanySubscriptionConfig, Subscription, isDesignerSubscription } from 'models';

import { ImagePreview } from 'components/image-preview/ImagePreview';

const SubscriptionFeatureList: FC<{
    subscription: Subscription;
    checkmark?: boolean;
}> = ({ subscription, checkmark = true }) => {
    const { features } = isDesignerSubscription(subscription)
        ? DesignerSubscriptionConfig[subscription]
        : CompanySubscriptionConfig[subscription];

    const highlightedFeatures = features.filter((feature) => feature.highlight);
    const otherFeatures = features.filter((feature) => !feature.highlight && !feature.notIncluded);
    const notIncludedFeatures = features.filter((feature) => feature.notIncluded);

    const icon = checkmark ? <IoCheckmark color="green" /> : <IoClose color="red" />;

    const groupHighlightedFeatures = isDesignerSubscription(subscription);

    if (!groupHighlightedFeatures) {
        return (
            <List spacing={6} center>
                {features.map((feature) => (
                    <ListItem
                        key={feature.title}
                        {...feature}
                        icon={feature.notIncluded ? <IoClose color="red" /> : icon}
                    />
                ))}
            </List>
        );
    }

    return (
        <List spacing={6}>
            {highlightedFeatures.map((feature) => (
                <ListItem key={feature.title} {...feature} icon={icon} highlight />
            ))}
            {otherFeatures.map((feature) => (
                <ListItem key={feature.title} {...feature} icon={icon} />
            ))}
            {notIncludedFeatures.map((feature) => (
                <ListItem key={feature.title} {...feature} icon={<IoClose color="red" />} notIncluded />
            ))}
        </List>
    );
};

const ListItem: FC<{
    title: string;
    description?: string;
    previewImage?: string;
    highlight?: boolean;
    notIncluded?: boolean;
    icon: JSX.Element;
}> = ({ title, description, previewImage, highlight, icon, notIncluded }) => {
    return (
        <List.Item icon={icon}>
            <Tooltip
                maw={600}
                label={previewImage ? <ImagePreview src={previewImage} w={590} mih={300} /> : description}
                disabled={!description && !previewImage}
                p={previewImage ? 5 : undefined}
            >
                <Box>
                    <Text
                        style={{
                            display: 'inline',
                            cursor: description ? 'help' : 'initial',
                            borderBottom: '1px dashed var(--mantine-color-gray-3)',
                            textDecoration: notIncluded ? 'line-through' : undefined,
                        }}
                        fw={highlight ? 500 : undefined}
                        className={highlight ? 'gradient-cyan-green' : undefined}
                        c={notIncluded ? 'dimmed' : undefined}
                    >
                        {title}
                    </Text>
                    <Text c="dimmed" fz="xs">
                        {description}
                    </Text>
                </Box>
            </Tooltip>
        </List.Item>
    );
};

export { SubscriptionFeatureList };
