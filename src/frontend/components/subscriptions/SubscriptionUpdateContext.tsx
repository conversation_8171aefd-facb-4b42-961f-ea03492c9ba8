import React, { ReactNode, useReducer } from 'react';

import {
    DesignerSubscription,
    CompanySubscription,
    isDowngrade,
    Subscription,
    isDesignerSubscription,
    getDesignerSubscriptionData,
    getCompanySubscriptionData,
    Team,
} from 'models';

import {
    SubscriptionUpdateContext,
    SubscriptionUpdateAction,
    SubscriptionUpdateActionPayload,
    SubscriptionUpdateCoreState,
} from './types';

const SubscriptionUpdateStateContext = React.createContext<SubscriptionUpdateContext | null>(null);
const SubscriptionUpdateDispatchContext = React.createContext<React.Dispatch<SubscriptionUpdateActionPayload> | null>(
    null,
);

type SubscriptionUpdateProviderProps = SubscriptionUpdateCoreState & { children: ReactNode };

export const SubscriptionUpdateProvider = ({ children, ...props }: SubscriptionUpdateProviderProps) => {
    const [state, dispatch] = useReducer(
        (state: SubscriptionUpdateContext, action: SubscriptionUpdateActionPayload) =>
            calculateDerivedValues(subscriptionUpdateReducer(state, action)),
        props,
        calculateDerivedValues,
    );

    return (
        <SubscriptionUpdateDispatchContext.Provider value={dispatch}>
            <SubscriptionUpdateStateContext.Provider value={state}>{children}</SubscriptionUpdateStateContext.Provider>
        </SubscriptionUpdateDispatchContext.Provider>
    );
};

const subscriptionUpdateReducer = (
    state: SubscriptionUpdateContext,
    action: SubscriptionUpdateActionPayload,
): SubscriptionUpdateCoreState => {
    switch (action.type) {
        case SubscriptionUpdateAction.OPEN_MODAL:
            return { ...state, modalOpen: true };
        case SubscriptionUpdateAction.CLOSE_MODAL:
            return { ...state, modalOpen: false };
        case SubscriptionUpdateAction.SET_LOADING:
            return { ...state, loading: action.payload };
        case SubscriptionUpdateAction.SET_LEGAL_CHECK:
            return { ...state, legalCheck: action.payload };

        case SubscriptionUpdateAction.SET_REDIRECT:
            return { ...state, redirectUrl: action.payload };
        case SubscriptionUpdateAction.RESET_REDIRECT:
            return { ...state, redirectUrl: null };

        case SubscriptionUpdateAction.SET_SUBSCRIPTION:
            return { ...state, toSubscription: action.payload };
        case SubscriptionUpdateAction.SET_BILLING_CYCLE:
            return { ...state, toBillingCycle: action.payload };
        case SubscriptionUpdateAction.UPDATE_NUMBER_OF_SEATS:
            return { ...state, numberOfSeats: action.payload };
        case SubscriptionUpdateAction.TOGGLE_ADDITIONAL_SUBSCRIPTION:
            return {
                ...state,
                additionalSubscriptions: state.additionalSubscriptions.includes(action.payload)
                    ? state.additionalSubscriptions.filter((subscription) => subscription !== action.payload)
                    : [...state.additionalSubscriptions, action.payload],
            };

        case SubscriptionUpdateAction.HANDLE_SUBMIT:
            if (state.team.id !== action.payload.id) {
                throw new Error('Tried to refresh team with different ID');
            }

            return {
                ...state,
                team: action.payload,
                ...getCalculatedDefaultValues(state.toSubscription, action.payload),
            };

        case SubscriptionUpdateAction.TOGGLE_INTERESTED_IN:
            return {
                ...state,
                interestedIn: state.interestedIn.includes(action.payload)
                    ? state.interestedIn.filter((subscription) => subscription !== action.payload)
                    : [...state.interestedIn, action.payload],
            };
    }
};

const calculateDerivedValues = (state: SubscriptionUpdateCoreState): SubscriptionUpdateContext => {
    return {
        ...state,
        numberOfSeats: state.numberOfSeats < state.team.users.length ? state.team.users.length : state.numberOfSeats,

        isFree: state.toSubscription === DesignerSubscription.FREE || state.toSubscription === CompanySubscription.FREE,
        isDowngrade: isDowngrade(state.currentSubscription, state.toSubscription),
    };
};

export const useSubscriptionUpdateState = () => {
    const state = React.useContext(SubscriptionUpdateStateContext);

    if (!state) {
        throw new Error('useSubscriptionUpdateState must be used within a SubscriptionUpdateStateContext');
    }

    return state;
};

export const useSubscriptionUpdateDispatch = () => {
    const dispatch = React.useContext(SubscriptionUpdateDispatchContext);

    if (!dispatch) {
        throw new Error('useSubscriptionUpdateDispatch must be used within a SubscriptionUpdateDispatchContext');
    }

    return dispatch;
};

export const getCalculatedDefaultValues = (currentSubscription: Subscription, team: Team) => {
    const subscriptionData = isDesignerSubscription(currentSubscription)
        ? getDesignerSubscriptionData(team.subscriptions)
        : getCompanySubscriptionData(team.subscriptions);

    return {
        currentBillingCycle: team.subscriptionBillingCycle,
        numberOfSeats: subscriptionData?.quantity ?? 1,
        currentSubscription: subscriptionData?.subscription ?? currentSubscription,
    };
};
