import { DesignerSubscription, CompanySubscription, SubscriptionBillingCycle, Subscription, Team, User } from 'models';

export type SubscriptionUpdateCoreState = {
    currentSubscription: DesignerSubscription | CompanySubscription;
    currentBillingCycle: SubscriptionBillingCycle | undefined;
    toSubscription: DesignerSubscription | CompanySubscription;
    toBillingCycle: SubscriptionBillingCycle;
    numberOfSeats: number;
    additionalSubscriptions: Subscription[];
    interestedIn: string[];

    modalOpen: boolean;
    loading: boolean;
    legalCheck: LegalCheck;

    team: Team;
    user: User;

    redirectUrl?: string | null;
    postSubmitAction?: () => void;
};

export type SubscriptionUpdateContext = SubscriptionUpdateCoreState & {
    isFree: boolean;
    isDowngrade: boolean;
};

export enum SubscriptionUpdateAction {
    OPEN_MODAL = 'OPEN_MODAL',
    CLOSE_MODAL = 'CLOSE_MODAL',
    SET_LOADING = 'SET_LOADING',
    SET_LEGAL_CHECK = 'SET_LEGAL_CHECK',

    SET_SUBSCRIPTION = 'SET_SUBSCRIPTION',
    SET_BILLING_CYCLE = 'SET_BILLING_CYCLE',
    UPDATE_NUMBER_OF_SEATS = 'UPDATE_NUMBER_OF_SEATS',
    TOGGLE_ADDITIONAL_SUBSCRIPTION = 'TOGGLE_ADDITIONAL_SUBSCRIPTION',
    TOGGLE_INTERESTED_IN = 'TOGGLE_INTERESTED_IN',
    HANDLE_SUBMIT = 'HANDLE_SUBMIT',

    SET_REDIRECT = 'SET_REDIRECT',
    RESET_REDIRECT = 'RESET_REDIRECT',
}

export type SubscriptionUpdateActionPayload =
    | { type: SubscriptionUpdateAction.OPEN_MODAL }
    | { type: SubscriptionUpdateAction.CLOSE_MODAL }
    | { type: SubscriptionUpdateAction.SET_LOADING; payload: boolean }
    | { type: SubscriptionUpdateAction.SET_LEGAL_CHECK; payload: LegalCheck }
    | { type: SubscriptionUpdateAction.SET_SUBSCRIPTION; payload: DesignerSubscription | CompanySubscription }
    | { type: SubscriptionUpdateAction.SET_BILLING_CYCLE; payload: SubscriptionBillingCycle }
    | { type: SubscriptionUpdateAction.UPDATE_NUMBER_OF_SEATS; payload: number }
    | { type: SubscriptionUpdateAction.TOGGLE_ADDITIONAL_SUBSCRIPTION; payload: Subscription }
    | { type: SubscriptionUpdateAction.TOGGLE_INTERESTED_IN; payload: string }
    | { type: SubscriptionUpdateAction.HANDLE_SUBMIT; payload: Team }
    | { type: SubscriptionUpdateAction.SET_REDIRECT; payload: string }
    | { type: SubscriptionUpdateAction.RESET_REDIRECT };

export enum LegalCheck {
    PASS,
    FAIL,
    ERROR,
}
