import React, { FC } from 'react';
import {
    CompanyProfile,
    CompanySubscription,
    getCompanySubscriptionData,
    PermissionCompany,
    PublishedStatus,
} from 'models';

import { Text } from '@mantine/core';

import { IoStarSharp } from 'react-icons/io5';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SubscriptionUpdateWrapper } from '../SubscriptionUpdateWrapper';
import { SubscriptionButton } from '../components/SubscriptionButton';
import { useTeam } from 'hooks/use-team';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { Alert } from 'components/alert/Alert';

const CompanySubscriptionNotice: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { team } = useTeam(company.team);
    const canEdit = useProfilePermission(PermissionCompany.EDIT, false, company);

    if (!team) {
        return null;
    }

    if (!canEdit) {
        return null;
    }

    const subscription = getCompanySubscriptionData(team.subscriptions)?.subscription ?? CompanySubscription.NONE;

    if (
        subscription !== CompanySubscription.PREMIUM &&
        !(company.status === PublishedStatus.DRAFT && subscription === CompanySubscription.NONE)
    ) {
        return (
            <Alert
                title="Upgrade to Premium"
                icon={<IoStarSharp size={36} />}
                rightSection={
                    <SubscriptionUpdateWrapper
                        toSubscription={CompanySubscription.PREMIUM}
                        currentSubscription={subscription}
                        team={team}
                        redirectUrl={CompanyProfileHelpers.urls.view(company.slug)}
                    >
                        <SubscriptionButton buttonLabel="Upgrade Profile" />
                    </SubscriptionUpdateWrapper>
                }
                color="cyan"
            >
                <Text inherit>
                    Upgrade today to make your profile and products 24/7 discoverable in search results. In addition,
                    gain access to valuable insights and analytics to track your performance.
                </Text>
            </Alert>
        );
    }

    return null;
};

export { CompanySubscriptionNotice };
