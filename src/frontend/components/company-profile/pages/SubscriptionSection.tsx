import React, { <PERSON> } from 'react';
import { Team } from 'models';

import { Anchor, Card, Stack, Text, Title } from '@mantine/core';
import Link from 'next/link';

import { Section } from 'components/section/Section';
import { useCurrentTeam } from 'hooks/use-current-team';
import { CompanyProfileSubscription } from 'components/company-subscription/CompanyProfileSubscription';

import cx from './styles.module.scss';
import { useCurrentProfile } from '../hooks/use-current-profile';

const SubscriptionSection: FC<{ team: Team; profileId: string }> = ({ team, profileId }) => (
    <Card classNames={cx}>
        <Section title="Subscription" maw="600">
            <Stack gap="xl">
                <CompanyProfileSubscription team={team} profileId={profileId} />
                <Stack gap="xs">
                    <Title order={3} size="h2" fw={600}>
                        Invoices and Payments
                    </Title>
                    <Text>
                        Manage billing, invoices, and payment methods in your{' '}
                        <Anchor component={Link} href="/account#team">
                            account settings
                        </Anchor>
                        .
                    </Text>
                </Stack>
            </Stack>
        </Section>
    </Card>
);

const WrappedSubscriptionSection = () => {
    const team = useCurrentTeam();
    const profile = useCurrentProfile();

    if (!team || !profile) {
        return null;
    }

    return <SubscriptionSection team={team} profileId={profile.id} />;
};

export { WrappedSubscriptionSection as SubscriptionSection };
