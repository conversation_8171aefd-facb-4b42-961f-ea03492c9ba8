import { FC } from 'react';

import { Card, SimpleGrid, Skeleton, Stack, Text } from '@mantine/core';

import { CompanyProfile, PermissionCompany } from 'models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { About } from 'components/company-profile/sections/About';
import { Contact } from 'components/company-profile/sections/Contact';
import { ReferenceDesigns } from 'components/company-profile/sections/ReferenceDesigns';
import { Products } from 'components/company-profile/sections/Products';
import { ContactPeople } from 'components/company-profile/sections/ContactPeople';
import { CaseStudies } from 'components/company-profile/sections/CaseStudies';
import { RequestAccess } from 'components/company-profile/components/RequestAccess';
import { Stubs } from 'components/company-profile/sections/Stubs';
import { Highlights } from 'components/company-profile/sections/Highlights';
import { Partners } from 'components/company-profile/sections/Partners';
import { Progress } from 'components/company-profile/sections/Progress';

import cx from './styles.module.scss';

const Home: FC<{ company: CompanyProfile }> = ({ company }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);
    const {
        showProgress,
        showAbout,
        showHighlights,
        showReferenceDesigns,
        showProducts,
        showContact,
        showPromos,
        showContactPeople,
        showCaseStudies,
        showPartners,
        shownLocations,
    } = useShowCompanyInfo(company);

    const isEmpty =
        !showAbout &&
        !showHighlights &&
        !showContactPeople &&
        !showPromos &&
        !showPartners &&
        !showProducts &&
        !showReferenceDesigns &&
        !showCaseStudies &&
        !showContact;

    return (
        <Stack gap="md">
            {showProgress && <Progress company={company} />}

            <Card className={`${cx.root} ${cx.home}`}>
                {isEmpty && !canEdit && (
                    <Text c="dimmed">We are still setting up this page. Please come back later.</Text>
                )}

                {(showAbout || showHighlights) && (
                    <Stack gap="md" data-section>
                        {showAbout && <About company={company} />}

                        {showHighlights && <Highlights company={company} highlights={company.highlights} />}
                    </Stack>
                )}

                {showPromos && <Stubs isCarousel field="promos" company={company} stubs={company.promos} />}

                {showProducts && <Products isCarousel company={company} />}

                {showReferenceDesigns && <ReferenceDesigns isCarousel company={company} />}

                {showCaseStudies && <CaseStudies company={company} isCarousel />}

                {showContact && <Contact locations={shownLocations} company={company} />}

                {showContactPeople && <ContactPeople company={company} contacts={company.contactPeople} />}

                {showPartners && <Partners company={company} partners={company.partners} />}
            </Card>
            <RequestAccess company={company} />
        </Stack>
    );
};

const WrappedHome = () => {
    const company = useCurrentProfile();

    if (!company) {
        return (
            <Card classNames={cx}>
                <Stack gap={8}>
                    <Skeleton height={8} radius="xl" />
                    <Skeleton height={8} mt={6} radius="xl" />
                    <Skeleton height={8} mt={6} width="70%" radius="xl" />
                </Stack>
                <SimpleGrid cols={3} h={300}>
                    <Skeleton />
                    <Skeleton />
                    <Skeleton />
                </SimpleGrid>
            </Card>
        );
    }

    return <Home company={company} />;
};

export { WrappedHome as Home };
