import React, { FC, useState } from 'react';

import { CompanyProfile, CompanySubscription, getEffectiveCompanySubscription } from 'models';

import DayJS from 'dayjs';
import useSWR from 'swr';
import { draw, random } from 'radash';

import { Badge, Card, Group, Stack, Table, Text, Title } from '@mantine/core';
import { AreaChart } from '@mantine/charts';
import { DatePickerInput } from '@mantine/dates';
import { TbCalendar } from 'react-icons/tb';

import { Section } from 'components/section/Section';
import { SubscriptionWall } from 'components/company/SubscriptionWall';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useComponent } from 'hooks/use-component';
import { useCurrentUser } from 'hooks/use-current-user';
import { useTeam } from 'hooks/use-team';

import { ApiService } from 'services/ApiService';
import { DateService } from 'services/DateService';

import '@mantine/dates/styles.css';
import cx from './styles.module.scss';
import { AnalyticsProductCalculator } from 'helpers/AnalyticsProductCalculator';
import { AnalyticsViewToggle } from '../components/analytics/AnalyticsViewToggle';
import { AnalyticsPopularProducts } from '../components/analytics/AnalyticsPopularProducts';

const Analytics: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { team } = useTeam(company.team);
    const user = useCurrentUser();
    const [[start, end], setRange] = useState<[Date | null, Date | null]>([
        DayJS().endOf('day').subtract(14, 'days').toDate(),
        DayJS().endOf('day').toDate(),
    ]);

    const key = `/api/tracking/company?from=${start?.getTime()}&to=${end?.getTime()}&company=${company.id}`;
    const { data } = useSWR(key, async () => {
        return ApiService.get(key);
    });

    const subscription = getEffectiveCompanySubscription({
        teamSubscriptions: team?.subscriptions,
        developer: user?.developer,
    });

    const stats = AnalyticsProductCalculator.calculateComponentStats(data?.components || []);
    const totalStats = AnalyticsProductCalculator.calculateComponentTotals(stats);
    if (team && subscription !== CompanySubscription.PREMIUM) {
        return (
            <SubscriptionWall
                company={company}
                team={team}
                title="Unlock Advanced Analytics"
                description={`
                    Make data-driven decisions to boost your performance and Upgrade to
                    Premium for detailed insights on profile views, product impressions,
                    click-through rates, and search ranking
                `}
            >
                <Card classNames={cx}>
                    <Section>
                        <Stack gap="xl">
                            <DummyChart />
                            <DummyChart />
                            <DummyChart />
                            <DummyChart />
                            <DummyChart />
                            <DummyChart />
                        </Stack>
                    </Section>
                </Card>
            </SubscriptionWall>
        );
    }

    if (!team || !data || !data.profile || !data.components) {
        return <Text>No analytics data found. Please check back later!</Text>;
    }

    return (
        <Card classNames={cx}>
            <Section>
                <Group justify="end">
                    <DatePickerInput
                        type="range"
                        value={[start, end]}
                        onChange={(range) => {
                            const from = range[0] ? new Date(range[0]) : null;
                            const to = range[1] ? new Date(range[1]) : null;

                            setRange([from, to]);
                        }}
                        allowSingleDateInRange
                        placeholder="Select a date range"
                        leftSection={<TbCalendar size={16} />}
                    />
                </Group>
                {start && end && (
                    <Stack gap="xl">
                        <AnalyticsViewToggle
                            listView={<AnalyticsPopularProducts totalStats={totalStats} />}
                            chartView={
                                <>
                                    <SearchConsoleChart
                                        namespace="component"
                                        title="Products"
                                        events={data.components}
                                        from={start}
                                        to={end}
                                    />
                                    <ProductTable events={data.components} />
                                </>
                            }
                        />
                        <SearchConsoleChart
                            namespace="caseStudy"
                            title="Case studies"
                            events={data.caseStudies}
                            from={start}
                            to={end}
                        />
                        <SearchConsoleChart
                            namespace="company"
                            title="Profile"
                            events={data.companies}
                            from={start}
                            to={end}
                        />
                        <ProfileViews events={data.profile.views} start={start} end={end} />
                    </Stack>
                )}
            </Section>
        </Card>
    );
};

const ProfileViews: FC<{
    events: any[];
    start: Date;
    end: Date;
}> = ({ events, start, end }) => {
    const views: any[] = [];
    const series = [
        { name: 'home', label: 'Home', color: 'blue' },
        { name: 'products', label: 'Products', color: 'teal' },
        { name: 'templates', label: 'Reference Designs', color: 'red' },
        { name: 'promos', label: 'Announcements', color: 'violet' },
        { name: 'caseStudies', label: 'Case Studies', color: 'green' },
        { name: 'contact', label: 'Contact', color: 'orange' },
    ];

    DateService.interval(start, end).forEach((date) => {
        const item: any = {
            day: DayJS(date).format('D MMM'),
            total: 0,
        };

        series.forEach((entry) => {
            item[entry.name] = 0;
        });

        (events || []).forEach((view: any) => {
            if (DayJS(view.timestamp).isSame(date, 'day')) {
                item[view.data.tab] = item[view.data.tab] || 0;
                item[view.data.tab] += 1;

                item.total += 1;
            }
        });

        views.push(item);
    });

    return (
        <Stack gap="xl" className={cx.graph}>
            <Group justify="space-between">
                <Title fw={700} fz={18} c="black">
                    Profile views
                </Title>
                {events.length && (
                    <Badge variant="light" color="teal" radius="sm">
                        Total views {events.length}
                    </Badge>
                )}
            </Group>
            <AreaChart
                h={300}
                data={views}
                dataKey="day"
                series={series}
                withLegend
                legendProps={{ verticalAlign: 'bottom', align: 'center' }}
                yAxisProps={{ hide: true }}
            />
        </Stack>
    );
};

const SearchConsoleChart: FC<{
    namespace: string;
    title: string;
    events: any[];
    from: Date;
    to: Date;
}> = ({ namespace, title, events, from, to }) => {
    const data: any[] = [];
    const series = [
        { name: 'impressions', label: 'Total Impressions', color: 'blue', yAxisId: 'right' },
        { name: 'clicks', label: 'Total Clicks', color: 'green' },
        { name: 'position', label: 'Average position in search', color: 'orange' },
    ];

    DateService.interval(from, to).forEach((date) => {
        const item: any = {
            day: DayJS(date).format('D MMM'),
        };

        series.forEach((entry) => {
            item[entry.name] = 0;
        });

        (events || []).forEach((event: any) => {
            if (DayJS(event.timestamp).isSame(date, 'day')) {
                if (event.name === `${namespace}.search.view`) {
                    item.impressions += 1;
                    item.positions = item.positions || [];
                    item.positions.push(event.data.position);
                }

                if (event.name === `${namespace}.search.click`) {
                    item.clicks += 1;
                }
            }
        });

        data.push(item);
    });

    data.forEach((item) => {
        if (item.positions) {
            item.position = item.positions.reduce((a: number, b: number) => a + b) / item.positions.length;
            item.position = Math.floor(item.position);
        }
    });

    return (
        <Stack gap="xl" className={cx.graph}>
            <Group justify="space-between">
                <Title fw={700} fz={18} c="black">
                    {title}
                </Title>
                {0 && (
                    <Badge variant="light" color="teal" radius="sm">
                        Total views 0
                    </Badge>
                )}
            </Group>
            <AreaChart
                h={300}
                data={data}
                dataKey="day"
                series={series}
                withLegend
                legendProps={{ verticalAlign: 'bottom', align: 'center' }}
                yAxisProps={{ hide: true }}
            />
        </Stack>
    );
};

const DummyChart: FC = () => {
    const start = DayJS().endOf('day').subtract(7, 'days').toDate();
    const end = DayJS().endOf('day').toDate();
    const data: any[] = [];

    const randomColor = () => {
        return draw(['red', 'green', 'blue', 'orange', 'lime', 'grape', 'violet']) || 'red';
    };

    const series = [
        { name: 'one', label: 'One', color: randomColor() },
        { name: 'two', label: 'Two', color: randomColor() },
    ];

    DateService.interval(start, end).forEach((date) => {
        data.push({
            day: DayJS(date).format('D MMM'),
            one: random(10, 100),
            two: random(10, 100),
        });
    });

    return (
        <Stack gap="xl" className={cx.graph}>
            <Group justify="space-between">
                <Title fw={700} fz={18} c="black">
                    Advanced Analytics
                </Title>
                <Badge variant="light" color="teal" radius="sm">
                    Total views 0
                </Badge>
            </Group>
            <AreaChart
                h={300}
                data={data}
                series={series}
                dataKey="day"
                withLegend
                legendProps={{ verticalAlign: 'bottom', align: 'center' }}
                yAxisProps={{ hide: true }}
            />
        </Stack>
    );
};

const ProductTable: FC<{
    events: any[];
}> = ({ events }) => {
    const components: {
        [key: string]: {
            component: string;
            impressions: number;
            positions: number[];
            clicks: number;
        };
    } = {};

    (events || []).forEach((event: any) => {
        const component = event.data.component;

        if (event.name === 'component.search.view') {
            components[component] = components[component] || {
                component,
                impressions: 0,
                positions: [],
                clicks: 0,
            };

            components[component].impressions += 1;
            components[component].positions.push(event.data.position);
        }

        if (event.name === 'component.search.click') {
            components[component] = components[component] || {
                component,
                impressions: 0,
                positions: [],
                clicks: 0,
            };

            components[component].clicks += 1;
        }
    });

    return (
        <Table>
            <Table.Thead>
                <Table.Tr>
                    <Table.Td>Product</Table.Td>
                    <Table.Td align="center">Impressions</Table.Td>
                    <Table.Td align="center">Average position</Table.Td>
                    <Table.Td align="center">Clicks</Table.Td>
                    <Table.Td align="center">Click through rate</Table.Td>
                </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
                {Object.values(components).map((component) => (
                    <ProductTableRow row={component} key={component.component} />
                ))}
            </Table.Tbody>
        </Table>
    );
};

const ProductTableRow: FC<{ row: any }> = ({ row }) => {
    const { component } = useComponent(row.component);
    const position = Math.floor(row.positions.reduce((a: number, b: number) => a + b) / row.positions.length);
    const clickThroughRate = Math.ceil((row.clicks / row.impressions) * 100);

    return (
        <Table.Tr key={row.component}>
            <Table.Td>{component?.name}</Table.Td>
            <Table.Td align="center">{row.impressions || '-'}</Table.Td>
            <Table.Td align="center">{position}</Table.Td>
            <Table.Td align="center">{row.clicks || '-'}</Table.Td>
            <Table.Td align="center">{clickThroughRate > 0 ? `${clickThroughRate}%` : '-'}</Table.Td>
        </Table.Tr>
    );
};

const WrappedAnalytics = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <Analytics company={company} />;
};

export { WrappedAnalytics as Analytics };
