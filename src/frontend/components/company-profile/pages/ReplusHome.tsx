import { FC } from 'react';

import { Stack } from '@mantine/core';

import { CompanyProfile } from 'models';

import { useProfilesByIds } from 'hooks/use-profiles-by-ids';
import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';

import { ScrollNav } from 'components/scroll-nav';
import { ComponentLanding } from 'components/component-landing';
import { Promos } from 'components/company-profile/sections/Promos';
import { Products } from 'components/company-profile/sections/Products';
import { ReferenceDesigns } from 'components/company-profile/sections/ReferenceDesigns';
import { ContactPeople } from 'components/company-profile/sections/ContactPeople';

const ReplusHome: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { showReferenceDesigns, showProducts, showPromos, showContactPeople } = useShowCompanyInfo(company);

    const { profiles } = useProfilesByIds(company.partners?.map(({ company }) => company).filter(Boolean) ?? []);

    return (
        <Stack gap={40}>
            <ComponentLanding.Companies id="manufacturers" companies={profiles} title="Featured Manufacturers" />

            {showProducts && <Products id="products" company={company} title="Featured Products" nbCols={4} />}

            {showReferenceDesigns && (
                <ReferenceDesigns
                    isCarousel
                    id="referenceDesigns"
                    company={company}
                    title="Featured Reference Designs"
                    nbCols={4}
                />
            )}

            {showPromos && <Promos id="announcements" promos={company.promos} nbCols={4} company={company} />}

            {showContactPeople && <ContactPeople id="team" company={company} contacts={company.contactPeople} />}
        </Stack>
    );
};

const SCROLL_ITEMS = [
    { label: 'Featured Manufacturers', id: 'manufacturers' },
    { label: 'Featured Products', id: 'products' },
    { label: 'Featured Reference Designs', id: 'referenceDesigns' },
    { label: 'Announcements', id: 'announcements' },
    { label: 'Case Studies', id: 'caseStudies' },
    { label: 'Team', id: 'team' },
];

const WrappedReplusHome: FC<{ company: CompanyProfile }> = ({ company }) => {
    return (
        <ScrollNav items={SCROLL_ITEMS} navCols={1.7} topSpacing={50}>
            <ReplusHome company={company} />
        </ScrollNav>
    );
};

export { WrappedReplusHome as ReplusHome, ReplusHome as ReplusHomeComponent };
