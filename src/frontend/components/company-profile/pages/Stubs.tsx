import { FC } from 'react';

import { Card } from '@mantine/core';

import { CompanyProfile, CompanyProfileStubFields } from 'models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { Stubs as StubsComponent } from 'components/company-profile/sections/Stubs';

import cx from './styles.module.scss';

const Stubs: FC<{ company: CompanyProfile; field: CompanyProfileStubFields }> = ({ company, field }) => {
    const stubs = company[field];

    return (
        <Card classNames={cx}>
            <StubsComponent field={field} company={company} stubs={stubs} />
        </Card>
    );
};

const WrappedStubs: FC<{ field: CompanyProfileStubFields }> = ({ field }) => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <Stubs company={company} field={field} />;
};

export { WrappedStubs as Stubs };
