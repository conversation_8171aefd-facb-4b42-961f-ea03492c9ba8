import { FC } from 'react';

import { Card } from '@mantine/core';

import { CompanyProfile } from 'models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import cx from './styles.module.scss';
import { ReferenceDesigns as ReferenceDesignsComponent } from 'components/company-profile/sections/ReferenceDesigns';

const ReferenceDesigns: FC<{ company: CompanyProfile }> = ({ company }) => {
    return (
        <Card classNames={cx}>
            <ReferenceDesignsComponent company={company} />
        </Card>
    );
};

const WrappedReferenceDesigns = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <ReferenceDesigns company={company} />;
};

export { WrappedReferenceDesigns as ReferenceDesigns };
