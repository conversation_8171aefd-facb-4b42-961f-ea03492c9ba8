import { FC, useEffect, useState } from 'react';

import { Affix, Box } from '@mantine/core';
import { useHash } from '@mantine/hooks';

import { CompanyProfile as CompanyProfileType, CompanyService, PermissionCompany } from 'models';

import { Page } from 'components/page';

import { Header } from 'components/company-profile/components/Header';
import { useCompanyProfileNav } from 'components/company-profile/hooks/use-company-profile-nav';
import { HorizontalTabs } from 'components/horizontal-tabs';
import { Banner } from 'components/company-profile/components/Banner';
import { Notices } from 'components/company-profile/components/Notices';
import { CONTENT_WIDTH } from 'components/page/Content';
import { ProfileActions } from 'components/company-profile/components/ProfileActions';
import { ActiveArticleModal } from 'components/company-profile/components/ActiveArticleModal';
import { PreviewModal } from 'components/company-profile/components/PreviewModal';

import { CompanyIntercom } from 'components/intercom/types/CompanyIntercom';
import { currentProfileState, ProfileMode } from 'components/company-profile/state/current-profile';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { useCurrentTeam } from 'hooks/use-current-team';
import { ProfileContext, ProfileModal } from 'components/company-profile/hooks/use-profile-context';
import { AcceptPartnerModal } from 'components/company-profile/components/partners/AcceptPartnerModal';
import { UpgradeMessageModal } from 'components/company-profile/components/UpgradeMessageModal';

import { InternalTrackingService } from 'services/InternalTrackingService';
import { CompanyProfileService } from 'services/CompanyProfileService';

import cx from './CompanyProfile.module.scss';

const CompanyProfile: FC<{ company: CompanyProfileType }> = ({ company }) => {
    const team = useCurrentTeam();
    const { navItems, isLoading } = useCompanyProfileNav(company);
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const [hash, setHash] = useHash();

    // if we do not have the content for active hash, redirect to first tab
    useEffect(() => {
        if (isLoading) return;
        if (!hash) return;

        const hasTabForHash = navItems.find((item) => item.value === hash.replace('#', ''));

        if (!hasTabForHash) {
            setHash(navItems[0].value);
        }
    }, [hash, navItems, isLoading]);

    useEffect(() => {
        InternalTrackingService.track('profile.view', {
            company: company.id,
            tab: hash.replace('#', '') || 'home',
        });
    }, [hash]);

    useEffect(() => {
        if (!team || !canEdit) return;

        const localSignupUrl = CompanyProfileService.getLocalSignupUrl(team.id);

        if (localSignupUrl) {
            CompanyProfileService.finishLocalSignup(team.id);
        }
    }, [team, company]);

    return (
        <Page
            showBackground
            hideLicenseAgreement
            title={company.name}
            breadcrumbs={{
                type: 'floating.fullWidth',
                rightSection: <ProfileActions company={company} />,
            }}
        >
            <Page.FullScreenContent>
                <Banner company={company} />
                <Box maw={CONTENT_WIDTH} mx="auto" mb="xl" w="100%" px="md">
                    <Header company={company} />

                    <Notices company={company} mb="xl" />

                    {/* keepMounted={false} to make sure carousel sections rerender properly on tab change */}
                    <HorizontalTabs tabs={navItems} keepMounted={false} />
                </Box>
                {company.services.includes(CompanyService.IN_APP_SUPPORT) && (
                    <Affix className={cx.intercom}>
                        <CompanyIntercom company={company} />
                    </Affix>
                )}
                <PreviewModal />
                <ActiveArticleModal />
                <UpgradeMessageModal />

                {canEdit && <AcceptPartnerModal company={company} />}
            </Page.FullScreenContent>
        </Page>
    );
};

const WrappedCompanyProfile: FC<{ company: CompanyProfileType }> = ({ company: incomingCompany }) => {
    const company = useCurrentProfile();
    const canEdit = useProfilePermission(PermissionCompany.EDIT, true, incomingCompany);

    const [openedModal, setOpenedModal] = useState<ProfileModal | null>(null);

    useEffect(() => {
        if (canEdit) {
            currentProfileState.mode = ProfileMode.EDIT;
        } else {
            currentProfileState.mode = ProfileMode.VIEW;
        }

        return () => {
            if (canEdit) {
                currentProfileState.mode = ProfileMode.EDIT;
            } else {
                currentProfileState.mode = ProfileMode.VIEW;
            }
        };
    }, [canEdit]);

    useEffect(() => {
        if (incomingCompany) {
            currentProfileState.profile = incomingCompany;
        }

        return () => {
            currentProfileState.profile = null;
        };
    }, [incomingCompany.id]);

    if (!company) return null;

    /*
    const getDarkColor = (color: string) => {
        let darkColor = color;

        while (isLightColor(darkColor)) {
            darkColor = darken(darkColor, 0.1);
        }

        return darkColor;
    };

    return (
        <MantineProvider
            theme={{
                colors: {
                    brand: [...Array(5).fill(company.color), ...Array(5).fill(getDarkColor(company.color))] as any,
                },
                defaultGradient: {
                    deg: 130,
                    from: 'primary',
                    to: 'brandOriginal',
                },
            }}
        >
            <CompanyProfile company={company} />
        </MantineProvider>
    );
    */

    return (
        <ProfileContext.Provider
            value={{
                openedModal,
                openModal: (modal) => setOpenedModal(modal),
                closeModal: () => setOpenedModal(null),
            }}
        >
            <CompanyProfile company={company} />
        </ProfileContext.Provider>
    );
};

export { WrappedCompanyProfile as CompanyProfile };
