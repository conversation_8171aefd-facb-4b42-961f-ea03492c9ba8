import { proxy } from 'valtio';

import { CompanyProfile } from 'models';

export enum ProfileMode {
    VIEW = 'view',
    EDIT = 'edit',
}

type ProjectState = {
    profile: CompanyProfile | null;
    activeArticle: string | null;
    mode: ProfileMode;
};

const currentProfileState = proxy<ProjectState>({
    profile: null,
    activeArticle: null,
    mode: ProfileMode.VIEW,
});

export { currentProfileState };
