import { useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/router';

import { ActionIcon, Box, Button, Transition } from '@mantine/core';
import { IoAnalyticsSharp, IoChatbubblesSharp, IoClose, IoSearchOutline, IoSettingsOutline } from 'react-icons/io5';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { DashboardHighlightedInfo } from 'components/dashboard/components/Dashboard.HighlightedInfo';
import { CompanySubscription } from 'models';

const ITEMS = [
    {
        icon: <IoSearchOutline />,
        value: 'search',
        title: 'Find your products in our AI-powered search',
        button: {
            label: 'Visit product catalog',
            href: RouterHelpers.urls.search(),
        },
    },
    {
        icon: <IoChatbubblesSharp />,
        value: 'support',
        title: 'Engage with customers to understand their needs',
        button: {
            label: 'Go to Lead Management',
            href: '#support-center',
        },
    },
    {
        icon: <IoAnalyticsSharp />,
        value: 'analytics',
        title: 'Learn who is searching for your products and/or services',
        button: {
            label: 'View analytics',
            href: '#analytics',
        },
    },
    {
        icon: <IoSettingsOutline />,
        value: 'manage',
        title: 'Manage your subscription or view your invoices',
        button: {
            label: 'Manage subscription',
            href: '#subscription',
        },
    },
];

const UpgradeSuccesNotice = () => {
    const router = useRouter();
    const [show, setShow] = useState(false);

    useEffect(() => {
        const { subscription, ...updatedQuery } = router.query;

        if (subscription === CompanySubscription.PREMIUM) {
            setShow(true);

            const newPath = {
                pathname: router.pathname,
                query: updatedQuery,
            };

            router.replace(newPath, undefined, { shallow: true }).then();
        }
    }, [router]);

    return (
        <Transition mounted={show} transition="fade-up">
            {(transitionStyles) => (
                <Box style={transitionStyles}>
                    <ActionIcon
                        variant="transparent"
                        color="white"
                        onClick={() => setShow(false)}
                        pos="absolute"
                        right={10}
                        top={10}
                    >
                        <IoClose />
                    </ActionIcon>
                    <DashboardHighlightedInfo
                        nbCols={4}
                        title="Your subscription has been upgraded! What's next?"
                        items={ITEMS.map(({ button, ...item }) => ({
                            ...item,
                            description: (
                                <Button variant="outline" size="xs" component={Link} href={button.href}>
                                    {button.label}
                                </Button>
                            ),
                        }))}
                    />
                </Box>
            )}
        </Transition>
    );
};

export { UpgradeSuccesNotice };
