import React, { FC, useEffect, useMemo, useState } from 'react';
import { Card, Title, Text, Group, Stack, Pagination, Divider } from '@mantine/core';
import { GoTrophy } from 'react-icons/go';

import { AnalyticsPopularProductRow } from './AnalyticsPopularProductRow';
import { AnalyticsProductCalculator, ComponentTotals } from 'helpers/AnalyticsProductCalculator';

interface ProductsPopularContentProps {
    totalStats: ComponentTotals[];
}
export const AnalyticsPopularProducts: FC<ProductsPopularContentProps> = ({ totalStats }) => {
    if (!totalStats || totalStats.length === 0) {
        return null;
    }

    const [page, setPage] = useState(1);
    const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null);
    const [isUserSelection, setIsUserSelection] = useState(false);

    const totalStatsPaginated = AnalyticsProductCalculator.sortComponentTotals(totalStats, page, 'impressions');
    const totalStatsPage = totalStatsPaginated.results;
    const memoizedTotalStatsPage = useMemo(() => totalStatsPage, [totalStatsPage]);

    useEffect(() => {
        if (memoizedTotalStatsPage.length > 0 && !isUserSelection) {
            setSelectedComponentId(memoizedTotalStatsPage[0].componentId);
        }
    }, [page, memoizedTotalStatsPage, isUserSelection]);

    function onRowClick(componentId: string) {
        setSelectedComponentId(componentId);
        setIsUserSelection(true);
    }

    function handleSetPage(newPage: number) {
        setPage(newPage);
        setIsUserSelection(false);
    }

    return (
        <Card withBorder radius="md" padding="md">
            <>
                <Group gap="xs" align="center">
                    <GoTrophy size={24} />
                    <Title order={4}>Your most popular content</Title>
                </Group>

                <Text color="dimmed" size="sm" mt={4}>
                    By impressions
                </Text>

                <Stack gap="xs" style={{ marginTop: 4 }}>
                    {totalStatsPage.map((item) => (
                        <>
                            <AnalyticsPopularProductRow
                                key={item.componentId}
                                item={item}
                                isSelected={selectedComponentId === item.componentId}
                                onRowClick={onRowClick}
                            />
                            <Divider my="sm" />
                        </>
                    ))}
                </Stack>
                <Group justify="center">
                    <Pagination
                        radius="md"
                        size="sm"
                        mt="md"
                        value={page}
                        onChange={handleSetPage}
                        total={totalStatsPaginated.totalPages}
                    ></Pagination>
                </Group>
            </>
        </Card>
    );
};
