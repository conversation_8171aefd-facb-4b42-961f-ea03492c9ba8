import { FC } from 'react';
import { Group, Stack, Text, Box } from '@mantine/core';
import { ComponentThumbnail } from 'components/thumbnail';
import { useComponent } from 'hooks/use-component';
import cx from './AnalyticsPopularProductRow.module.scss';
import { ComponentTotals } from 'helpers/AnalyticsProductCalculator';
import { AnalyticsProductDetails } from './AnalyticsProductDetails';

interface AnalyticsPopularProductRowProps {
    item: ComponentTotals;
    isSelected: boolean;
    onRowClick: (productId: string) => void;
}

const IMAGE_WIDTH = 240;

export const AnalyticsPopularProductRow: FC<AnalyticsPopularProductRowProps> = ({ item, isSelected, onRowClick }) => {
    const componentId = item.componentId;
    const component = componentId ? useComponent(componentId)?.component : undefined;
    return (
        <Box>
            <Group
                align="apart"
                key={componentId}
                style={{
                    flexWrap: 'nowrap',
                    padding: '15px',
                    borderRadius: 'var(--mantine-radius-md)',
                    cursor: isSelected ? 'default' : 'pointer',
                }}
                className={cx.row}
                onClick={() => {
                    onRowClick(componentId);
                }}
            >
                <Stack gap={0} style={{ flex: 1, minWidth: 0 }}>
                    <Text fw={500} lineClamp={1} style={{ textDecoration: 'none', color: 'inherit' }}>
                        {component?.name ?? ''}
                    </Text>
                    <Stack className={cx.stats} gap={0} style={{ flex: 1, minWidth: 0 }}>
                        <Text fw={700}>Impressions: {item.totalImpressions}</Text>
                        <Text size="xs" c="dimmed">
                            Clicks: {item.totalClicks}
                        </Text>
                        <Text size="xs" c="dimmed">
                            Top search queries: {item.stats.map((stat) => stat.query).join(', ')}
                        </Text>
                        <Text size="xs" c="dimmed">
                            Avg. position: {item.averagePosition.toFixed(1)}
                        </Text>
                        <Text size="xs" c="dimmed">
                            Click through rate:{' '}
                            {item.totalClicks >= 1
                                ? Math.ceil((item.totalClicks / item.totalImpressions) * 100) + '%'
                                : 'N/A'}
                        </Text>
                    </Stack>
                </Stack>

                <Group gap="xs" align="center">
                    {component && (
                        <ComponentThumbnail
                            showEmpty
                            component={component}
                            style={{
                                flexShrink: 0,
                                flexGrow: 0,
                                width: IMAGE_WIDTH,
                                height: IMAGE_WIDTH,
                            }}
                        />
                    )}
                </Group>
            </Group>
            {isSelected && <AnalyticsProductDetails stats={item.stats} />}
        </Box>
    );
};
