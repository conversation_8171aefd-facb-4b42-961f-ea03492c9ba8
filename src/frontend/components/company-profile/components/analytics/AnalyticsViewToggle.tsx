import { Flex, SegmentedControl } from '@mantine/core';
import { ReactNode, useState } from 'react';
import { TfiViewListAlt } from 'react-icons/tfi';
import { FaChartLine } from 'react-icons/fa6';

interface ViewToggleProps {
    listView: ReactNode;
    chartView: ReactNode;
    onViewChange?: (view: 'list' | 'chart' | 'details') => void;
}

export function AnalyticsViewToggle({ listView, chartView, onViewChange }: ViewToggleProps) {
    const [view, setView] = useState<'list' | 'chart' | 'details'>('chart');

    const handleViewChange = (value: string) => {
        const newView = value as 'list' | 'chart';
        setView(newView);
        onViewChange?.(newView);
    };

    return (
        <div>
            <Flex justify="flex-end">
                <SegmentedControl
                    value={view}
                    onChange={handleViewChange}
                    data={[
                        { label: <TfiViewListAlt size={16} />, value: 'list' },
                        { label: <FaChartLine size={16} />, value: 'chart' },
                    ]}
                    color="blue"
                    size="md"
                    radius="md"
                    transitionDuration={200}
                    transitionTimingFunction="ease"
                />
            </Flex>
            {view === 'list' && listView}
            {view === 'chart' && chartView}
        </div>
    );
}
