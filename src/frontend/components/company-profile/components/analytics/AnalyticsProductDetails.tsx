import React, { FC, useMemo, useState } from 'react';
import { Card, Table } from '@mantine/core';
import { ComponentStats } from 'helpers/AnalyticsProductCalculator';

interface AnalyticsProductDetailsProps {
    stats: ComponentStats[];
}

export const AnalyticsProductDetails: FC<AnalyticsProductDetailsProps> = ({ stats }) => {
    const [sortBy, setSortBy] = useState<keyof ComponentStats>('query');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: keyof ComponentStats) => {
        if (sortBy === field) {
            setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'));
        } else {
            setSortBy(field);
            setSortOrder('asc');
        }
    };

    const sortedStats = useMemo(() => {
        return [...stats].sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            if (sortBy === 'query') {
                return sortOrder === 'asc'
                    ? String(aValue).localeCompare(String(bValue))
                    : String(bValue).localeCompare(String(aValue));
            }

            if (isNaN(Number(aValue))) {
                aValue = 0;
            }
            if (isNaN(Number(bValue))) {
                bValue = 0;
            }
            return sortOrder === 'asc' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
        });
    }, [stats, sortBy, sortOrder]);

    const rows = sortedStats.map((stat, index) => (
        <Table.Tr key={index}>
            <Table.Td
                style={{
                    textAlign: 'left',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                }}
            >
                {stat.query}
            </Table.Td>
            <Table.Td>{isNaN(stat.averagePosition) ? 'N/A' : stat.averagePosition}</Table.Td>
            <Table.Td>{stat.impressions}</Table.Td>
            <Table.Td>{stat.clicks}</Table.Td>
        </Table.Tr>
    ));

    const renderHeader = (label: string, field: keyof ComponentStats) => (
        <Table.Th style={{ paddingRight: '4px' }} onClick={() => handleSort(field)}>
            {label}
            <span style={{ display: 'inline-block', width: '12px', textAlign: 'center', marginLeft: '4px' }}>
                {sortBy === field ? (sortOrder === 'asc' ? '▲' : '▼') : ' '}
            </span>
        </Table.Th>
    );
    return (
        <Card withBorder radius="md" padding="md" mt={15}>
            <Table verticalSpacing="sm" highlightOnHover fs="sm">
                <Table.Thead>
                    <Table.Tr style={{ cursor: 'pointer' }}>
                        {renderHeader('Query', 'query')}
                        {renderHeader('Avg. position', 'averagePosition')}
                        {renderHeader('Impressions', 'impressions')}
                        {renderHeader('Clicks', 'clicks')}
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
            </Table>
        </Card>
    );
};
