import React, { <PERSON> } from 'react';

import { Article, ArticleSchema, CompanyProfile, TipTapHelpers } from 'models';

import { Alert } from '@mantine/core';
import { TbAlertCircle } from 'react-icons/tb';

import { ImageField } from 'components/forms/fields/ImageField';
import { TextField } from 'components/forms/fields/TextField';
import { FileField } from 'components/forms/fields/FileField';
import { URLField } from 'components/forms/fields/URLField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { PreviewModal } from 'components/modals/PreviewModal';
import { CardTeaserPreview } from 'components/teasers/CardTeaserPreview';

import { useRTE } from 'hooks/use-rte';

const ArticleModal: FC<{
    company: CompanyProfile['id'] | CompanyProfile;
    article?: Article;
    onSubmit: (data: Pick<Article, 'name' | 'file' | 'teaser'>) => void;
    onClose: () => void;
    copy: {
        title: string;
        submit: string;
    };
}> = ({ company, article, onSubmit, onClose, copy }) => {
    const { editor, RTEField: DescriptionField } = useRTE(article?.teaser?.description);
    const companyId = typeof company === 'string' ? company : company.id;

    return (
        <PreviewModal<Article>
            title={copy.title}
            defaultValues={
                article
                    ? {
                          name: article.name,
                          file: article.file,
                          teaser: article.teaser,
                      }
                    : {}
            }
            validator={ArticleSchema.extend({
                type: ArticleSchema.shape.type.optional(),
                company: ArticleSchema.shape.type.optional(),
            })}
            onSubmit={(values) => {
                onSubmit({
                    ...values,
                    // @ts-ignore
                    file: values.file?.id || values.file,
                    teaser: {
                        // @ts-ignore
                        ...(values?.teaser || {}),
                        description: editor ? editor.getJSON() : {},
                    },
                } as any);
            }}
            onClose={onClose}
            preview={(values) => {
                const hasDescription = editor ? TipTapHelpers.getText(editor.getJSON())?.length > 0 : false;

                return (
                    <CardTeaserPreview
                        image={values.teaser?.image}
                        name={values.name || 'Add a title'}
                        description={hasDescription && editor ? editor.getJSON() : 'Add a short description'}
                        button={{
                            label: 'More Info',
                            url: 'https://www.preview.com',
                        }}
                    />
                );
            }}
            opened
        >
            {article?.type === 'caseStudy' && !article.teaser.image ? (
                <Alert
                    variant="light"
                    color="yellow"
                    p="xs"
                    title="No image uploaded"
                    icon={<TbAlertCircle size={16} />}
                >
                    Case studies without an image will not be visible to users.
                </Alert>
            ) : null}

            <ImageField
                name="teaser.image"
                label="Thumbnail"
                buttonLabel="Upload an image"
                group={`company:${companyId}:images`}
            />
            <TextField name="name" label="Title" />
            {DescriptionField && <DescriptionField label="Description" editor={editor} mode="minimal" />}
            <Alert color="blue">
                You can upload a PDF file <strong>or</strong> link directly to an external page. PDF files will be{' '}
                <strong>indexed by our search engine</strong>, making your case study easier to find.
            </Alert>
            <FileField
                name="file"
                label="File"
                group={`company:${companyId}:files`}
                buttonLabel="Upload a pdf"
                fileButtonProps={{
                    accept: 'application/pdf',
                }}
            />
            <URLField name="teaser.button.url" label="Link" placeholder="https://www.casestudy.com" />
            <FormSubmit submitOnce>{copy.submit}</FormSubmit>
        </PreviewModal>
    );
};

export { ArticleModal };
