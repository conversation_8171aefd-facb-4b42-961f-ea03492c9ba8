import { useState } from 'react';

import { z } from 'zod';
import { assign } from 'radash';
import { useWatch } from 'react-hook-form';

import { Modal, ModalProps, SimpleGrid, Stack, Text } from '@mantine/core';

import {
    CompanyProfile,
    LocalFile,
    Stub as StubType,
    StubSchema,
    StubWithLocalFiles,
    TipTapHelpers,
    ImageCrop,
} from 'models';

import { getId } from 'helpers/getId';

import { useRTE } from 'hooks/use-rte';

import { Form } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';
import { URLField } from 'components/forms/fields/URLField';
import { ImageField } from 'components/forms/fields/ImageField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { Stub } from 'components/company-profile/components/Stub';
import { SelectField } from 'components/forms/fields/SelectField';

const StubValidator = StubSchema.omit({ files: true }).extend({
    files: z
        .object({
            file: z.string(),
        })
        .array()
        .default([]),
});

const StubModal = ({
    company,
    submitLabel = 'Submit',
    onSumbit,
    defaultStub,
    placeholder,
    ...props
}: {
    company: CompanyProfile;
    submitLabel?: string;
    onSumbit: (data: StubWithLocalFiles) => Promise<void>;
    defaultStub?: StubType;
    placeholder?: string;
} & ModalProps) => {
    const defaultLocalFiles = defaultStub?.files?.map(({ file }) => ({ file: getId(file) as string })) ?? [];

    const [files, setFiles] = useState<LocalFile[]>(defaultLocalFiles);

    const { editor, RTEField } = useRTE(defaultStub?.description, placeholder);

    const handleSubmit = async (data: StubType) => {
        await onSumbit({
            ...data,
            description: editor?.getJSON(),
            files,
        });
    };

    const cleanDefaultValues = {
        ...defaultStub,
        image: getId(defaultStub?.image) ?? '',
        imageCrop: ImageCrop.CROP,
        files: defaultLocalFiles,
    };

    const addFile = (file: string) => {
        setFiles((state) => [...state, { file }]);
    };

    const deleteFile = (file: string) => {
        setFiles((state) => state.filter(({ file: fileId }) => fileId !== file));
    };

    return (
        <Modal {...props} size="xl">
            <Form onSubmit={handleSubmit} defaultValues={cleanDefaultValues} zodSchema={StubValidator}>
                <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg">
                    <Stack>
                        <ImageField
                            name="image"
                            label="Thumbnail"
                            buttonLabel="Upload an image"
                            group={`company:${company.id}:images`}
                        />
                        <SelectField
                            name="imageCrop"
                            label="Thumbnail preview"
                            data={[
                                {
                                    value: ImageCrop.CROP,
                                    label: 'Crop image and fill the space',
                                },
                                {
                                    value: ImageCrop.CONTAIN,
                                    label: 'Show full image (grey background might be visible)',
                                },
                            ]}
                        />
                        <TextField name="name" label="Title" />
                        {editor && (
                            <RTEField
                                editor={editor}
                                label="Description"
                                fileGroup={`company:${company.id}:files`}
                                files={files}
                                addFile={addFile}
                                deleteFile={deleteFile}
                            />
                        )}
                        <URLField name="button.url" label="Link to more info" />
                        <TextField name="button.label" label="More info text" placeholder="eg. Read more" />
                        <FormSubmit>{submitLabel}</FormSubmit>
                    </Stack>

                    <StubModal.Preview editor={editor} />
                </SimpleGrid>
            </Form>
        </Modal>
    );
};

const StubModalPreview = ({ editor }: { editor?: any }) => {
    const { description: _, ...values } = useWatch();

    const hasDescription = TipTapHelpers.getText(editor.getJSON())?.length > 0;

    const previewValues = assign(
        {
            name: 'Add a title',
            description: hasDescription ? editor?.getJSON() : 'Add a short description',
        },
        values,
    );

    return (
        <Stack bg="gray.0" p="md" gap="xs" visibleFrom="sm">
            <Text fz="xs" c="dimmed" fw={500}>
                Preview
            </Text>
            <Stub showEmptyImage {...previewValues} />
        </Stack>
    );
};

StubModal.Preview = StubModalPreview;

export { StubModal };
