import Link from 'next/link';

import { Button, Text, Tooltip } from '@mantine/core';
import { IoHandLeft } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany } from 'models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

export const ClaimProfileButton = ({ company: company }: { company: CompanyProfile }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    if (canEdit || !company.internal) return null;

    return (
        <Tooltip
            position="left"
            label={
                <Text inherit>
                    Do you work at {company.name}?<br />
                    Claim this profile to update info, add products, and more.
                </Text>
            }
        >
            <Button
                size="xs"
                variant="gradient"
                component={Link}
                href={CompanyProfileHelpers.urls.requestAccess(company.slug)}
                leftSection={<IoHandLeft />}
                style={{
                    alignSelf: 'start',
                }}
            >
                Claim This Profile
            </Button>
        </Tooltip>
    );
};
