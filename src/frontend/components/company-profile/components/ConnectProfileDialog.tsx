import { useState } from 'react';

import { Button, Modal, Space, Text, Textarea } from '@mantine/core';
import { useForm } from '@mantine/form';

import { CompanyProfile } from 'models';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

type Props = {
    opened: boolean;
    close: () => void;
    partner: CompanyProfile;
    onSuccess?: () => void;
    company?: CompanyProfile | null;
};

export const ConnectProfileDialog = ({ opened, close, partner, onSuccess, company }: Props) => {
    const form = useForm({
        mode: 'uncontrolled',
        initialValues: {
            description: '',
        },
    });

    const [loading, setLoading] = useState(false);

    const { mutate } = useCurrentTeamCompanies();

    if (!company) {
        return null;
    }

    const connect = async (description: string) => {
        try {
            setLoading(true);
            await CompanyProfileService.addPartner(company, partner.id, { description });
            await mutate();
            setLoading(false);

            onSuccess?.();
        } catch (error) {
            console.error(`Error tagging ${partner} as a partner:`, error);
        }
    };

    return (
        <Modal
            opened={opened}
            withCloseButton
            onClose={close}
            title={`Connect with ${partner.name} as ${company.name}`}
        >
            <form
                onSubmit={form.onSubmit((values) => {
                    connect(values.description);
                })}
            >
                <Text>
                    You are about to connect with <strong>{partner.name}</strong>.
                    <br /> {partner.name} will be added as a partner to the profile of <strong>{company.name}</strong>.
                </Text>

                <Space h="xs" />

                <Textarea
                    placeholder="Tell us more about how you work together..."
                    resize="vertical"
                    key={form.key('description')}
                    {...form.getInputProps('description')}
                />

                <Space h="md" />

                <Button type="submit" loading={loading}>
                    Connect
                </Button>
            </form>
        </Modal>
    );
};
