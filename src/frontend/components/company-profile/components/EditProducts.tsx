import { unique } from 'radash';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { BsPencil } from 'react-icons/bs';

import { CompanyProfile, PermissionCompany } from 'models';

import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';
import { SelectProductForm } from 'components/select-product-form/SelectProductForm';

import { useComponent } from 'hooks/use-component';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

const EditProducts = ({ company }: { company: CompanyProfile }) => {
    const [opened, handlers] = useDisclosure();

    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    if (!canEdit) return null;

    const highlightedComponentIds = company.highlightedComponents ?? [];

    const addHighlightedProduct = async (componentId: string) => {
        await CompanyProfileService.update(company.id, {
            highlightedComponents: unique([componentId, ...highlightedComponentIds]),
        });
    };

    const deleteHighlightedProduct = async (componentId: string) => {
        await CompanyProfileService.update(company.id, {
            highlightedComponents: highlightedComponentIds.filter((id) => id !== componentId),
        });
    };

    return (
        <>
            <Button leftSection={<BsPencil />} size="compact-xs" variant="transparent" onClick={handlers.open}>
                Edit featured products
            </Button>
            <Modal opened={opened} onClose={handlers.close} title="Edit featured products" size="lg">
                <Stack>
                    <SelectProductForm onSelectProduct={addHighlightedProduct} />
                    {highlightedComponentIds.map((componentId) => (
                        <HighlightedProduct
                            key={componentId}
                            componentId={componentId}
                            rightSide={
                                <Button
                                    variant="light"
                                    size="compact-xs"
                                    onClick={() => deleteHighlightedProduct(componentId)}
                                    ml="auto"
                                >
                                    Remove
                                </Button>
                            }
                        />
                    ))}
                </Stack>
            </Modal>
        </>
    );
};

const HighlightedProduct = ({ componentId, rightSide }: { componentId: string; rightSide?: React.ReactNode }) => {
    const { component } = useComponent(componentId);

    if (!component) return null;

    return <ComponentOverviewHitSimple key={component.id} component={component} rightSide={rightSide} />;
};

export { EditProducts };
