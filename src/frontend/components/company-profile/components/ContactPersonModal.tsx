import { z } from 'zod';

import { Modal, ModalProps, Stack } from '@mantine/core';

import { CompanyProfile, CompanyProfileContactSchema } from 'models';

import { getId } from 'helpers/getId';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { TextField } from 'components/forms/fields/TextField';
import { ImageField } from 'components/forms/fields/ImageField';
import { PhoneField } from 'components/forms/fields/PhoneField';

type ContactPerson = CompanyProfile['contactPeople'][0];

const Validator = CompanyProfileContactSchema.extend({
    email: z.string().email().optional(),
});

const ContactPersonModal = ({
    company,
    submitLabel = 'Submit',
    onSumbit,
    defaultContact,
    ...props
}: {
    company: CompanyProfile;
    submitLabel?: string;
    onSumbit: (data: ContactPerson) => Promise<void>;
    defaultContact?: ContactPerson;
} & ModalProps) => {
    const handleSubmit = async (data: ContactPerson) => {
        await onSumbit(data);
    };

    const cleanDefaultValues = {
        ...defaultContact,
        image: getId(defaultContact?.image) ?? '',
    };

    return (
        <Modal {...props}>
            <Form<ContactPerson> onSubmit={handleSubmit} defaultValues={cleanDefaultValues} zodSchema={Validator}>
                <Stack>
                    <ImageField
                        name="image"
                        label="Image"
                        buttonLabel="Upload an image"
                        group={`company:${company.id}:images`}
                    />
                    <TextField name="name" label="Name" />
                    <TextField name="position" label="Position" />
                    <TextField name="email" label="Email" />
                    <PhoneField name="phone" label="Phone" />
                    <FormSubmit>{submitLabel}</FormSubmit>
                </Stack>
            </Form>
        </Modal>
    );
};

export { ContactPersonModal };
