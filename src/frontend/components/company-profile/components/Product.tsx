import { FC } from 'react';

import { CompanyProfile, Component } from 'models';

import { ComponentOverviewHit } from 'components/component-overview';
import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';

const Product: FC<{ component: Component; company?: CompanyProfile; showReferenceDesignButton?: boolean }> = ({
    component,
    company,
    showReferenceDesignButton,
}) => {
    return (
        <ComponentOverviewHit showEmptyImage component={component}>
            {showReferenceDesignButton && company && (
                <ReferenceDesignButton component={component} companyId={company.id} variant="default" mt="md" />
            )}
        </ComponentOverviewHit>
    );
};

export { Product };
