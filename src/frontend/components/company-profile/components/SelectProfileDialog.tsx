import { useState } from 'react';

import { Button, Group, Modal, SimpleGrid, Text } from '@mantine/core';

import { CompanyProfile } from 'models';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyButton } from 'components/company-button/CompanyButton';

type Props = {
    opened: boolean;
    close: () => void;
    selectCompany: (company: CompanyProfile) => void;
};

export const SelectProfileDialog = ({ opened, close, selectCompany }: Props) => {
    const { companies } = useCurrentTeamCompanies();

    const [selectedCompany, setSelectedCompany] = useState<CompanyProfile | null>(null);

    return (
        <Modal opened={opened} withCloseButton onClose={close} size="lg" title="Select a Profile">
            <Text mb="xs">Select the profiles you want to connect with this company.</Text>

            <SimpleGrid cols={{ base: 2, sm: 4 }} spacing={8}>
                {companies.map((company) => (
                    <CompanyButton
                        key={company.id}
                        company={company}
                        onClick={() => setSelectedCompany(company)}
                        selected={selectedCompany?.id === company.id}
                    />
                ))}
            </SimpleGrid>
            <Group justify="center" p="md">
                <Button
                    onClick={() => {
                        selectCompany(selectedCompany!);
                        close();
                    }}
                    disabled={!selectedCompany}
                >
                    Connect
                </Button>
            </Group>
        </Modal>
    );
};
