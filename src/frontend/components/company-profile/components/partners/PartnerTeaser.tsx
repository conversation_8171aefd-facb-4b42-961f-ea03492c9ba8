import { An<PERSON>, <PERSON>, But<PERSON>, Card, Stack, Text, Title, UnstyledButton } from '@mantine/core';

import { CompanyPartner } from 'models';

import { getId } from 'helpers/getId';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';

import { CompanyLogo } from 'components/company-logo';
import { CompanyLogoFallback } from 'components/company-logo/CompanyLogoFallback';

import cx from './PartnerTeaser.module.scss';

const PartnerTeaser = ({
    partner,
    handleEdit,
    handleDelete,
    isDeleting,
    extraButtons,
    withDashedBorder,
}: {
    partner: CompanyPartner;
    handleEdit?: (partner: CompanyPartner) => void;
    handleDelete?: (partner: CompanyPartner) => void;
    isDeleting?: boolean;
    extraButtons?: React.ReactNode;
    withDashedBorder?: boolean;
}) => {
    const { company: partnerCompany } = useCompanyProfile(partner.company);

    const canEdit = !!handleEdit || !!handleDelete;

    const viewUrl = partnerCompany ? CompanyProfileHelpers.urls.view(partnerCompany.slug) : partner.linkedin;

    return (
        <Card withBorder className={`${cx.company} ${withDashedBorder ? cx.withDashedBorder : ''}`}>
            <Box className={cx.companyLogo}>
                <CompanyLogo
                    logos={
                        partnerCompany?.logos ?? {
                            small: getId(partner.logo),
                        }
                    }
                    fallback={<CompanyLogoFallback name={partnerCompany?.name ?? partner.name ?? ''} />}
                />
            </Box>

            <Box fz={16} ta="center" fw={700} c="brand">
                {viewUrl ? (
                    <Anchor inherit c="inherit" href={viewUrl} target="_blank">
                        {partnerCompany?.name ?? partner.name}
                    </Anchor>
                ) : (
                    <Text inherit fz={16} ta="center" fw={700} c="brand">
                        {partnerCompany?.name ?? partner.name}
                    </Text>
                )}
            </Box>

            {partner.description && (
                <Box fz="sm" ta="center">
                    {partner.description}
                </Box>
            )}

            {!partner.description && handleEdit && (
                <UnstyledButton
                    fz="sm"
                    c="dimmed"
                    onClick={() => handleEdit(partner)}
                    style={{
                        borderBottom: '1px dashed var(--mantine-color-gray-3)',
                    }}
                >
                    Add a short description
                </UnstyledButton>
            )}

            {(viewUrl || extraButtons) && (
                <Stack w="100%" gap={4} mt="auto">
                    {viewUrl && (
                        <Button fullWidth size="xs" variant="default" component="a" href={viewUrl} target="_blank">
                            {partnerCompany ? 'View profile' : 'LinkedIn'}
                        </Button>
                    )}

                    {extraButtons}
                </Stack>
            )}

            {canEdit && (
                <Box className={cx.settings}>
                    <SettingsDropdown loading={isDeleting}>
                        {handleEdit && (
                            <SettingsDropdown.Edit onClick={() => handleEdit(partner)}>Edit</SettingsDropdown.Edit>
                        )}
                        {handleDelete && (
                            <SettingsDropdown.Delete onClick={() => handleDelete(partner)}>
                                Delete
                            </SettingsDropdown.Delete>
                        )}
                    </SettingsDropdown>
                </Box>
            )}
        </Card>
    );
};

const PartnerTeaserPlaceholder = ({
    title,
    description,
    extraButtons,
    onClick,
}: {
    title?: string;
    description?: string;
    extraButtons?: React.ReactNode;
    onClick?: () => void;
}) => {
    return (
        <Card className={`${cx.company} ${cx.placeholder}`} data-interactive={!!onClick} onClick={onClick}>
            <Box className={cx.companyLogo}>
                <CompanyLogoFallback name="+" color="var(--mantine-color-brand-4)" />
            </Box>

            {title && (
                <Title order={3} ta="center" c="dimmed">
                    {title}
                </Title>
            )}

            {description && (
                <Box fz="sm" ta="center" c="dimmed">
                    {description}
                </Box>
            )}

            {extraButtons}
        </Card>
    );
};

PartnerTeaser.Placeholder = PartnerTeaserPlaceholder;

export { PartnerTeaser };
