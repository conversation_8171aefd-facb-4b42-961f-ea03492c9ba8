import { useState } from 'react';

import useSWR from 'swr';

import { Box, Button, SimpleGrid } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';
import { useDebouncedValue } from '@mantine/hooks';

import { CompanyProfile } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { usePartnerHandlers } from 'components/company-profile/hooks/use-partner-handlers';

import { SearchBox } from 'components/search-box';
import { PartnerTeaser } from 'components/company-profile/components/partners/PartnerTeaser';

import cx from './ChoosePartnerModal.module.scss';

const ChoosePartnerModal = ({
    innerProps,
}: ContextModalProps<{
    company: CompanyProfile;
}>) => {
    const { company: incomingCompany } = innerProps;

    const { company } = useCompanyProfile(incomingCompany);

    const [query, setQuery] = useState('');
    const [debouncedQuery] = useDebouncedValue(query, 1000);

    const { adding, handleAdd } = usePartnerHandlers({ company: incomingCompany });

    const chosenCompanies = company?.partners?.map(({ company }) => company).filter(Boolean);

    const { data, isLoading } = useSWR(
        `/searchPartnerProfiles?query=${debouncedQuery}`,
        async () =>
            CompanyProfileService.searchPartnerProfiles({
                query: debouncedQuery,
                excludeIds: chosenCompanies,
                limit: 5,
            }),
        {
            keepPreviousData: true,
        },
    );

    return (
        <>
            <Box className={cx.search}>
                <SearchBox
                    size="lg"
                    value={query}
                    placeholder="Search for a company"
                    onChange={(event) => setQuery(event.currentTarget.value)}
                    handleReset={query ? () => setQuery('') : undefined}
                    isLoading={isLoading}
                />
            </Box>

            <SimpleGrid cols={{ base: 1, xs: 2, sm: 3 }} spacing="xs" className={cx.results}>
                {data?.docs.map((company) => (
                    <Company
                        key={company.id}
                        company={company}
                        handleAdd={handleAdd}
                        isAdding={adding === company.id}
                        isSelected={chosenCompanies?.includes(company.id)}
                    />
                ))}

                <CreatePlaceholder handleClick={() => CompanyProfileHelpers.openCreatePartnerModal(incomingCompany)} />
            </SimpleGrid>
        </>
    );
};

const Company = ({
    company,
    handleAdd,
    isAdding,
    isSelected,
}: {
    company: CompanyProfile;
    handleAdd: (companyId: string) => void;
    isAdding?: boolean;
    isSelected?: boolean;
}) => {
    return (
        <PartnerTeaser
            partner={{ company: company.id }}
            extraButtons={
                <Button
                    fullWidth
                    size="xs"
                    variant="filled"
                    onClick={() => handleAdd(company.id)}
                    loading={isAdding}
                    disabled={isSelected}
                >
                    Add as partner
                </Button>
            }
        />
    );
};

const CreatePlaceholder = ({ handleClick }: { handleClick: () => void }) => {
    return (
        <PartnerTeaser.Placeholder
            title="Company not found?"
            description="Help expand our database by adding a new company"
            extraButtons={
                <Button fullWidth size="xs" variant="gradient" onClick={handleClick} mt="auto">
                    Add new company
                </Button>
            }
            onClick={handleClick}
        />
    );
};

export { ChoosePartnerModal };
