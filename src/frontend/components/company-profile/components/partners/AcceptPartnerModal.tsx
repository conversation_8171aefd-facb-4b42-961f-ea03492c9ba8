import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { Button, Modal } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { CompanyProfile } from 'models';

import { PartnerTeaser } from 'components/company-profile/components/partners/PartnerTeaser';

import { usePartnerHandlers } from 'components/company-profile/hooks/use-partner-handlers';

const AcceptPartnerModal = ({ company }: { company: CompanyProfile }) => {
    const router = useRouter();
    const [show, handlers] = useDisclosure();

    const [partnerId, setPartnerId] = useState<string | null>(null);

    const { adding, handleAdd } = usePartnerHandlers({ company });

    useEffect(() => {
        const { action, id, ...updatedQuery } = router.query;

        if (action === 'accept-partner' && id) {
            handlers.open();
            setPartnerId(id as string);

            const newPath = {
                pathname: router.pathname,
                query: updatedQuery,
            };

            router.replace(newPath, undefined, { shallow: true }).then();
        }
    }, [router]);

    const handleAddPartner = async (id: string) => {
        await handleAdd(id);
        handlers.close();
    };

    return (
        <Modal opened={show && !!partnerId} onClose={handlers.close} title={`Add as partner of ${company.name}?`}>
            {partnerId && (
                <PartnerTeaser
                    partner={{ company: partnerId }}
                    extraButtons={
                        <Button
                            fullWidth
                            size="xs"
                            variant="filled"
                            onClick={() => handleAddPartner(partnerId)}
                            loading={adding === partnerId}
                        >
                            Connect
                        </Button>
                    }
                />
            )}
        </Modal>
    );
};

export { AcceptPartnerModal };
