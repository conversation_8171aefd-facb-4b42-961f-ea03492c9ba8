import { z } from 'zod';

import { Box, Modal, Stack } from '@mantine/core';

import { CompanyPartner, CompanyProfile } from 'models';

import { getId } from 'helpers/getId';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { URLField } from 'components/forms/fields/URLField';
import { TextField } from 'components/forms/fields/TextField';
import { ImageField } from 'components/forms/fields/ImageField';

import { PartnerTeaser } from 'components/company-profile/components/partners/PartnerTeaser';

const FormValidator = z.object({
    description: z.string().optional(),
});

const FormValidatorPlaceholder = FormValidator.extend({
    name: z.string().min(3),
    linkedin: z.string().url().optional(),
    logo: z.string(),
});

const EditPartnerModal = ({
    partner,
    company,
    opened,
    handleClose,
}: {
    partner: CompanyPartner | null;
    company: CompanyProfile;
    opened: boolean;
    handleClose: () => void;
}) => {
    const handleEdit = async (values: CompanyPartner) => {
        if (!partner) return;

        const updatedPartner = { ...partner, ...values };

        await CompanyProfileService.update(company.id, {
            partners: company.partners.map((other) => (partner.id === other.id ? updatedPartner : other)),
        });

        LocalNotificationService.showSuccess({
            message: 'Partner updated',
        });

        handleClose();
    };

    const zodSchema = partner?.company ? FormValidator : FormValidatorPlaceholder;

    return (
        <Modal opened={opened} onClose={handleClose} withCloseButton={false} size="lg">
            <Form<CompanyPartner>
                onSubmit={handleEdit}
                zodSchema={zodSchema}
                defaultValues={{
                    ...(partner ?? {}),
                    logo: partner ? getId(partner.logo) : '',
                }}
            >
                <Stack>
                    {partner?.company && (
                        <Box style={{ alignSelf: 'center', width: 300 }}>
                            <PartnerTeaser partner={partner} />
                        </Box>
                    )}

                    {!partner?.company && <TextField name="name" label="Partner Name" />}

                    <TextField
                        name="description"
                        label="Description"
                        placeholder="How do you collaborate with this partner?"
                    />

                    {!partner?.company && <URLField name="linkedin" label="LinkedIn" />}

                    {!partner?.company && (
                        <ImageField
                            name="logo"
                            label="Logo"
                            buttonLabel="Upload a logo"
                            group={`company:${company.id}:images`}
                        />
                    )}

                    <FormSubmit>Save changes</FormSubmit>
                </Stack>
            </Form>
        </Modal>
    );
};

export { EditPartnerModal };
