.company {
    display: flex;
    align-items: center;
    gap: var(--mantine-spacing-xs);

    padding: var(--mantine-spacing-xs);

    background-color: rgba(white, 0.5);
}

.withDashedBorder {
    border: 2px dashed var(--mantine-color-gray-3);
}

.placeholder {
    @extend .withDashedBorder;

    background-color: transparent;

    &[data-interactive='true'] {
        cursor: pointer;
    }
}

.companyLogo {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 120px;
    height: 120px;

    aspect-ratio: 1 / 1;

    img {
        max-width: 100%;
        max-height: 100%;

        mix-blend-mode: multiply;
    }
}

.settings {
    position: absolute;
    top: 8px;
    right: 8px;
}
