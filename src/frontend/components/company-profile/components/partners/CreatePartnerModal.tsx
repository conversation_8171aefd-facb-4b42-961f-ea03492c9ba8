import { z } from 'zod';

import { <PERSON><PERSON>, Stack } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { CompanyPartner, CompanyProfile } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { URLField } from 'components/forms/fields/URLField';
import { TextField } from 'components/forms/fields/TextField';
import { ImageField } from 'components/forms/fields/ImageField';

const FormValidator = z.object({
    name: z.string().min(3),
    description: z.string().optional(),
    linkedin: z.string().url().optional(),
    email: z.string().email().optional(),
    logo: z.string(),
});

const CreatePartnerModal = ({
    innerProps,
}: ContextModalProps<{
    company: CompanyProfile;
}>) => {
    const { company: incomingCompany } = innerProps;

    const { company } = useCompanyProfile(incomingCompany);

    const handleAdd = async (partner: CompanyPartner & { email?: string }) => {
        await CompanyProfileService.update(incomingCompany.id, {
            partners: [partner, ...(company?.partners ?? [])],
        });

        LocalNotificationService.showSuccess({
            message: 'Partner added',
        });

        CompanyProfileHelpers.closeModal(CompanyProfileHelpers.modals.createPartner);
    };

    return (
        <Form<CompanyPartner> onSubmit={handleAdd} zodSchema={FormValidator}>
            <Stack>
                <TextField name="name" label="Partner Name" />

                <TextField
                    name="description"
                    label="Description"
                    placeholder="How do you collaborate with this partner?"
                />

                <URLField name="linkedin" label="LinkedIn" />

                <ImageField
                    name="logo"
                    label="Logo"
                    buttonLabel="Upload a logo"
                    group={`company:${incomingCompany.id}:images`}
                />

                <TextField name="email" label="Contact Email (optional)" placeholder="<EMAIL>" />

                <Alert fz="xs" color="primary" p="xs">
                    Help expand our database by adding a contact email. We will contact the partner to add their company
                    profile to our database.
                </Alert>

                <FormSubmit>Add partner</FormSubmit>
            </Stack>
        </Form>
    );
};

export { CreatePartnerModal };
