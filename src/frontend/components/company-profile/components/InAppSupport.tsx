import React from 'react';

import { useWatch } from 'react-hook-form';

import { Box, Stack, Tooltip } from '@mantine/core';

import { CompanyProfile, CompanyService } from 'models';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { FormSubmit } from 'components/forms/FormSubmit';
import { Form, FormOnSubmit } from 'components/forms/Form';

import { InAppSupportFields } from 'components/company-profile/components/InAppSupportFields';

type Props = { company: CompanyProfile; afterSubmit?: () => void };

const InAppSupport = ({ company, afterSubmit = () => {} }: Props) => {
    const { mutate } = useCompanyProfile(company ?? null);

    const onSubmit: FormOnSubmit<CompanyProfile> = async (values) => {
        await CompanyProfileService.update(company.id, values);
        await mutate?.();

        LocalNotificationService.showSuccess({
            message: 'In-App Support settings updated successfully.',
        });

        afterSubmit();
    };

    return (
        <Form
            onSubmit={onSubmit}
            defaultValues={{ services: company.services, users: company.users }}
            data-content-wrapper
        >
            <Stack maw={700}>
                <InAppSupportFields company={company} />
                <InAppSupportSubmit company={company} />
            </Stack>
        </Form>
    );
};

const InAppSupportSubmit = ({ company }: { company: CompanyProfile }) => {
    const [services, users] = useWatch({ name: ['services', 'users'] }) as [
        CompanyProfile['services'],
        CompanyProfile['users'],
    ];

    const wasEnabled = company.services.includes(CompanyService.IN_APP_SUPPORT);
    const isEnabled = services.includes(CompanyService.IN_APP_SUPPORT);
    const hasSupportUsers = users.length > 0;

    const enableSubmit = (wasEnabled && !isEnabled) || (isEnabled && hasSupportUsers);

    const getButtonText = () => {
        if (wasEnabled && !isEnabled) {
            return 'Disable In-App Support';
        }

        if (!wasEnabled) {
            return 'Enable In-App Support';
        }

        return 'Update Support Users';
    };

    const getTooltipText = () => {
        if (!isEnabled) {
            return 'Enable In-App Support to start receiving messages from your leads.';
        }

        if (!hasSupportUsers) {
            return 'Add support users to start receiving messages from your leads.';
        }

        return '';
    };

    return (
        <Tooltip label={getTooltipText()} disabled={enableSubmit}>
            <Box>
                <FormSubmit fullWidth disabled={!enableSubmit}>
                    {getButtonText()}
                </FormSubmit>
            </Box>
        </Tooltip>
    );
};

export { InAppSupport };
