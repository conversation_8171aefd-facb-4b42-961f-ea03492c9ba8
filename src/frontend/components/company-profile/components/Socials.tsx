import { FC } from 'react';

import { ActionIcon, Group } from '@mantine/core';

import { IoGlobe, IoLogoFacebook, IoLogoLinkedin, IoLogoTwitter, IoLogoYoutube } from 'react-icons/io5';
import { IconType } from 'react-icons';

const ICON_SIZE = 14;

const socialIcons: { [key: string]: IconType } = {
    facebook: IoLogoFacebook,
    twitter: IoLogoTwitter,
    linkedin: IoLogoLinkedin,
    youtube: IoLogoYoutube,
    website: IoGlobe,
};

const Socials: FC<{ socials: { [key in keyof typeof socialIcons]?: string | null } | null | undefined }> = ({
    socials,
}) => {
    if (!socials) return null;

    return (
        <Group gap={8}>
            {Object.entries(socials || {}).map(([socialKey, socialUrl]) => {
                if (!socialUrl) return null;

                const Icon = socialIcons[socialKey];

                if (!Icon) return null;

                return (
                    <ActionIcon
                        key={socialKey}
                        size={20}
                        component="a"
                        href={socialUrl}
                        target="_blank"
                        color="brand"
                        disabled={!socialUrl}
                    >
                        <Icon size={ICON_SIZE} />
                    </ActionIcon>
                );
            })}
        </Group>
    );
};

export { Socials };
