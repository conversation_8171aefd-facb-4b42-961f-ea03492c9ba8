import Link from 'next/link';

import { Button, Flex, Tooltip } from '@mantine/core';
import { IoLockOpen } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useSearchParams } from 'next/navigation';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

const RequestAccess = ({ company }: { company: CompanyProfile }) => {
    const search = useSearchParams();
    const canEdit = useProfilePermission(PermissionCompany.EDIT);
    const isPreviewUrl = search?.get('preview');

    if (canEdit || isPreviewUrl) return null;

    return (
        <Flex justify="center" mt="lg">
            <Tooltip label="Do you work for this company? Request access to manage this profile.">
                <Button
                    leftSection={<IoLockOpen />}
                    variant="subtle"
                    size="compact-sm"
                    component={Link}
                    href={CompanyProfileHelpers.urls.requestAccess(company.slug)}
                >
                    Request access
                </Button>
            </Tooltip>
        </Flex>
    );
};

export { RequestAccess };
