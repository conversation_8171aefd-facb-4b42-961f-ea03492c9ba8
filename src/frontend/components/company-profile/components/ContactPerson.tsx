import React from 'react';

import { usePhoneInput } from 'react-international-phone';

import { Anchor, Box, Card, Text, Title } from '@mantine/core';
import { BsTelephoneFill } from 'react-icons/bs';
import { IoMail } from 'react-icons/io5';

import { CompanyProfile, CompanyService } from 'models';

import { Avatar } from 'components/avatar/Avatar';
import { IconWithText } from 'elements/IconWithText';
import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';
import { IKImage } from 'components/ik-image/IKImage';
import { InAppSupportBadge } from 'elements/badge/InAppSupportBadge';

type ContactPerson = CompanyProfile['contactPeople'][0];

const ContactPerson = ({
    id,
    name,
    position,
    image,
    email,
    phone,
    company,
    handleEdit,
    handleDelete,
}: ContactPerson & {
    company: CompanyProfile;
    handleEdit?: (id: string) => void;
    handleDelete?: (id: string) => void;
}) => {
    const { inputValue: formattedPhone } = usePhoneInput({
        value: phone,
    });

    const inAppSupport = company.services.includes(CompanyService.IN_APP_SUPPORT);

    return (
        <Card withBorder style={{ alignItems: 'center', gap: 10 }} ta="center">
            {image ? (
                <IKImage
                    fileOrId={image}
                    width={200}
                    height={200}
                    alt={name}
                    radius={9999}
                    style={{ width: 100, height: 100 }}
                />
            ) : (
                <Avatar name={name} size={100} />
            )}

            <Title order={3} fz="xl" fw={600}>
                {name}
            </Title>

            {position && (
                <Text c="dimmed" fw={600}>
                    {position}
                </Text>
            )}

            {inAppSupport ? (
                <Box mt="auto">
                    <InAppSupportBadge company={company} size="xs" />
                </Box>
            ) : (
                <>
                    {email && (
                        <IconWithText icon={<IoMail />} text={<Anchor href={`mailto:${email}`}>{email}</Anchor>} />
                    )}

                    {phone && (
                        <IconWithText
                            icon={<BsTelephoneFill />}
                            text={<Anchor href={`tel:${phone}`}>{formattedPhone}</Anchor>}
                        />
                    )}
                </>
            )}

            {(handleEdit || handleDelete) && (
                <Box
                    style={{
                        position: 'absolute',
                        top: 'var(--mantine-spacing-xs)',
                        right: 'var(--mantine-spacing-xs)',
                    }}
                >
                    <SettingsDropdown>
                        {handleEdit && (
                            <SettingsDropdown.Edit onClick={() => handleEdit(id)}>Edit</SettingsDropdown.Edit>
                        )}
                        {handleDelete && (
                            <SettingsDropdown.Delete onClick={() => handleDelete(id)}>Delete</SettingsDropdown.Delete>
                        )}
                    </SettingsDropdown>
                </Box>
            )}
        </Card>
    );
};

export { ContactPerson };
