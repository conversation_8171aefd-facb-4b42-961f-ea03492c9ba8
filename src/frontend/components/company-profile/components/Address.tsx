import { FC } from 'react';

import { IoLocationSharp } from 'react-icons/io5';

import { CompanyProfile } from 'models';

import { IconWithText } from 'elements/IconWithText';

const Address: FC<{ location: CompanyProfile['locations'][number] }> = ({ location }) => {
    const { address } = location || {};

    if (!address) return null;

    const shownAddress = [address.city, address.country].filter(Boolean);

    if (!shownAddress.length) return null;

    return <IconWithText icon={<IoLocationSharp size={14} />} text={shownAddress.join(', ')} />;
};

export { Address };
