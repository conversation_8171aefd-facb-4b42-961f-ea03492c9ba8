import { useState } from 'react';

import Link from 'next/link';
import { debounce, unique } from 'radash';

import { <PERSON><PERSON>, Button, Group, Modal, Select, Stack } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { BsPencil } from 'react-icons/bs';

import { CompanyProfile, PermissionCompany } from 'models';

import { useReferenceDesignsBySearch } from 'hooks/use-reference-designs-by-search';
import { useReferenceDesigns } from 'components/company-profile/hooks/use-reference-designs';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { ProjectHelpers } from 'helpers/ProjectHelpers';

const EditReferenceDesigns = ({ company }: { company: CompanyProfile }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const [opened, handlers] = useDisclosure();

    const [search, setSearch] = useState('');
    const { projects: suggestions } = useReferenceDesignsBySearch(search);

    if (!canEdit) return null;

    const debouncedSearch = debounce({ delay: 300 }, setSearch);

    const highlightedIds = company.highlightedProjects ?? [];

    const handleAdd = async (projectId: string) => {
        await CompanyProfileService.update(company.id, {
            highlightedProjects: unique([projectId, ...highlightedIds]),
        });
    };

    const handleDelete = async (projectId: string) => {
        await CompanyProfileService.update(company.id, {
            highlightedProjects: highlightedIds.filter((id) => id !== projectId),
        });
    };

    return (
        <>
            <Button leftSection={<BsPencil />} size="compact-xs" variant="transparent" onClick={handlers.open}>
                Edit featured reference designs
            </Button>
            <Modal opened={opened} onClose={handlers.close} title="Edit featured reference designs" size="lg">
                <Stack>
                    <Select
                        searchable
                        data={suggestions.map((suggestion) => ({
                            value: suggestion.id,
                            label: suggestion.name,
                        }))}
                        label="Select reference design"
                        placeholder="Search by title"
                        clearable
                        onSearchChange={(value) => {
                            debouncedSearch(value);
                        }}
                        onChange={(value) => {
                            if (!value) return;

                            handleAdd(value);
                        }}
                        nothingFoundMessage="Search for a reference design"
                    />

                    <HighlightedReferenceDesigns company={company} onRemove={handleDelete} />
                </Stack>
            </Modal>
        </>
    );
};

const HighlightedReferenceDesigns = ({
    company,
    onRemove,
}: {
    company: CompanyProfile;
    onRemove: (projectId: string) => void;
}) => {
    const { highlightedReferenceDesigns } = useReferenceDesigns(company);

    return (
        <>
            {highlightedReferenceDesigns.map((project) => (
                <Group key={project.id}>
                    <Anchor component={Link} href={ProjectHelpers.urls.editor(project.id)} target="_blank">
                        {project.name}
                    </Anchor>
                    <Button variant="light" size="compact-xs" onClick={() => onRemove(project.id)} ml="auto">
                        Remove
                    </Button>
                </Group>
            ))}
        </>
    );
};

export { EditReferenceDesigns };
