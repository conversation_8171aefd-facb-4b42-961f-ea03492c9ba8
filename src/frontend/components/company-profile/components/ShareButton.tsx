import { FC } from 'react';

import Link from 'next/link';

import { ActionIcon, Menu } from '@mantine/core';
import { IoAdd, IoCheckmark, IoCopyOutline, IoShareOutline } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany, PublishedStatus } from 'models';

import { getId } from 'helpers/getId';

import { useCopyShareUrl } from 'hooks/use-copy-share-url';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { CryptoHelpers } from 'helpers/CryptoHelpers';

const ShareButton: FC<{ company: CompanyProfile }> = ({ company }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const preview = company.status !== PublishedStatus.PUBLISHED;
    const hash = CryptoHelpers.unsafeSimpleHash(company.slug);

    const { clipboard, share } = useCopyShareUrl('profile', preview ? { preview: hash } : undefined);

    return (
        <Menu trigger="click-hover" position="bottom-start" closeOnItemClick={false}>
            <Menu.Target>
                <ActionIcon variant="transparent" size="lg">
                    <IoShareOutline />
                </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
                <Menu.Item leftSection={clipboard.copied ? <IoCheckmark /> : <IoCopyOutline />} onClick={share}>
                    {clipboard.copied ? 'Copied url' : 'Copy url'}
                </Menu.Item>
                {canEdit && (
                    <Menu.Item leftSection={<IoAdd />} component={Link} href={`/account#team-${getId(company.team)}`}>
                        Invite team members
                    </Menu.Item>
                )}
            </Menu.Dropdown>
        </Menu>
    );
};

export { ShareButton };
