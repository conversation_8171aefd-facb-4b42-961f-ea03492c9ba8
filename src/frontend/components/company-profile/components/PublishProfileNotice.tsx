import Link from 'next/link';

import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@mantine/core';
import { useHash } from '@mantine/hooks';
import { IoInformationCircleOutline } from 'react-icons/io5';

import {
    CompanyProfile,
    CompanySubscription,
    getCompanySubscriptionData,
    PermissionCompany,
    PublishedStatus,
} from 'models';

import { useTeam } from 'hooks/use-team';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { Alert } from 'components/alert/Alert';

export const PublishProfileNotice = ({ company: company }: { company: CompanyProfile }) => {
    const { team } = useTeam(company.team);
    const [hash] = useHash();

    const canEdit = useProfilePermission(PermissionCompany.EDIT, false, company);

    if (!team) {
        return null;
    }

    if (!canEdit) {
        return null;
    }

    const subscription = getCompanySubscriptionData(team.subscriptions)?.subscription ?? CompanySubscription.NONE;

    if (company.status === PublishedStatus.REVIEW) {
        return (
            <Alert title="Profile Under Review" icon={<IoInformationCircleOutline size={36} />}>
                <Text inherit>
                    Your profile is currently under review. We will notify you once your profile has been published.
                    <br />
                    <Anchor href="mailto:<EMAIL>" inherit>
                        Contact us
                    </Anchor>{' '}
                    if your profile is not published within the next 24 hours.
                </Text>
            </Alert>
        );
    }

    if (company.status === PublishedStatus.DRAFT) {
        if (subscription === CompanySubscription.NONE) {
            return (
                <Alert
                    title="Select a Subscription"
                    icon={<IoInformationCircleOutline size={36} />}
                    rightSection={
                        hash === '#subscription' ? null : (
                            <Button
                                component={Link}
                                href={CompanyProfileHelpers.urls.view(company.slug, 'subscription')}
                            >
                                Select Subscription
                            </Button>
                        )
                    }
                >
                    <Text inherit>
                        Your profile is currently in draft mode. Please select a subscription. This is required before
                        publishing your profile. Start with the Free plan or choose the Premium plan to feature your
                        profile and products in AI-powered search and suggestions.
                    </Text>
                </Alert>
            );
        }
    }

    return null;
};
