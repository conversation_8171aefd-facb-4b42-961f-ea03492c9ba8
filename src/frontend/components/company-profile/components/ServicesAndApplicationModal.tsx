import React from 'react';

import { pick } from 'radash';

import { Button, Group, Loader, Modal, ModalProps, Stack } from '@mantine/core';

import { CompanyBase, CompanyProfile, CompanyProfileSchema, CompanyService } from 'models';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useCompanyApplicationTags } from 'hooks/use-company-application-tags';

import { FormSubmit } from 'components/forms/FormSubmit';
import { TagsField } from 'components/forms/fields/TagsField';
import { CompanyForm } from 'components/company-form/CompanyForm';
import { ProfileServicesField } from 'components/forms/fields/ProfileServicesFields';
import { CompanyFormServiceTags } from 'components/company-form/CompanyFormServiceTags';

const FormPayload = CompanyProfileSchema.pick({
    services: true,
    serviceTags: true,
    applicationTags: true,
});

const ServicesAndApplicationModal = ({ company, ...props }: { company: CompanyProfile } & ModalProps) => {
    return (
        <Modal title="Edit Services & Application" size="xl" {...props}>
            <ServicesAndApplicationModalForm company={company} onClose={props.onClose} />
        </Modal>
    );
};

const ServicesAndApplicationModalForm = ({
    company: incomingCompany,
    onClose,
}: {
    company: CompanyProfile;
    onClose: ModalProps['onClose'];
}) => {
    const { company, isLoading: isLoadingCompany } = useCompanyProfile(incomingCompany ?? null);
    const { applicationTags } = useCompanyApplicationTags();

    const defaultValues = pick(company ?? incomingCompany, [
        'services',
        'serviceTags',
        'applicationTags',
    ]) as Partial<CompanyBase>;

    if (incomingCompany && isLoadingCompany) return <Loader size="xs" color="primary" />;

    return (
        <CompanyForm
            companyId={incomingCompany.id}
            defaultValues={defaultValues}
            zodSchema={FormPayload}
            onSubmitSuccess={() => {
                onClose();
            }}
        >
            <Stack>
                <Stack>
                    <Stack gap={0}>
                        <ProfileServicesField
                            name="services"
                            label="Type of services your company provides"
                            excludeServices={[CompanyService.IN_APP_SUPPORT]}
                            description="Check all that apply. Multiple options are allowed."
                        />

                        <CompanyFormServiceTags />
                    </Stack>

                    <TagsField
                        name="applicationTags"
                        label="Application"
                        placeholder="Specify the applications you are active in"
                        data={applicationTags}
                    />
                </Stack>

                <Group ml="auto" gap={8}>
                    <Button variant="default" onClick={onClose}>
                        Discard changes
                    </Button>
                    <FormSubmit>Save changes</FormSubmit>
                </Group>
            </Stack>
        </CompanyForm>
    );
};

export { ServicesAndApplicationModal };
