import { z } from 'zod';

import { <PERSON>dal, ModalProps, Stack } from '@mantine/core';

import { CompanyLocationSchema, CompanyProfile } from 'models';

import { getId } from 'helpers/getId';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { TextField } from 'components/forms/fields/TextField';
import { ImageField } from 'components/forms/fields/ImageField';
import { PhoneField } from 'components/forms/fields/PhoneField';
import { AddressAutocompleteField } from 'components/forms/fields/AddressAutocompleteField';
import { CheckboxField } from 'components/forms/fields/CheckboxField';

type Location = CompanyProfile['locations'][0];

const Validator = CompanyLocationSchema.extend({
    name: z.string(),
    contactInformation: z.object({
        email: z.string().email().optional(),
        phone: z.string().optional(),
    }),
});

const LocationModal = ({
    company,
    submitLabel = 'Submit',
    onSumbit,
    defaultLocation,
    ...props
}: {
    company: CompanyProfile;
    submitLabel?: string;
    onSumbit: (data: Location) => Promise<void>;
    defaultLocation?: Location;
} & ModalProps) => {
    const handleSubmit = async (data: Location) => {
        await onSumbit(data);
    };

    const cleanDefaultValues = {
        ...defaultLocation,
        image: getId(defaultLocation?.image) ?? '',
    };

    return (
        <Modal {...props}>
            <Form<Location> onSubmit={handleSubmit} defaultValues={cleanDefaultValues} zodSchema={Validator}>
                <Stack>
                    <ImageField
                        name="image"
                        label="Image"
                        buttonLabel="Upload an image"
                        group={`company:${company.id}:images`}
                    />
                    <TextField name="name" label="Name" />
                    <AddressAutocompleteField name="address" label="Address" />
                    <TextField name="contactInformation.email" label="Email" />
                    <PhoneField name="contactInformation.phone" label="Phone" />
                    <CheckboxField name="isHeadquarter" label="Mark as headquarter" />
                    <FormSubmit>{submitLabel}</FormSubmit>
                </Stack>
            </Form>
        </Modal>
    );
};

export { LocationModal };
