import { FC } from 'react';

import { BoxProps, Stack } from '@mantine/core';

import { CompanyProfile as CompanyProfileType, PermissionCompany } from 'models';

import { UpgradeSuccesNotice } from 'components/company-profile/components/UpgradeSuccesNotice';
import { PublishProfileNotice } from 'components/company-profile/components/PublishProfileNotice';
import { CompanySubscriptionNotice } from 'components/subscriptions/subscription-notice/CompanySubscriptionNotice';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import cx from './Notices.module.scss';

const Notices: FC<{ company: CompanyProfileType } & BoxProps> = ({ company, ...rest }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT, false, company);

    if (!canEdit) {
        return null;
    }

    return (
        <Stack gap="xs" className={cx.root} {...rest}>
            <PublishProfileNotice company={company} />
            <CompanySubscriptionNotice company={company} />
            <UpgradeSuccesNotice />
        </Stack>
    );
};

export { Notices };
