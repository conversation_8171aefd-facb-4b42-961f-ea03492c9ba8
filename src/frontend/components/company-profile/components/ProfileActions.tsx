import Link from 'next/link';

import { useHash } from '@mantine/hooks';
import { Button, Tooltip } from '@mantine/core';
import { IoAddSharp, IoEyeOffOutline, IoEyeOutline } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany } from 'models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { useProfileMode } from 'components/company-profile/hooks/use-profile-mode';
import { ProfileMode } from 'components/company-profile/state/current-profile';

const ProfileActions = ({ company }: { company: CompanyProfile }) => {
    const [, setHash] = useHash();
    const { mode, setMode } = useProfileMode();

    const canEdit = useProfilePermission(PermissionCompany.EDIT);
    const canEditInViewMode = useProfilePermission(PermissionCompany.EDIT, true);

    const createRoute = ComponentHelpers.urls.create({ company });

    return (
        <>
            {canEdit && (
                <>
                    <ReferenceDesignButton size="xs" companyId={company.id} />
                    {createRoute && (
                        <Button
                            size="xs"
                            variant="subtle"
                            component={Link}
                            href={createRoute}
                            leftSection={<IoAddSharp size={12} />}
                        >
                            Add product
                        </Button>
                    )}
                </>
            )}

            {canEditInViewMode && (
                <Tooltip label={mode === ProfileMode.EDIT ? 'View your profile as a visitor' : 'Back to edit mode'}>
                    <Button
                        size="xs"
                        variant="filled"
                        onClick={() => {
                            if (mode === ProfileMode.EDIT) {
                                setHash('');
                            }

                            setMode(mode === ProfileMode.EDIT ? ProfileMode.VIEW : ProfileMode.EDIT);
                        }}
                        leftSection={
                            mode === ProfileMode.EDIT ? <IoEyeOutline size={12} /> : <IoEyeOffOutline size={12} />
                        }
                    >
                        {mode === ProfileMode.EDIT ? 'Enter view mode' : 'Exit view mode'}
                    </Button>
                </Tooltip>
            )}
        </>
    );
};

export { ProfileActions };
