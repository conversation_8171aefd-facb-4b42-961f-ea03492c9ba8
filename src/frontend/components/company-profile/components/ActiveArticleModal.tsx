import { useEffect } from 'react';

import { useRouter } from 'next/router';

import { useActiveArticle } from 'components/company-profile/hooks/use-active-article';

import { ArticleViewModal } from 'components/company-profile/components/ArticleViewModal';

const ActiveArticleModal = () => {
    const router = useRouter();
    const { query, pathname } = router;
    const { article: articleId, ...restQuery } = query;

    const { activeArticle, setActiveArticle } = useActiveArticle();

    const closeView = () => {
        setActiveArticle(null);
    };

    useEffect(() => {
        if (articleId) {
            setActiveArticle(articleId as string);

            router.replace({ pathname, query: restQuery }, undefined, { shallow: true }).then();
        }
    }, [articleId]);

    if (!activeArticle) {
        return null;
    }

    return <ArticleViewModal articleId={activeArticle} onClose={closeView} />;
};

export { ActiveArticleModal };
