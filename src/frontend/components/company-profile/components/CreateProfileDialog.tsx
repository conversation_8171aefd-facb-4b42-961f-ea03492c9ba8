import { useState } from 'react';
import { useRouter } from 'next/router';
import { Button, Modal, Space, Text } from '@mantine/core';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfile } from 'models';

type Props = {
    opened: boolean;
    close: () => void;
    redirect?: string;
    partner?: CompanyProfile['id'];
};

export const CreateProfileDialog = ({ opened, close, redirect, partner }: Props) => {
    const [loading, setLoading] = useState(false);
    const router = useRouter();

    const createProfile = async () => {
        const url = CompanyProfileHelpers.urls.create({
            redirect,
            partner,
        });

        setLoading(true);
        await router.push(url);
        setLoading(false);

        close();
    };

    return (
        <Modal opened={opened} withCloseButton onClose={close} title="Create a Company Profile">
            <Text c="dimmed" fz="sm">
                A company profile allows you to put your products, services, and information in one place.
            </Text>

            <Space h="xs" />

            <Text>In order to connect, you need to create a profile first.</Text>
            <Text>Creating a profile takes less than 5 minutes</Text>

            <Space h="md" />

            <Button onClick={createProfile} disabled={loading} loading={loading}>
                Create profile
            </Button>
        </Modal>
    );
};
