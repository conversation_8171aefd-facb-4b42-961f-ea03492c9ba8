import { FC } from 'react';

import { Text } from '@mantine/core';

import {
    CompanyProfile,
    CompanyProfileStubFields,
    StubPlaceholder as StubPlaceholderType,
    Stub as StubType,
} from 'models';

import { GridSection } from 'components/section/GridSection';
import { Section, SectionProps } from 'components/section/Section';
import { Stub } from 'components/company-profile/components/Stub';
import { CarouselSection } from 'components/section/CarouselSection';
import { StubPlaceholder } from 'components/company-profile/components/StubPlaceholder';

export type StubSectionProps = {
    stubs: StubType[];
    placeholders?: StubPlaceholderType[];
    nbCols?: number;
    isCarousel?: boolean;
    handleEdit?: (id: string) => void;
    handleDelete?: (id: string) => void;
    emptyMessage?: string;
    field: CompanyProfileStubFields;
    company: CompanyProfile;
} & SectionProps;

const StubSection: FC<StubSectionProps> = ({
    stubs,
    placeholders,
    nbCols,
    isCarousel,
    emptyMessage = 'No items yet.',
    handleEdit,
    handleDelete,
    field,
    company,
    ...props
}) => {
    const Stubs = stubs.map((stub) => (
        <Stub key={stub.id} {...stub} handleEdit={handleEdit} handleDelete={handleDelete} />
    ));

    const Placeholders = placeholders?.map((props) => (
        <StubPlaceholder key={props.name} {...props} company={company} field={field} />
    ));

    let content = (
        <GridSection nbCols={nbCols}>
            {Stubs}
            {Placeholders}
        </GridSection>
    );

    if (isCarousel) {
        content = (
            <CarouselSection nbCols={nbCols}>
                {Stubs}
                {Placeholders}
            </CarouselSection>
        );
    }

    return (
        <Section {...props}>
            {stubs?.length || placeholders?.length ? content : <Text c="dimmed">{emptyMessage}</Text>}
        </Section>
    );
};

export { StubSection };
