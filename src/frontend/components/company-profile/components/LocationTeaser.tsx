import { FC } from 'react';

import { usePhoneInput } from 'react-international-phone';

import { CompanyProfile, CompanyService, isLocationEmpty } from 'models';

import { Anchor, Box, Stack, Text } from '@mantine/core';

import { IoLocationSharp, IoMail } from 'react-icons/io5';
import { BsTelephoneFill } from 'react-icons/bs';

import { IconWithText } from 'elements/IconWithText';

import { Stub } from 'components/company-profile/components/Stub';
import { InAppSupportBadge } from 'elements/badge/InAppSupportBadge';

type Location = CompanyProfile['locations'][0];

const LocationTeaser: FC<
    Location & {
        company: CompanyProfile;
        handleEdit?: (id: string) => void;
        handleDelete?: (id: string) => void;
    }
> = ({ company, handleEdit, handleDelete, ...location }) => {
    const { id, address, name, description, image, contactInformation } = location;
    const { coordinates: _, ...fieldsToCheck } = address ?? {};
    const hasAddressFields = address && Object.values(fieldsToCheck).some((field) => !!field);

    const { inputValue: formattedPhone } = usePhoneInput({
        value: contactInformation?.phone,
    });

    if (isLocationEmpty(location)) {
        return null;
    }

    const inAppSupport = company.services.includes(CompanyService.IN_APP_SUPPORT);

    return (
        <Stub
            id={id}
            name={name}
            description={description}
            image={image}
            handleEdit={handleEdit}
            handleDelete={handleDelete}
        >
            {hasAddressFields && (
                <IconWithText
                    icon={<IoLocationSharp size={14} />}
                    text={
                        <Stack gap={0}>
                            {(address.street || address.number) && (
                                <Text>
                                    {address.street} {address.number}
                                </Text>
                            )}

                            {address.city && (
                                <Text>
                                    {address.postalCode} {address.city}
                                </Text>
                            )}

                            {address.state && <Text>{address.state}</Text>}

                            {address.country && <Text>{address.country}</Text>}
                        </Stack>
                    }
                />
            )}

            {inAppSupport ? (
                <Box>
                    <InAppSupportBadge company={company} size="xs" />
                </Box>
            ) : (
                <>
                    {contactInformation?.email && (
                        <IconWithText
                            icon={<IoMail />}
                            text={
                                <Anchor href={`mailto:${contactInformation.email}`}>{contactInformation.email}</Anchor>
                            }
                        />
                    )}

                    {contactInformation?.phone && (
                        <IconWithText
                            icon={<BsTelephoneFill />}
                            text={<Anchor href={`tel:${contactInformation.phone}`}>{formattedPhone}</Anchor>}
                        />
                    )}
                </>
            )}
        </Stub>
    );
};

export { LocationTeaser };
