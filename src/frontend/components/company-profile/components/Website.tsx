import { FC } from 'react';

import { Anchor } from '@mantine/core';
import { IoGlobeSharp } from 'react-icons/io5';

import { CompanyProfile } from 'models';

import { IconWithText } from 'elements/IconWithText';

const Website: FC<{ website: CompanyProfile['website'] }> = ({ website }) => {
    if (!website) return null;

    return (
        <IconWithText
            icon={<IoGlobeSharp size={14} />}
            text={
                <Anchor href={website} target="_blank" rel="noopener noreferrer">
                    {website}
                </Anchor>
            }
        />
    );
};

export { Website };
