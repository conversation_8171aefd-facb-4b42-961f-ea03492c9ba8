import React, { FC } from 'react';

import { Button, Group, Paper, Text } from '@mantine/core';
import { IoHandLeft } from 'react-icons/io5';

import Link from 'next/link';

import { PermissionCompany } from 'models';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useProfilePreview } from 'components/company-profile/hooks/use-profile-preview';

const PreviewModal: FC = () => {
    const company = useCurrentProfile()!;

    const isEditable = useProfilePermission(PermissionCompany.EDIT);
    const isCreatedByUs = company.internal;

    const { previewSecret, previewSecretIsValid } = useProfilePreview();

    return previewSecretIsValid && isCreatedByUs && !isEditable ? (
        <Group
            justify="center"
            style={{
                position: 'fixed',
                zIndex: 201,
                bottom: 'var(--mantine-spacing-sm)',
                left: 240,
                right: 0,
            }}
        >
            <Paper bg="white" w="100%" maw="500px" p="lg" shadow="md" radius="sm">
                <Text size="lg" fw={700} ta="center">
                    Do you work for {company.name}?
                </Text>
                <Text ta="center">Claim this profile to update info, add products, and more.</Text>
                <Group justify="center" mt="md">
                    <Button
                        component={Link}
                        href={CompanyProfileHelpers.urls.requestAccess(company.id, previewSecret)}
                        leftSection={<IoHandLeft />}
                        variant="gradient"
                    >
                        Claim This Profile
                    </Button>
                </Group>
            </Paper>
        </Group>
    ) : null;
};

export { PreviewModal };
