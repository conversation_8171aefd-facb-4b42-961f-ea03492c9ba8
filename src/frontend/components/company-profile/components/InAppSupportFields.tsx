import React, { useState } from 'react';
import { z } from 'zod';

import { useFormContext, useWatch } from 'react-hook-form';
import {
    Button,
    Card,
    Checkbox,
    Divider,
    Flex,
    Group,
    MantineProvider,
    Stack,
    Text,
    TextInput,
    Title,
} from '@mantine/core';

import { CompanyProfile, CompanyService, User } from 'models';

import { useAction } from 'hooks/use-action';
import { useUser } from 'hooks/use-user';
import { useTeam } from 'hooks/use-team';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { getId } from 'helpers/getId';
import { TeamService } from 'services/TeamService';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { Avatar } from 'components/avatar/Avatar';
import { ProfileServicesField } from 'components/forms/fields/ProfileServicesFields';

import cx from './InAppSupportFields.module.scss';

type Props = { company?: CompanyProfile; hideEnableCheckbox?: boolean };

const InAppSupportFields = ({ company, hideEnableCheckbox }: Props) => {
    const hasInAppSupport = company?.services?.includes(CompanyService.IN_APP_SUPPORT);

    return (
        <Stack gap="xl">
            {!hideEnableCheckbox && !hasInAppSupport && (
                <Stack gap="xs">
                    <Title order={3} size="h2" fw={600}>
                        Enable in-app support
                    </Title>

                    <Text>
                        Enabling in-app support will allow your customers to ask technical and other questions about
                        your products or reference designs. They for instance can ask for guidance on integration.
                    </Text>

                    <ProfileServicesField name="services" shownServices={[CompanyService.IN_APP_SUPPORT]} />
                </Stack>
            )}

            {company && <InAppSupportUsers company={company} />}
        </Stack>
    );
};

const InAppSupportUsers = ({ company: incomingCompany }: { company: CompanyProfile }) => {
    const selectedServices = useWatch({ name: 'services' });

    const { company } = useCompanyProfile(incomingCompany ?? null);
    const { team: companyTeam } = useTeam(incomingCompany.team);

    const [email, setEmail] = useState('');
    const [error, setError] = useState<string | null>(null);

    const [addSupportUser, submittingSupportUser] = useAction(async () => {
        if (!company || !email) return;

        const validationResult = z.string().email().safeParse(email);
        if (!validationResult.success) {
            setError('Please enter a valid email address');
            return;
        }

        const validEmail = validationResult.data;

        try {
            await CompanyProfileService.addSupportUser(company.id, { email: validEmail });

            setEmail('');
            LocalNotificationService.showSuccess({
                message: 'Support user added',
            });

            TeamService.refresh(incomingCompany.team);
            CompanyProfileService.refresh(incomingCompany.id);
        } catch (error) {
            console.error('Error adding support user', error);

            LocalNotificationService.showError({
                message: error?.toString() ?? 'Something went wrong on our side, please try again later.',
            });
        }
    });

    const disabled = !selectedServices?.includes(CompanyService.IN_APP_SUPPORT);

    if (!company || !companyTeam) return null;

    return (
        <MantineProvider
            theme={{
                components: {
                    Button: {
                        defaultProps: {
                            disabled,
                        },
                    },
                    Input: {
                        defaultProps: {
                            disabled,
                        },
                    },
                    Checkbox: {
                        defaultProps: {
                            disabled,
                        },
                    },
                },
            }}
        >
            <Stack gap="xs" className={cx.addUsers} data-disabled={disabled}>
                <Title order={3} size="h2" fw={600}>
                    Who to notify?
                </Title>

                <Text>
                    Select team members to notify when a customer asks a question or a new lead is shared. They will
                    receive a notification in their inbox.
                </Text>

                <Card withBorder>
                    <Stack>
                        <Divider label="Choose from existing users" labelPosition="left" />

                        {companyTeam.users.map((user, index) => (
                            <ExistingSupportUser key={index} userOrId={user.user} />
                        ))}

                        <Divider label="Or invite a new user" labelPosition="left" />

                        <Flex gap={4}>
                            <TextInput
                                value={email}
                                name="inviteUser"
                                placeholder="Invite by email"
                                style={{ flexGrow: 1 }}
                                onChange={(event) => {
                                    setEmail(event.target.value);
                                    setError(null);
                                }}
                                error={error}
                            />
                            <Button
                                disabled={!email}
                                variant="outline"
                                onClick={addSupportUser}
                                loading={submittingSupportUser}
                                radius="xs"
                            >
                                Invite
                            </Button>
                        </Flex>
                    </Stack>
                </Card>
            </Stack>
        </MantineProvider>
    );
};
const ExistingSupportUser = ({ userOrId }: { userOrId: string | User }) => {
    const { user } = useUser(userOrId);

    const selectedUsers = useWatch({ name: 'users' });
    const { setValue } = useFormContext();

    if (!user) return null;

    const id = getId(userOrId)!;

    const toggleUser = () => {
        if (selectedUsers?.includes(id)) {
            setValue(
                'users',
                selectedUsers.filter((userId: string) => userId !== id),
            );
        } else {
            setValue('users', [...(selectedUsers ?? []), id]);
        }
    };

    return (
        <Checkbox
            size="xs"
            mr={4}
            defaultChecked={selectedUsers?.includes(id)}
            onChange={toggleUser}
            styles={{
                body: {
                    alignItems: 'center',
                },
            }}
            label={
                <Group gap={4}>
                    <Avatar user={user} /> <Text>{user.name ?? user.email}</Text>{' '}
                    {user.name && <Text c="dimmed">({user.email})</Text>}
                </Group>
            }
        />
    );
};

export { InAppSupportFields };
