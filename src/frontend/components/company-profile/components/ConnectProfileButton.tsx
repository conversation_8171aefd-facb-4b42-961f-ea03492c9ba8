import { useState } from 'react';
import Link from 'next/link';
import { Menu } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { CompanyProfile } from 'models';
import { IoSettingsOutline } from 'react-icons/io5';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { useCurrentUser } from 'hooks/use-current-user';
import { CreateProfileDialog } from './CreateProfileDialog';
import { SelectProfileDialog } from './SelectProfileDialog';
import { ModalService } from 'services/ModalService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { ConnectButton } from 'components/buttons/ConnectButton';
import { ConnectButtonState } from 'components/buttons/ConnectButton';
import { ConnectProfileDialog } from './ConnectProfileDialog';

type Props = {
    company: CompanyProfile;
};

export const ConnectProfileButton = ({ company }: Props) => {
    const [state, setState] = useState(ConnectButtonState.DEFAULT);

    const [showCreateProfileDialog, { open: openCreateProfileDialog, close: closeCreateProfileDialog }] =
        useDisclosure(false);
    const [selectProfileDialog, { open: openSelectProfileDialog, close: closeSelectProfileDialog }] =
        useDisclosure(false);
    const [showConnectProfileDialog, { open: openConnectProfileDialog, close: closeConnectProfileDialog }] =
        useDisclosure(false);

    const user = useCurrentUser();
    const { companies } = useCurrentTeamCompanies();
    const myOnlyCompany = companies.length === 1 ? companies[0] : null;

    const [activeCompany, setActiveCompany] = useState<CompanyProfile | null>(myOnlyCompany);

    const hasAccount = !!user;
    const hasProfile = Boolean(companies.length);
    const hasManyProfiles = companies.length > 1;

    const activeCompanyIsConnected = activeCompany?.partners
        ? Object.values(activeCompany.partners)
              .map(({ company }) => company)
              .includes(company.id)
        : false;

    const tagAsPartner = async () => {
        if (!hasAccount) {
            // Login first
            ModalService.openLoginModal({
                message: 'Enter your email address to connect with this company.',
            });
            return;
        } else if (!hasProfile) {
            // Create a profile first
            openCreateProfileDialog();
            return;
        } else if (hasManyProfiles) {
            // Select a profile first
            openSelectProfileDialog();
            return;
        } else if (activeCompany && !activeCompanyIsConnected) {
            // Tag as partner
            openConnectProfileDialog();
        }
    };

    const enableDropdown = activeCompanyIsConnected;

    return (
        <>
            <Menu trigger="hover" position="bottom-start" disabled={!enableDropdown}>
                <Menu.Target>
                    <ConnectButton state={state} onClick={tagAsPartner} />
                </Menu.Target>
                <Menu.Dropdown>
                    {activeCompanyIsConnected && activeCompany && (
                        <Menu.Item
                            leftSection={<IoSettingsOutline />}
                            component={Link}
                            href={CompanyProfileHelpers.urls.view(activeCompany.slug)}
                        >
                            Manage connections
                        </Menu.Item>
                    )}
                </Menu.Dropdown>
            </Menu>
            <CreateProfileDialog
                opened={showCreateProfileDialog}
                close={closeCreateProfileDialog}
                partner={company.id}
            />
            <SelectProfileDialog
                opened={selectProfileDialog}
                close={closeSelectProfileDialog}
                selectCompany={(company) => {
                    setActiveCompany(company);
                    openConnectProfileDialog();
                }}
            />
            <ConnectProfileDialog
                opened={showConnectProfileDialog}
                close={() => {
                    if (!myOnlyCompany) {
                        setActiveCompany(null);
                    }

                    closeConnectProfileDialog();
                }}
                partner={company}
                company={activeCompany}
                onSuccess={() => {
                    setState(ConnectButtonState.CONNECTED);
                    closeConnectProfileDialog();
                }}
            />
        </>
    );
};
