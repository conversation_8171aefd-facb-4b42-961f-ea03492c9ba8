import { useDisclosure } from '@mantine/hooks';
import { Button, Group, Modal, Stack, Text } from '@mantine/core';

import { CompanySubscription, getCompanySubscriptionData } from 'models';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SubscriptionUpdateWrapper } from 'components/subscriptions/SubscriptionUpdateWrapper';
import { CompanySubscriptionOptions } from 'components/subscriptions/CompanySubscriptionOptions';
import { CompanyProfileService } from 'services/CompanyProfileService';

const UpgradeMessageModal = () => {
    const team = useCurrentTeam();
    const company = useCurrentProfile();

    const initOpened = Boolean(team && company && company.showUpgradeMessage);

    const [opened, handlers] = useDisclosure(initOpened);

    const subscription = getCompanySubscriptionData(team?.subscriptions)?.subscription ?? CompanySubscription.NONE;

    const dismissUpgradeMessage = () => {
        if (!company) {
            return;
        }

        CompanyProfileService.update(company.id, {
            showUpgradeMessage: false,
        });

        handlers.close();
    };

    return (
        <Modal
            opened={opened}
            size={700}
            title="You're Almost There! Choose Your Subscription Plan"
            onClose={() => {}}
            closeOnEscape={false}
            closeOnClickOutside={false}
            withCloseButton={false}
        >
            <Stack>
                <Text>
                    Congratulations on claiming your profile! Now, you can enhance your visibility and access more
                    features. Choose the plan that best fits your needs:
                </Text>

                {team && company && (
                    <SubscriptionUpdateWrapper
                        toSubscription={CompanySubscription.PREMIUM}
                        currentSubscription={subscription}
                        team={team}
                        redirectUrl={CompanyProfileHelpers.urls.view(company.slug)}
                    >
                        <CompanySubscriptionOptions />
                    </SubscriptionUpdateWrapper>
                )}

                <Group justify="center">
                    <Button variant="transparent" onClick={dismissUpgradeMessage}>
                        Continue to profile and subscribe later
                    </Button>
                </Group>
            </Stack>
        </Modal>
    );
};

export { UpgradeMessageModal };
