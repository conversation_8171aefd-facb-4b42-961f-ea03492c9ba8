import React, { FC } from 'react';

import { Text } from '@mantine/core';

import { GridSection } from 'components/section/GridSection';
import { Section, SectionProps } from 'components/section/Section';
import { CarouselSection } from 'components/section/CarouselSection';

const TeaserSection: FC<
    SectionProps & {
        nbCols?: number;
        isCarousel?: boolean;
        isEmpty?: boolean;
        emptyMessage?: React.ReactNode;
        children?: React.ReactNode;
    }
> = ({ nbCols, isCarousel, isEmpty, emptyMessage = 'No items yet.', children, ...section }) => {
    let content = <GridSection nbCols={nbCols}>{children}</GridSection>;

    if (isCarousel) {
        content = <CarouselSection nbCols={nbCols}>{children}</CarouselSection>;
    }
    return <Section {...section}>{isEmpty ? <Text c="dimmed">{emptyMessage}</Text> : content}</Section>;
};

export { TeaserSection };
