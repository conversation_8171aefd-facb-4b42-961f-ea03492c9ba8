import {
    AIEmbeddingType,
    CompanyProfile,
    CompanyService,
    CompanyServiceOptions,
    isLocationEmpty,
    PermissionCompany,
} from 'models';
import { generateText } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';

import { useProductsByCompany } from 'components/company-profile/hooks/use-products-by-company';
import { useReferenceDesigns } from 'components/company-profile/hooks/use-reference-designs';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { useArticles } from 'hooks/use-articles';

const useShowCompanyInfo = (company: CompanyProfile) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const { referenceDesigns, isLoading: isLoadingDesigns } = useReferenceDesigns(company, canEdit);
    const { products, isLoading: isLoadingProducts } = useProductsByCompany(company, {
        showPrivate: canEdit,
    });
    const { articles: caseStudies } = useArticles(company.id, AIEmbeddingType.CASE_STUDY);

    const shownLocations = company.locations.filter((location) => !isLocationEmpty(location));
    const shownCaseStudies = canEdit ? caseStudies : caseStudies.filter((article) => article.teaser.image);

    const showProgress = canEdit;
    const showHighlights = canEdit || !!company.highlights?.length;
    const showReferenceDesigns = canEdit || !!referenceDesigns.length;
    const showAbout = canEdit || (company.about && generateText(company.about, [StarterKit, Link]));
    const showMission = company.mission && generateText(company.mission, [StarterKit, Link]);
    const showPromos = !!company.promos?.length;
    const showCaseStudies = !!shownCaseStudies.length;
    const showContact = canEdit || !!shownLocations.length;
    const showProducts = canEdit || !!products.length;
    const showContactPeople = canEdit || !!company.contactPeople.length;
    const showPartners = canEdit || !!company.partners?.length;
    const showProfilePage = company.mission || company.about || !!shownCaseStudies.length;
    const showEditInfoPage = canEdit;
    const showProductsPage = canEdit || showProducts;
    const showReferenceDesignsPage = canEdit || showReferenceDesigns;
    const showPromosPage = canEdit || showPromos;
    const showCaseStudiesPage = canEdit || showCaseStudies;

    const filteredServices = CompanyServiceOptions.filter(
        (service) =>
            company.services?.includes(service.value) &&
            ![CompanyService.IN_APP_SUPPORT, CompanyService.OTHER].includes(service.value),
    );
    const shownServices = [...filteredServices.map(({ label }) => label), ...(company.serviceTags ?? [])];

    return {
        showProgress,
        showHighlights,
        showReferenceDesigns,
        showAbout,
        showMission,
        showPromos,
        showCaseStudies,
        showContact,
        shownLocations,
        showProfilePage,
        showProducts,
        showContactPeople,
        showPartners,
        showEditInfoPage,
        showProductsPage,
        showPromosPage,
        showCaseStudiesPage,
        showReferenceDesignsPage,
        shownServices,
        isLoading: isLoadingDesigns || isLoadingProducts,
    };
};

export { useShowCompanyInfo };
