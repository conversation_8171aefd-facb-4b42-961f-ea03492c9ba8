import { unique } from 'radash';

import { CompanyProfile, Project } from 'models';

import { useReferenceDesignsByProfile } from 'hooks/use-reference-designs-by-profile';
import { useReferenceDesignsByIds } from 'hooks/use-reference-designs-by-ids';

const useReferenceDesigns = (company: CompanyProfile, includeUnpublished?: boolean) => {
    const { projects: highlightedReferenceDesigns, isLoading: isLoadingByIds } = useReferenceDesignsByIds(
        company.highlightedProjects,
    );

    const { projects: otherReferenceDesigns, isLoading: isLoadingByOwner } = useReferenceDesignsByProfile(
        company.id,
        includeUnpublished,
    );

    const sortedHighlightedReferenceDesigns = (company.highlightedProjects
        ?.map((id) => highlightedReferenceDesigns.find((project) => project.id === id))
        .filter((project) => !!project) ?? []) as Project[];

    return {
        highlightedReferenceDesigns: sortedHighlightedReferenceDesigns,
        otherReferenceDesigns: otherReferenceDesigns ?? [],
        referenceDesigns: unique(
            [...sortedHighlightedReferenceDesigns, ...(otherReferenceDesigns ?? [])],
            ({ id }) => id,
        ),
        isLoading: isLoadingByIds || isLoadingByOwner,
    };
};

export { useReferenceDesigns };
