import { useSnapshot } from 'hooks/use-safe-snapshot';

import { currentProfileState } from 'components/company-profile/state/current-profile';

const useActiveArticle = () => {
    const { activeArticle } = useSnapshot(currentProfileState);

    return {
        activeArticle,
        setActiveArticle: (activeArticle: string | null) => {
            currentProfileState.activeArticle = activeArticle;
        },
    };
};

export { useActiveArticle };
