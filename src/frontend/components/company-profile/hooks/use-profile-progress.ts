import { CompanyProfile, CompanyProfileHelpers } from 'models';

import { useCompanyCount } from 'components/company-profile/hooks/use-company-count';

const useProfileProgress = (company: CompanyProfile) => {
    const { products, referenceDesigns, caseStudies, isLoading } = useCompanyCount(company);

    if (isLoading) {
        return { progress: [], percentage: null };
    }

    const progress = CompanyProfileHelpers.getProgress(
        company,
        !!products?.total,
        !!referenceDesigns?.total,
        !!caseStudies?.total,
    );

    return {
        progress,
        percentage: CompanyProfileHelpers.getAverageProgress(progress),
    };
};

export { useProfileProgress };
