import useSWRImmutable from 'swr/immutable';

import { publicConfig } from '@public-config';

import { CompanyProfile } from 'models';

import { ApiService } from 'services/ApiService';

const useCompanyCount = (company: CompanyProfile) => {
    const swr = useSWRImmutable<{
        products: { total: number; published: number };
        referenceDesigns: { total: number; published: number };
        caseStudies: { total: number };
    }>(`/companies/${company.id}/count`, () =>
        ApiService.get(`${publicConfig.urls.api}/manufacturers/${company.id}/count`),
    );

    return {
        products: swr.data?.products,
        referenceDesigns: swr.data?.referenceDesigns,
        caseStudies: swr.data?.caseStudies,
        ...swr,
    };
};

export { useCompanyCount };
