import { useState } from 'react';

import { unique } from 'radash';

import { CompanyProfile } from 'models';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { useCompanyProfile } from 'hooks/use-company-profile';

const usePartnerHandlers = ({ company: incomingCompany }: { company: CompanyProfile }) => {
    const { company } = useCompanyProfile(incomingCompany);

    const [adding, setAdding] = useState('');

    const handleAdd = async (id: string) => {
        // filter out unique companies
        const newPartners = unique(
            [
                {
                    company: id,
                },
                ...(company?.partners ?? []),
            ],
            ({ company }) => company,
        );

        setAdding(id);

        await CompanyProfileService.update(incomingCompany.id, {
            partners: newPartners,
        });

        setAdding('');

        LocalNotificationService.showSuccess({
            message: 'Partner added',
        });
    };

    return { adding, handleAdd };
};

export { usePartnerHandlers };
