import { createContext, useContext } from 'react';

export enum ProfileModal {
    EDIT_ABOUT = 'editAbout',
    ADD_CONTACT = 'addContact',
    ADD_LOCATION = 'addLocation',
}

const ProfileContext = createContext<{
    openedModal: ProfileModal | null;
    openModal: (modal: ProfileModal) => void;
    closeModal: () => void;
}>({
    openedModal: null,
    openModal: () => {},
    closeModal: () => {},
});

const useProfileContext = () => useContext(ProfileContext);

export { ProfileContext, useProfileContext };
