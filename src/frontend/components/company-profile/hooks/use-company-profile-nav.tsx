import { IoSettingsOutline } from 'react-icons/io5';

import { CompanyProfile } from 'models';

import { HorizontalTabType } from 'components/horizontal-tabs';

import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';

import { Home } from 'components/company-profile/pages/Home';
import { Stubs } from 'components/company-profile/pages/Stubs';
import { Profile } from 'components/company-profile/pages/Profile';
import { Products } from 'components/company-profile/pages/Products';
import { ReferenceDesigns } from 'components/company-profile/pages/ReferenceDesigns';
import { SupportCenter } from 'components/company-profile/pages/SupportCenter';
import { Analytics } from 'components/company-profile/pages/Analytics';
import { Settings } from 'components/company-profile/pages/Settings';
import { CaseStudies } from 'components/company-profile/pages/CaseStudies';
import { SubscriptionSection } from 'components/company-profile/pages/SubscriptionSection';

const useCompanyProfileNav = (company: CompanyProfile) => {
    const {
        showReferenceDesignsPage,
        showPromosPage,
        showCaseStudiesPage,
        showProductsPage,
        showEditInfoPage,
        isLoading,
    } = useShowCompanyInfo(company);

    // pages
    const pages: { [key: string]: HorizontalTabType } = {
        home: { value: 'home', label: 'Home', content: <Home /> },
        profile: { value: 'profile', label: 'Profile', content: <Profile /> },
        products: {
            value: 'products',
            label: 'Products',
            content: <Products />,
        },
        templates: {
            value: 'templates',
            label: 'Reference Designs',
            content: <ReferenceDesigns />,
        },
        promos: {
            value: 'promos',
            label: 'Announcements',
            content: <Stubs field="promos" />,
        },
        caseStudies: {
            value: 'caseStudies',
            label: 'Case Studies',
            content: <CaseStudies />,
        },
        supportCenter: { value: 'support-center', label: 'Lead Management', content: <SupportCenter /> },
        analytics: { value: 'analytics', label: 'Analytics', content: <Analytics /> },
        settings: { value: 'settings', icon: <IoSettingsOutline />, label: 'Settings', content: <Settings /> },
        subscription: {
            value: 'subscription',
            icon: <IoSettingsOutline />,
            label: 'Subscription',
            content: <SubscriptionSection />,
        },
    };

    const navItems: HorizontalTabType[] = [
        pages.home,
        showPromosPage && pages.promos,
        showProductsPage && pages.products,
        showReferenceDesignsPage && pages.templates,
        showCaseStudiesPage && pages.caseStudies,
        showEditInfoPage && pages.supportCenter,
        showEditInfoPage && pages.analytics,
        showEditInfoPage && pages.settings,
        showEditInfoPage && pages.subscription,
    ].filter(Boolean) as HorizontalTabType[];

    return {
        navItems,
        isLoading,
    };
};

export { useCompanyProfileNav };
