import { unique } from 'radash';

import { CompanyProfile, Component, ComponentVisibility } from 'models';

import { ComponentListType } from 'services/ComponentService';

import { useProducts } from 'hooks/use-products';
import { useProductsByIds } from 'hooks/use-products-by-ids';

const useProductsByCompany = (company: CompanyProfile, props?: ComponentListType) => {
    const { products, isLoading } = useProducts({
        manufacturer: company.id,
        visibility: ComponentVisibility.PUBLIC,
        ...props,
    });

    const { components: highlightedProducts } = useProductsByIds(company.highlightedComponents ?? []);

    const sortedHighlightedProducts = (company.highlightedComponents
        ?.map((id) => highlightedProducts.find((product) => product.id === id))
        .filter((product) => !!product) ?? []) as Component[];

    const otherProducts = products ?? [];

    return {
        highlightedProducts: sortedHighlightedProducts,
        otherProducts,
        products: unique([...sortedHighlightedProducts, ...otherProducts], ({ id }) => id),
        isLoading: isLoading,
    };
};

export { useProductsByCompany };
