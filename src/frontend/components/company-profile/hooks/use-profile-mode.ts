import { useSnapshot } from 'hooks/use-safe-snapshot';

import { currentProfileState, ProfileMode } from 'components/company-profile/state/current-profile';

const useProfileMode = () => {
    const { mode } = useSnapshot(currentProfileState);

    return {
        mode,
        setMode: (mode: ProfileMode) => {
            currentProfileState.mode = mode;
        },
    };
};

export { useProfileMode };
