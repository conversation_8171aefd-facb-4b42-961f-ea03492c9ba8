import { CompanyProfile, PermissionCompany } from 'models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useProfileMode } from 'components/company-profile/hooks/use-profile-mode';
import { ProfileMode } from 'components/company-profile/state/current-profile';
import { useCurrentUser } from 'hooks/use-current-user';

const useProfilePermission = (
    permission: PermissionCompany,
    bypassMode?: boolean,
    fallbackCompany?: CompanyProfile,
) => {
    const profile = useCurrentProfile();
    const user = useCurrentUser();
    const { mode } = useProfileMode();

    if (!bypassMode && mode === ProfileMode.VIEW) {
        return false;
    }

    return user?.developer
        ? true
        : (profile?.permissions?.includes(permission) ?? fallbackCompany?.permissions?.includes(permission));
};

export { useProfilePermission };
