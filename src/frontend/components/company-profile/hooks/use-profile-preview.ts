import { CompanyProfile } from 'models';

import { useSearchParams } from 'next/navigation';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { CryptoHelpers } from 'helpers/CryptoHelpers';

const useProfilePreview = (company?: CompanyProfile) => {
    const currentCompanyProfile = useCurrentProfile();
    const companyToCheck = company ?? currentCompanyProfile;

    const search = useSearchParams();
    const previewSecret = search?.get('preview') || '';

    return {
        previewSecret: previewSecret,
        previewSecretIsValid: !!(
            companyToCheck &&
            previewSecret &&
            CryptoHelpers.unsafeSimpleHash(companyToCheck.slug) === previewSecret
        ),
    };
};

export { useProfilePreview };
