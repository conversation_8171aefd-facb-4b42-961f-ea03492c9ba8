import { FC } from 'react';

import { flat } from 'radash';

import { CompanyProfile, CompanyServiceOptions, PermissionCompany, StubPlaceholder } from 'models';

import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { SectionProps } from 'components/section/Section';
import { Stubs } from 'components/company-profile/sections/Stubs';

const Highlights: FC<
    {
        company: CompanyProfile;
        highlights: CompanyProfile['highlights'];
    } & SectionProps
> = ({ company, highlights, ...props }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const { shownServices } = useShowCompanyInfo(company);

    const addedHighlights = flat(company.highlights.map(({ tags }) => tags ?? []));

    let placeholders: StubPlaceholder[] = [];

    if (canEdit) {
        placeholders.push({
            name: 'Highlight',
            description: 'Want to highlight a product or service?',
        });

        placeholders.push(
            ...shownServices.map((service) => ({
                name: `About service: ${service}`,
                tag: service,
                description: 'This is a great place to add more information about your service.',
                placeholder: `eg. ${CompanyServiceOptions.find(({ label }) => label === service)?.description}`,
            })),
        );

        if (company.applicationTags) {
            placeholders.push(
                ...company.applicationTags.map((tag) => ({
                    name: `About application: ${tag}`,
                    tag,
                    description: 'This is a great place to add more information about your application.',
                })),
            );
        }
    }

    placeholders = placeholders.filter(({ tag, name }) => !addedHighlights.includes(tag ?? name));

    return (
        <Stubs
            hideTitle
            isCarousel
            stubs={highlights}
            company={company}
            field="highlights"
            placeholders={placeholders}
            {...props}
        />
    );
};

export { Highlights };
