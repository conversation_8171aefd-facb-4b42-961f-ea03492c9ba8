import { FC } from 'react';

import Link from 'next/link';

import { Box, UnstyledButton } from '@mantine/core';

import { CompanyProfile, CompanyService, PermissionCompany } from 'models';

import { useReferenceDesigns } from 'components/company-profile/hooks/use-reference-designs';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { Section, SectionProps } from 'components/section/Section';
import { GridSection } from 'components/section/GridSection';
import { SearchBar } from 'components/design-library/search-bar/SearchBar';
import { DesignLibraryTeaser } from 'components/design-library-teaser/DesignLibraryTeaser';
import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';
import { EditReferenceDesigns } from 'components/company-profile/components/EditReferenceDesigns';
import { CarouselSection } from 'components/section/CarouselSection';
import { DashboardEmptyReferenceDesigns } from 'components/dashboard/components/Dashboard.EmptyReferenceDesigns';

const ReferenceDesigns: FC<
    {
        company: CompanyProfile;
        nbCols?: number;
        isCarousel?: boolean;
    } & SectionProps
> = ({ company, nbCols = 4, isCarousel, ...props }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const { referenceDesigns } = useReferenceDesigns(company, canEdit);

    let content = null;

    if (referenceDesigns.length) {
        content = (
            <GridSection nbCols={nbCols}>
                {referenceDesigns.map((project) => (
                    <DesignLibraryTeaser key={project.id} project={project} showPublishedStatus={canEdit} />
                ))}
            </GridSection>
        );

        if (isCarousel) {
            content = (
                <CarouselSection nbCols={nbCols}>
                    {referenceDesigns.map((project) => (
                        <DesignLibraryTeaser key={project.id} project={project} showPublishedStatus={canEdit} />
                    ))}
                </CarouselSection>
            );
        }
    }

    return (
        <Section
            title="Reference Designs"
            titleSection={
                canEdit && (
                    <>
                        <ReferenceDesignButton size="compact-xs" variant="transparent" companyId={company.id} />
                        <EditReferenceDesigns company={company} />
                    </>
                )
            }
            {...props}
        >
            {/* TODO: remove after RE+ demo */}
            {company.services.includes(CompanyService.REPLUS) && (
                <UnstyledButton component={Link} href="/designs">
                    <Box style={{ pointerEvents: 'none' }}>
                        <SearchBar />
                    </Box>
                </UnstyledButton>
            )}

            {content}

            {referenceDesigns.length === 0 && <DashboardEmptyReferenceDesigns />}
        </Section>
    );
};

export { ReferenceDesigns };
