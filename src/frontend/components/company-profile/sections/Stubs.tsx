import { FC, useState } from 'react';

import { Button, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { modals } from '@mantine/modals';
import { IoAddSharp } from 'react-icons/io5';

import { CompanyProfile, CompanyProfileStubFields, PermissionCompany, Stub, StubWithLocalFiles } from 'models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { Section, SectionProps } from 'components/section/Section';
import { StubSection, StubSectionProps } from 'components/company-profile/components/StubSection';
import { StubModal } from 'components/company-profile/components/StubModal';
import { ExampleProfileLink } from 'components/example-profile-link/ExampleProfileLink';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';

export const stubConfig = {
    promos: {
        title: 'Announcements',
        editTitle: 'Edit announcement',
        addTitle: 'Add new announcement',
        addSubmitLabel: 'Create announcement',
        empty: (
            <EmptyMessage>
                No announcements <br />
                Need some inspiration? <br />
                <ExampleProfileLink label="Explore an example profile with announcements" hash="promos" />
            </EmptyMessage>
        ),
        titleIcon: undefined,
    },
    caseStudies: {
        title: 'Case Studies',
        editTitle: 'Edit case study',
        addTitle: 'Add new case study',
        addSubmitLabel: 'Create case study',
        empty: (
            <EmptyMessage>
                No case studies <br />
                Need some inspiration? <br />
                <ExampleProfileLink label="Explore an example profile with case studies" hash="caseStudies" />
            </EmptyMessage>
        ),
        titleIcon: undefined,
    },
    highlights: {
        title: 'Highlights',
        editTitle: 'Edit highlight',
        addTitle: 'Add new highlight',
        addSubmitLabel: 'Create highlight',
        empty: (
            <EmptyMessage>
                No highlights <br />
                Need some inspiration? <br />
                <ExampleProfileLink label="Explore an example profile with highlights" hash="highlights" />
            </EmptyMessage>
        ),
        titleIcon: undefined,
    },
};

const Stubs: FC<
    StubSectionProps & {
        hideTitle?: boolean;
    }
> = ({ company, field, stubs, placeholders, hideTitle, ...props }) => {
    const copy = stubConfig[field];

    const [editStub, setEditStub] = useState<Stub | null>(null);

    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const openEditModal = (id: string) => {
        const stub = stubs.find((needle) => needle.id === id);

        if (stub) {
            setEditStub(stub);
        }
    };

    const openDeleteModal = (id: string) => {
        const stub = stubs.find((needle) => needle.id === id);

        if (stub) {
            modals.openConfirmModal({
                centered: true,
                title: `Are you sure you want to delete ${stub.name}?`,
                labels: { confirm: 'Yes, delete', cancel: "No, don't delete it" },
                confirmProps: { variant: 'light', color: 'red' },
                closeOnConfirm: false,
                onConfirm: () => handleDelete(id),
            });
        }
    };

    const handleDelete = async (id: string) => {
        await CompanyProfileService.deleteStub({
            companyId: company.id,
            stubId: id,
            field,
        });

        modals.closeAll();
    };

    const sectionProps: SectionProps = {
        ...props,
    };

    if (!hideTitle) {
        sectionProps.title = copy.title;
        sectionProps.titleIcon = copy.titleIcon;
        sectionProps.titleSection = canEdit && <AddStub company={company} field={field} />;
    }

    return (
        <>
            {stubs.length || placeholders?.length ? (
                <StubSection
                    nbCols={4}
                    stubs={stubs}
                    handleEdit={canEdit ? openEditModal : undefined}
                    handleDelete={canEdit ? openDeleteModal : undefined}
                    company={company}
                    field={field}
                    placeholders={placeholders}
                    {...sectionProps}
                />
            ) : (
                <Section {...sectionProps}>
                    <Text c="dimmed">{copy.empty}</Text>
                </Section>
            )}

            {editStub && (
                <EditStub company={company} handleClose={() => setEditStub(null)} stub={editStub} field={field} />
            )}
        </>
    );
};

const AddStub = ({ company, field }: { company: CompanyProfile; field: CompanyProfileStubFields }) => {
    const copy = stubConfig[field];

    const [opened, handlers] = useDisclosure();

    const onCreate = async (data: StubWithLocalFiles) => {
        await CompanyProfileService.addStub({
            companyId: company.id,
            field,
            data,
        });

        handlers.close();
    };

    return (
        <>
            <Button leftSection={<IoAddSharp />} variant="transparent" size="compact-xs" onClick={handlers.open}>
                {copy.addTitle}
            </Button>

            {opened && (
                <StubModal
                    company={company}
                    opened={opened}
                    onClose={handlers.close}
                    title={copy.addTitle}
                    submitLabel={copy.addSubmitLabel}
                    onSumbit={onCreate}
                />
            )}
        </>
    );
};

const EditStub = ({
    company,
    handleClose,
    stub,
    field,
}: {
    company: CompanyProfile;
    handleClose: () => void;
    stub: Stub;
    field: CompanyProfileStubFields;
}) => {
    const copy = stubConfig[field];

    const onEdit = async (data: StubWithLocalFiles) => {
        await CompanyProfileService.editStub({
            companyId: company.id,
            stubId: data.id!,
            field,
            data,
        });

        handleClose();
    };

    return (
        <StubModal
            opened
            company={company}
            onClose={handleClose}
            title={copy.editTitle}
            submitLabel="Save"
            onSumbit={onEdit}
            defaultStub={stub}
        />
    );
};

export { Stubs };
