import { FC } from 'react';

import Link from 'next/link';

import { But<PERSON>, Loader } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany } from 'models';

import { Section, SectionProps } from 'components/section/Section';
import { GridSection } from 'components/section/GridSection';
import { Product } from 'components/company-profile/components/Product';
import { EditProducts } from 'components/company-profile/components/EditProducts';

import { useProductsByCompany } from 'components/company-profile/hooks/use-products-by-company';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { CarouselSection } from 'components/section/CarouselSection';
import { DashboardEmptyProducts } from 'components/dashboard/components/Dashboard.EmptyProducts';

const Products: FC<{ company: CompanyProfile; nbCols?: number; isCarousel?: boolean } & SectionProps> = ({
    company,
    nbCols = 4,
    isCarousel,
    ...props
}) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const { products, isLoading } = useProductsByCompany(company, {
        showPrivate: canEdit,
    });

    const createRoute = ComponentHelpers.urls.create({ company });

    return (
        <Section
            title="Products"
            titleSection={
                canEdit && (
                    <>
                        {createRoute && (
                            <Button
                                size="compact-xs"
                                variant="transparent"
                                component={Link}
                                href={createRoute}
                                leftSection={<IoAddSharp size={12} />}
                            >
                                Add product
                            </Button>
                        )}
                        <EditProducts company={company} />
                    </>
                )
            }
            {...props}
        >
            {products.length ? (
                isCarousel ? (
                    <CarouselSection nbCols={nbCols}>
                        {products?.filter(Boolean).map((component) => (
                            <Product
                                key={component.id}
                                component={component}
                                showReferenceDesignButton={canEdit}
                                company={company}
                            />
                        ))}
                    </CarouselSection>
                ) : (
                    <GridSection nbCols={nbCols}>
                        {products?.filter(Boolean).map((component) => (
                            <Product
                                key={component.id}
                                component={component}
                                showReferenceDesignButton={canEdit}
                                company={company}
                            />
                        ))}
                    </GridSection>
                )
            ) : isLoading ? (
                <Loader size="xs" color="gray.5" />
            ) : (
                <DashboardEmptyProducts company={company} />
            )}
        </Section>
    );
};

export { Products };
