import { FC, useState } from 'react';

import { Avatar, Button, Group, Stack, Title } from '@mantine/core';
import { modals } from '@mantine/modals';
import { IoAdd, IoEyeOffOutline } from 'react-icons/io5';

import { CompanyPartner, CompanyProfile, PermissionCompany } from 'models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { usePartnerHandlers } from 'components/company-profile/hooks/use-partner-handlers';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { Section } from 'components/section/Section';
import { GridSection } from 'components/section/GridSection';
import { PartnerTeaser } from 'components/company-profile/components/partners/PartnerTeaser';
import { EditPartnerModal } from 'components/company-profile/components/partners/EditPartnerModal';
import { CarouselSection } from 'components/section/CarouselSection';

const NB_COLS = 5;

const Partners: FC<{
    id?: string;
    company: CompanyProfile;
    partners: CompanyProfile['partners'];
}> = ({ id, company, partners }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const [editPartner, setEditPartner] = useState<CompanyPartner | null>(null);
    const [deletePartner, setDeletePartner] = useState<string | null>(null);

    const openDeleteModal = (partner: CompanyPartner) => {
        modals.openConfirmModal({
            centered: true,
            title: `Are you sure you want to remove partner?`,
            labels: { confirm: 'Yes, remove', cancel: "No, don't remove it" },
            confirmProps: { variant: 'light', color: 'red' },
            closeOnConfirm: false,
            onConfirm: () => handleDelete(partner),
        });
    };

    const handleDelete = async (partner: CompanyPartner) => {
        if (!partner.id) return;

        modals.closeAll();

        setDeletePartner(partner.id);

        await CompanyProfileService.update(company.id, {
            partners: company.partners.filter((item) => item.id !== partner.id),
        });

        LocalNotificationService.showSuccess({
            message: 'Partner deleted',
        });

        setDeletePartner(null);
    };

    const children = partners.map((partner, index) => (
        <PartnerTeaser
            key={index}
            partner={partner}
            handleEdit={canEdit ? setEditPartner : undefined}
            handleDelete={canEdit ? openDeleteModal : undefined}
            isDeleting={deletePartner === partner.id}
        />
    ));

    if (canEdit) {
        //add at the beginning
        children.unshift(
            <PartnerTeaser.Placeholder
                title="Add a partner"
                description="Highlight a manufacturer or service provider you work with"
                onClick={() => CompanyProfileHelpers.openChoosePartnerModal(company)}
            />,
        );
    }

    return (
        <Section
            id={id}
            title="Our Partners"
            titleSection={
                canEdit && (
                    <Button
                        leftSection={<IoAdd />}
                        size="compact-xs"
                        variant="transparent"
                        onClick={() => CompanyProfileHelpers.openChoosePartnerModal(company)}
                    >
                        Add partner
                    </Button>
                )
            }
        >
            <GridSection nbCols={NB_COLS}>{children}</GridSection>

            <PartnersCrossReferences company={company} />

            <EditPartnerModal
                company={company}
                partner={editPartner}
                opened={!!editPartner}
                handleClose={() => setEditPartner(null)}
            />
        </Section>
    );
};

const PartnersCrossReferences = ({ company }: { company: CompanyProfile }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);
    const references = company.partnersCrossReference ?? [];

    const { adding, handleAdd } = usePartnerHandlers({ company });

    if (!canEdit || !references.length) {
        return null;
    }

    return (
        <Stack gap="xs" mt="sm">
            <Title order={3}>
                <Group gap={8}>
                    <Avatar size="sm">
                        <IoEyeOffOutline />
                    </Avatar>
                    These companies added you as a partner
                </Group>
            </Title>

            <CarouselSection nbCols={NB_COLS}>
                {references.map((partner) => (
                    <PartnerTeaser
                        key={partner}
                        withDashedBorder
                        partner={{ company: partner }}
                        extraButtons={
                            <Button
                                size="xs"
                                onClick={() => handleAdd(partner)}
                                loading={adding === partner}
                                disabled={company.partners?.some(({ company: id }) => id === partner)}
                            >
                                Also add as partner
                            </Button>
                        }
                    />
                ))}
            </CarouselSection>
        </Stack>
    );
};

export { Partners };
