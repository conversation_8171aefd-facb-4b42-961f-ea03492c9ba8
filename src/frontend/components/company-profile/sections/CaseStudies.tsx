import { FC } from 'react';

import { AIEmbeddingType, CompanyProfile, PermissionCompany } from 'models';

import { Button } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { ExampleProfileLink } from 'components/example-profile-link/ExampleProfileLink';
import { ArticleTeaser } from 'components/teasers/ArticleTeaser';
import { ArticleModal } from 'components/company-profile/components/ArticleModal';
import { TeaserSection } from 'components/company-profile/components/TeaserSection';

import { ArticleService } from 'services/ArticleService';

import { useDisclosure } from '@mantine/hooks';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { useArticles } from 'hooks/use-articles';
import { useActiveArticle } from 'components/company-profile/hooks/use-active-article';

const CaseStudies: FC<{
    company: CompanyProfile;
    isCarousel?: boolean;
}> = ({ company, isCarousel = false }) => {
    const { articles, isLoading } = useArticles(company.id, AIEmbeddingType.CASE_STUDY);
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const [addModalOpened, addModalHandlers] = useDisclosure();

    const { setActiveArticle } = useActiveArticle();

    const openView = (id: string) => {
        setActiveArticle(id);
    };

    const visibleArticles = canEdit ? articles : articles.filter((article) => article.teaser.image);

    return (
        <>
            <TeaserSection
                title="Case Studies"
                titleSection={
                    canEdit && (
                        <Button
                            leftSection={<IoAddSharp />}
                            variant="transparent"
                            size="compact-xs"
                            onClick={addModalHandlers.open}
                        >
                            Add new case study
                        </Button>
                    )
                }
                nbCols={4}
                isCarousel={isCarousel}
                isEmpty={!isLoading && visibleArticles.length === 0}
                emptyMessage={
                    <EmptyMessage>
                        No case studies <br />
                        Need some inspiration? <br />
                        <ExampleProfileLink label="Explore an example profile with case studies" hash="caseStudies" />
                    </EmptyMessage>
                }
            >
                {visibleArticles.map((article) => (
                    <ArticleTeaser article={article} key={article.id} handleOpen={openView} canEdit={canEdit} />
                ))}
            </TeaserSection>
            {addModalOpened && (
                <ArticleModal
                    company={company}
                    onSubmit={async (values) => {
                        await ArticleService.create({
                            ...values,
                            type: AIEmbeddingType.CASE_STUDY,
                            company: company.id,
                        });
                        await ArticleService.mutate.list(company.id, AIEmbeddingType.CASE_STUDY);

                        addModalHandlers.close();
                    }}
                    onClose={addModalHandlers.close}
                    copy={{
                        title: 'Create case study',
                        submit: 'Create case study',
                    }}
                />
            )}
        </>
    );
};

export { CaseStudies };
