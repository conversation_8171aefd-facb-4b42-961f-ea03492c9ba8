import React, { useState } from 'react';

import { Button, Text } from '@mantine/core';
import { modals } from '@mantine/modals';
import { IoAddSharp } from 'react-icons/io5';

import { CompanyProfile, PermissionCompany } from 'models';

import { Section, SectionProps } from 'components/section/Section';
import { CarouselSection } from 'components/section/CarouselSection';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { getId } from 'helpers/getId';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { ContactPerson } from 'components/company-profile/components/ContactPerson';
import { ContactPersonModal } from 'components/company-profile/components/ContactPersonModal';
import { ProfileModal, useProfileContext } from 'components/company-profile/hooks/use-profile-context';

type ContactPersonType = CompanyProfile['contactPeople'][0];

const ContactPeople = ({
    nbCols = 5,
    contacts,
    company,
    titleSection,
    ...props
}: {
    nbCols?: number;
    contacts: CompanyProfile['contactPeople'];
    company: CompanyProfile;
} & SectionProps) => {
    const [editContact, setEditContact] = useState<ContactPersonType | null>(null);

    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    if (!contacts.length && !canEdit) {
        return null;
    }

    const openEditModal = (id: string) => {
        const contact = contacts.find((needle) => needle.id === id);

        if (contact) {
            setEditContact(contact);
        }
    };

    const openDeleteModal = (id: string) => {
        const contact = contacts.find((needle) => needle.id === id);

        if (contact) {
            modals.openConfirmModal({
                centered: true,
                title: `Are you sure you want to delete ${contact.name}?`,
                labels: { confirm: 'Yes, delete', cancel: "No, don't delete it" },
                confirmProps: { variant: 'light', color: 'red' },
                closeOnConfirm: false,
                onConfirm: () => handleDelete(id),
            });
        }
    };

    const handleDelete = async (id: string) => {
        await CompanyProfileService.update(company.id, {
            contactPeople: company.contactPeople.filter((item) => item.id !== id),
        });

        modals.closeAll();
    };

    return (
        <Section
            title="Our Team"
            titleSection={
                canEdit ? (
                    <>
                        <AddContact company={company} />
                        {titleSection}
                    </>
                ) : (
                    titleSection
                )
            }
            {...props}
        >
            {contacts.length === 0 && <Text c="dimmed">No contact people added yet.</Text>}
            <CarouselSection nbCols={nbCols}>
                {contacts.map((item, index) => (
                    <ContactPerson
                        key={index}
                        {...item}
                        company={company}
                        handleEdit={canEdit ? openEditModal : undefined}
                        handleDelete={canEdit ? openDeleteModal : undefined}
                    />
                ))}
            </CarouselSection>

            {editContact && (
                <EditContact company={company} handleClose={() => setEditContact(null)} contact={editContact} />
            )}
        </Section>
    );
};

const AddContact = ({ company }: { company: CompanyProfile }) => {
    const { openedModal, openModal, closeModal } = useProfileContext();

    const onCreate = async (data: ContactPersonType) => {
        const cleanContactPeople =
            company.contactPeople?.map(({ image, ...item }) => ({ ...item, image: getId(image) })) || [];

        await CompanyProfileService.update(company.id, { contactPeople: [data, ...cleanContactPeople] });

        closeModal();
    };

    const opened = openedModal === ProfileModal.ADD_CONTACT;

    return (
        <>
            <Button
                leftSection={<IoAddSharp />}
                variant="transparent"
                size="compact-xs"
                onClick={() => openModal(ProfileModal.ADD_CONTACT)}
            >
                Add contact person
            </Button>

            {opened && (
                <ContactPersonModal
                    title="Add contact"
                    company={company}
                    opened={opened}
                    onClose={closeModal}
                    onSumbit={onCreate}
                />
            )}
        </>
    );
};

const EditContact = ({
    company,
    handleClose,
    contact,
}: {
    company: CompanyProfile;
    handleClose: () => void;
    contact: ContactPersonType;
}) => {
    const onEdit = async (data: ContactPersonType) => {
        const cleanContactPeople =
            company.contactPeople?.map(({ image, ...item }) => ({ ...item, image: getId(image) })) || [];

        await CompanyProfileService.update(company.id, {
            contactPeople: cleanContactPeople.map((item) => (item.id === data.id ? data : item)),
        });

        handleClose();
    };

    return (
        <ContactPersonModal
            opened
            title="Edit contact"
            submitLabel="Save contact"
            company={company}
            onClose={handleClose}
            onSumbit={onEdit}
            defaultContact={contact}
        />
    );
};

export { ContactPeople };
