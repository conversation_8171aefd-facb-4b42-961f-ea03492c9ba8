import { FC, useState } from 'react';

import { CompanyProfile, PermissionCompany } from 'models';

import { Button, Text } from '@mantine/core';
import { modals } from '@mantine/modals';

import { IoAddSharp } from 'react-icons/io5';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { ProfileModal, useProfileContext } from 'components/company-profile/hooks/use-profile-context';

import { getId } from 'helpers/getId';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { Section } from 'components/section/Section';
import { GridSection } from 'components/section/GridSection';
import { LocationModal } from 'components/company-profile/components/LocationModal';
import { LocationTeaser } from 'components/company-profile/components/LocationTeaser';

type Location = CompanyProfile['locations'][0];

const Contact: FC<{ locations: CompanyProfile['locations']; company: CompanyProfile }> = ({ locations, company }) => {
    const [editLocation, setEditLocation] = useState<Location | null>(null);

    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    if (!locations.length && !canEdit) {
        return null;
    }

    const openEditModal = (id: string) => {
        const location = locations.find((needle) => needle.id === id);

        if (location) {
            setEditLocation(location);
        }
    };

    const openDeleteModal = (id: string) => {
        const location = locations.find((needle) => needle.id === id);

        if (location) {
            modals.openConfirmModal({
                centered: true,
                title: `Are you sure you want to delete ${location.name}?`,
                labels: { confirm: 'Yes, delete', cancel: "No, don't delete it" },
                confirmProps: { variant: 'light', color: 'red' },
                closeOnConfirm: false,
                onConfirm: () => handleDelete(id),
            });
        }
    };

    const handleDelete = async (id: string) => {
        await CompanyProfileService.update(company.id, {
            locations: company.locations.filter((item) => item.id !== id),
        });

        modals.closeAll();
    };

    return (
        <Section title="Office Locations" titleSection={canEdit && <AddLocation company={company} />}>
            {locations.length === 0 && <Text c="dimmed">No contact information added yet.</Text>}
            <GridSection nbCols={4}>
                {locations.map((location) => (
                    <LocationTeaser
                        key={location.id}
                        {...location}
                        handleEdit={canEdit ? openEditModal : undefined}
                        handleDelete={canEdit ? openDeleteModal : undefined}
                        company={company}
                    />
                ))}
            </GridSection>

            {editLocation && (
                <EditLocation company={company} handleClose={() => setEditLocation(null)} location={editLocation} />
            )}
        </Section>
    );
};

const AddLocation = ({ company }: { company: CompanyProfile }) => {
    const { openedModal, openModal, closeModal } = useProfileContext();

    const onCreate = async (data: Location) => {
        const cleanLocations = company.locations?.map(({ image, ...item }) => ({ ...item, image: getId(image) })) || [];

        await CompanyProfileService.update(company.id, { locations: [data, ...cleanLocations] });

        closeModal();
    };

    const opened = openedModal === ProfileModal.ADD_LOCATION;

    return (
        <>
            <Button
                leftSection={<IoAddSharp />}
                variant="transparent"
                size="compact-xs"
                onClick={() => openModal(ProfileModal.ADD_LOCATION)}
            >
                Add location
            </Button>

            {opened && (
                <LocationModal
                    title="Add location"
                    company={company}
                    opened={opened}
                    onClose={closeModal}
                    onSumbit={onCreate}
                />
            )}
        </>
    );
};

const EditLocation = ({
    company,
    handleClose,
    location,
}: {
    company: CompanyProfile;
    handleClose: () => void;
    location: Location;
}) => {
    const onEdit = async (data: Location) => {
        const cleanLocations = company.locations?.map(({ image, ...item }) => ({ ...item, image: getId(image) })) || [];

        await CompanyProfileService.update(company.id, {
            locations: cleanLocations.map((item) => (item.id === data.id ? data : item)),
        });

        handleClose();
    };

    return (
        <LocationModal
            opened
            title="Edit location"
            submitLabel="Save location"
            company={company}
            onClose={handleClose}
            onSumbit={onEdit}
            defaultLocation={location}
        />
    );
};

export { Contact };
