import { FC } from 'react';

import { IoMegaphoneOutline } from 'react-icons/io5';

import { CompanyProfile } from 'models';

import { StubSection } from 'components/company-profile/components/StubSection';
import { SectionProps } from 'components/section/Section';

const Promos: FC<
    {
        promos: CompanyProfile['promos'];
        nbCols?: number;
        company: CompanyProfile;
    } & SectionProps
> = ({ promos, title = 'Announcements', nbCols = 4, company, ...props }) => {
    return (
        <StubSection
            isCarousel
            title={title}
            titleIcon={<IoMegaphoneOutline />}
            stubs={promos}
            nbCols={nbCols}
            emptyMessage="No announcements yet."
            company={company}
            field="promos"
            {...props}
        />
    );
};

export { Promos };
