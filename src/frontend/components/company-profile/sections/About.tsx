import { FC } from 'react';

import { Box, Button, Modal, Stack, Text } from '@mantine/core';
import { BsPencil } from 'react-icons/bs';

import { CompanyProfile, PermissionCompany } from 'models';

import { useRTE } from 'hooks/use-rte';
import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';
import { ProfileModal, useProfileContext } from 'components/company-profile/hooks/use-profile-context';

import { Section } from 'components/section/Section';
import { FormSubmit } from 'components/forms/FormSubmit';
import { RTEContent } from 'components/rte-content/RTEContent';
import { CompanyForm } from 'components/company-form/CompanyForm';

const About: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { showAbout } = useShowCompanyInfo(company);

    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const { openedModal, openModal, closeModal } = useProfileContext();

    const { editor, RTEField } = useRTE(company.about);

    if (!showAbout) {
        return null;
    }

    const editOpened = openedModal === ProfileModal.EDIT_ABOUT;

    return (
        <>
            <Section
                title="About"
                titleSection={
                    canEdit && (
                        <Button
                            variant="transparent"
                            size="compact-xs"
                            leftSection={<BsPencil />}
                            onClick={() => openModal(ProfileModal.EDIT_ABOUT)}
                        >
                            Edit
                        </Button>
                    )
                }
            >
                <Box maw={800}>
                    <RTEContent content={company.about} emptyMessage={<Text c="dimmed">No about added yet.</Text>} />
                </Box>
            </Section>

            {editOpened && (
                <Modal opened onClose={closeModal} title="Edit About" size="xl">
                    <CompanyForm companyId={company.id} onSubmitSuccess={closeModal} aboutEditor={editor}>
                        <Stack>
                            {editor && RTEField && <RTEField editor={editor} label="About the company" />}

                            <FormSubmit style={{ alignSelf: 'start' }}>Save changes</FormSubmit>
                        </Stack>
                    </CompanyForm>
                </Modal>
            )}
        </>
    );
};

export { About };
