import React, { FC, useEffect, useState } from 'react';

import { Box } from '@mantine/core';

import { Component, ComponentType } from 'models';

import { ModalService } from 'services/ModalService';
import { useCurrentUser } from 'hooks/use-current-user';
import { useSignupSteps } from 'components/component-signup/hooks/use-signup-steps';

import { sidebarNavState } from 'state/sidebar-nav';

import { Page } from 'components/page';

import { Steps } from 'components/component-signup/components/Steps';

import { CurrentComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import cx from './ComponentSignup.module.scss';

export type ComponentSignupProps = {
    className?: string;
    active: number;
    nextStep: () => void;
    prevStep: () => void;
    progress?: number;
};

const ComponentSignup: FC<{ startStep?: number; componentType?: ComponentType }> = ({
    startStep = 0,
    componentType,
}) => {
    const user = useCurrentUser();

    const signupSteps = useSignupSteps(componentType);

    const [active, setActive] = useState(startStep);
    const nextStep = () => setActive((current) => (current < signupSteps.length ? current + 1 : current));
    const prevStep = () => setActive((current) => (current > 0 ? current - 1 : current));

    const progress = (active / signupSteps.length) * 100;

    useEffect(() => {
        if (user) return;

        ModalService.openLoginModal({
            message: 'You need to log in to add products',
            closeOnClickOutside: false,
            closeOnEscape: false,
        });

        return () => ModalService.closeLoginModal();
    }, [user]);

    // close sidebar
    useEffect(() => {
        sidebarNavState.isOpen = false;

        return () => {
            sidebarNavState.isOpen = true;
        };
    }, []);

    return (
        <>
            <Page
                hideFooter
                title="Sign up product"
                breadcrumbs={{
                    isSticky: false,
                    showToggle: true,
                }}
            >
                <Page.FullScreenContent>
                    <Box className={cx.root}>
                        <Steps active={active} nextStep={nextStep} prevStep={prevStep} progress={progress} />
                    </Box>
                </Page.FullScreenContent>
            </Page>
        </>
    );
};

const WrappedComponentSignup: FC<{ component?: Component }> = ({ component }) => {
    const [currentComponent, setCurrentComponent] = useState<Component | null>(component ?? null);

    return (
        <CurrentComponentContext.Provider
            value={{
                component: currentComponent,
                setComponent: setCurrentComponent,
            }}
        >
            <ComponentSignup startStep={component ? 1 : 0} componentType={component?.type} />
        </CurrentComponentContext.Provider>
    );
};

export { WrappedComponentSignup as ComponentSignup };
