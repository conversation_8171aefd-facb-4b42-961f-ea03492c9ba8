.root {
    display: grid;

    [data-content] {
        min-height: calc(100dvh - var(--header-height));
    }

    [data-header] {
        position: sticky;
        top: 0;
        z-index: 99;
    }

    [data-actions] {
        position: sticky;
        bottom: 0;
        z-index: 99;
    }

    .aiSidebar[data-steps] {
        grid-column: span 4;
    }

    [data-steps] {
        :global(.mantine-ScrollArea-viewport) {
            > div {
                height: 100%;
            }
        }
    }
}
