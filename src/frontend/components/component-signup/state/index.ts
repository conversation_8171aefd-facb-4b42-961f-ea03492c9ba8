import { proxy } from 'valtio';

const componentSignupState = proxy<{
    aiOpened: boolean;
    aiOpenedExpanded: boolean;
    selectedResponse: any;
    key: string;
    aiResponses: Map<string, any>;
    fileUrl: string | null;
}>({
    aiOpened: false,
    aiOpenedExpanded: false,
    selectedResponse: undefined,
    key: '',
    aiResponses: new Map<string, any>(),
    fileUrl: '',
});

export { componentSignupState };
