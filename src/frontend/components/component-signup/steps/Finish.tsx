import { FC } from 'react';

import Link from 'next/link';

import { Anchor, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { IoAddSharp, IoDuplicateOutline, IoEyeOutline, IoStarSharp } from 'react-icons/io5';
import { BsPencil } from 'react-icons/bs';

import { CompanySubscription, Component, ComponentVisibility, getCompanySubscriptionData } from 'models';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';
import { useRouterQuery } from 'hooks/use-router-query';

import { Content } from 'components/component-signup/components/Content';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';
import { FinishAction } from 'components/signup-layout/components/FinishAction';
import { SubscriptionUpdateWrapper } from 'components/subscriptions/SubscriptionUpdateWrapper';
import { SubscriptionButton } from 'components/subscriptions/components/SubscriptionButton';
import { Al<PERSON> } from 'components/alert/Alert';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { useCurrentTeam } from 'hooks/use-current-team';
import { useCompanyProfile } from 'hooks/use-company-profile';

const Finish: FC<ComponentSignupProps> = (props) => {
    const { component } = useOptionalComponentContext();
    const team = useCurrentTeam();
    const companySubscription =
        getCompanySubscriptionData(team?.subscriptions)?.subscription ?? CompanySubscription.NONE;

    if (!component || !team) return null;

    if (component.visibility === ComponentVisibility.PUBLIC) {
        const LiveMessage =
            companySubscription === CompanySubscription.PREMIUM ? (
                <Text>
                    Your product, <b>{component.name}</b> is now live on our catalog!
                </Text>
            ) : (
                <Alert
                    title="Upgrade to Premium"
                    icon={<IoStarSharp size={36} />}
                    rightSection={
                        <SubscriptionUpdateWrapper
                            toSubscription={CompanySubscription.PREMIUM}
                            currentSubscription={companySubscription}
                            team={team}
                            redirectUrl={ComponentHelpers.urls.view(component.id)}
                        >
                            <SubscriptionButton buttonLabel="Upgrade" />
                        </SubscriptionUpdateWrapper>
                    }
                    color="cyan"
                >
                    <Text inherit>
                        Thank you for adding your product! To enhance its visibility in our product catalog, consider
                        upgrading to the Premium plan.
                    </Text>
                </Alert>
            );

        return (
            <Content {...props}>
                <Stack maw={600}>
                    <Title order={2}>Thanks for adding your product!</Title>
                    {LiveMessage}

                    <Text>
                        We&apos;re excited to have you on board. If you have any questions, please don&apos;t hesitate
                        to contact us at{' '}
                        <Anchor href="mailto:<EMAIL>"><EMAIL></Anchor>.
                    </Text>

                    <Text fw={600}>What&apos;s next?</Text>

                    <FinishActions component={component} />
                </Stack>
            </Content>
        );
    }

    return (
        <Content {...props}>
            <Stack maw={600}>
                <Title order={2}>Thanks for providing all the information about your product</Title>
                <Text>
                    Your product, <b>{component.name}</b> is not yet listed on our catalog.
                </Text>

                <Text>
                    We&apos;ll be waiting to showcase your product to the world! In the meantime, if you need to make
                    any changes, you can easily{' '}
                    <Anchor component={Link} href={ComponentHelpers.urls.view(component.id)}>
                        edit the product details here
                    </Anchor>
                    .
                </Text>

                <Text>
                    If you have any questions, please don&apos;t hesitate to contact us at{' '}
                    <Anchor href="mailto:<EMAIL>"><EMAIL></Anchor>.
                </Text>

                <Text fw={600}>What&apos;s next?</Text>

                <FinishActions component={component} />
            </Stack>
        </Content>
    );
};

const FinishActions = ({ component }: { component: Component }) => {
    const { manufacturer, distributor } = useRouterQuery<{
        manufacturer?: string;
        distributor?: string;
    }>();

    const { company } = useCompanyProfile(manufacturer || distributor);

    return (
        <SimpleGrid cols={4} spacing={8}>
            <FinishAction
                href={ProjectHelpers.urls.create({
                    isReferenceDesign: true,
                    referenceComponent: component.id,
                    name: `Reference design for ${component.name}`,
                    manufacturer: manufacturer || distributor,
                })}
                isFocus
                icon={<BsPencil />}
            >
                Create a reference design for your product
            </FinishAction>
            <FinishAction href={ComponentHelpers.urls.duplicate(component.id)} icon={<IoDuplicateOutline />}>
                Add a variant of this product
            </FinishAction>
            <FinishAction href={ComponentHelpers.urls.create({ company })} icon={<IoAddSharp />}>
                Add a new product
            </FinishAction>
            <FinishAction href={ComponentHelpers.urls.view(component.id)} icon={<IoEyeOutline />}>
                View product page
            </FinishAction>
        </SimpleGrid>
    );
};

export { Finish };
