import { FC, useState } from 'react';

import { z } from 'zod';

import { omit, unique } from 'radash';
import { useFormContext, useWatch } from 'react-hook-form';

import { IoAlertCircleOutline } from 'react-icons/io5';
import {
    Button,
    Group,
    InputLabel,
    Modal,
    Radio,
    SimpleGrid,
    Stack,
    Text,
    ThemeIcon,
    Title,
    Tooltip,
} from '@mantine/core';

import {
    Compliance,
    Component,
    ComponentApplication,
    ComponentType,
    ComponentVisibility,
    images as ComponentImagesSchema,
    UserFeatureFlags,
    RegionAvailability,
} from 'models';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { Form } from 'components/forms/Form';
import { FormStatus } from 'components/forms/FormStatus';
import { TextField } from 'components/forms/fields/TextField';
import { NumberField } from 'components/forms/fields/NumberField';
import { ManufacturerField } from 'components/forms/fields/ManufacturerField';
import { DistributorsField } from 'components/forms/fields/DistributorsField';
import { ComponentTypeField } from 'components/forms/fields/ComponentTypeField';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';
import { PriceField } from 'components/forms/fields/PriceField';
import { RegionAvailabilityField } from 'components/component-fields/RegionAvailability';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import { ComponentOverviewHit } from 'components/component-overview';
import useHandleComponentFormSubmit from 'components/component-form/useHandleComponentFormSubmit';

import { ComponentService } from 'services/ComponentService';
import { Content } from 'components/component-signup/components/Content';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';
import { useRouter } from 'next/router';
import { useCurrentUser } from 'hooks/use-current-user';
import { BulkUploadCsv } from '../components/BulkUploadCsv';
import { useCurrentUserFlag } from 'hooks/use-current-user-flag';

import { CompliancesField } from 'components/forms/fields/CompliancesField';
import { CONTENT_WIDTH } from 'components/page/Content';
import { Datasheet, DatasheetMode } from 'components/datasheet';
import { ComponentImagesField } from 'components/component-fields/ComponentImagesField';

const Step1Validator = z.object({
    type: z.string(),
    name: z.string().min(3),
    description: z.string().optional(),
    manufacturer: z.string().optional(),
    distributorsDetails: z
        .array(
            z.object({
                distributor: z.string(),
            }),
        )
        .optional(),
    productIdentifier: z.string(),
    productSeries: z.string().optional(),
    msrp: z.number().optional().nullable(),
    leadTime: z.number().optional().nullable(),
    compliance: z
        .object({
            CE: z.boolean().optional(),
            UL: z.boolean().optional(),
            currentOS: z.boolean().optional(),
            ODCA: z.boolean().optional(),
            emergeAlliance: z.boolean().optional(),
            other: z.boolean().optional(),
            otherInput: z.string().optional(),
        })
        .optional(),
    application: z.nativeEnum(ComponentApplication).array().optional(),
    images: ComponentImagesSchema,
    regionAvailability: RegionAvailability.validator.default([]),
});

const Step1ValidatorStrict = Step1Validator.extend({
    manufacturer: z.string(),
});

const NEW_COMPONENT_TYPES = [
    'battery',
    'breaker',
    'charger',
    'combinerBox',
    'converter',
    'disconnect',
    'fuse',
    'panel',
    'light',
    'rapidShutdownDevice',
    'solar',
    'solution',
    'transformer',
    'powerDistributionUnit',
    'hvac',
    'generator',
    'cable',
    'other',
] as ComponentType[];

const Step1 = ({
    originalComponent,
    closeModal,
    prefillManufacturer,
    prefillDistributor,
}: {
    originalComponent?: Component;
    closeModal: () => void;
    prefillManufacturer?: string;
    prefillDistributor?: string;
}) => {
    const { component } = useOptionalComponentContext();

    const user = useCurrentUser();
    const userHasBulkUpload = useCurrentUserFlag(UserFeatureFlags.BULK_UPLOAD);
    const router = useRouter();

    // developers can add products to any manufacturer
    const enableManufacturer = user?.developer || !prefillManufacturer;
    const enableDistributor = user?.developer || !prefillDistributor;

    const onCreate = async (component: Component) => {
        const route = ComponentHelpers.urls.create({
            component: component.id,
        });

        await router.replace(route, undefined, {
            shallow: false,
        });
    };

    const { handleSubmit, ...formContext } = useFormContext<Component>();
    const onSubmit = useHandleComponentFormSubmit(onCreate, false);

    const application = useWatch({
        name: 'application',
    }) as Component['application'];

    const enableDC = application?.includes(ComponentApplication.DC_MICROGRID);

    return (
        <Stack maw={CONTENT_WIDTH}>
            <Title order={2}>Select the product category</Title>
            <Text>
                Is your product category not listed? Select <strong>Other</strong> to proceed.
            </Text>

            <Tooltip
                label={
                    <>
                        You cannot change the product category after you create the product.
                        <br /> If you need to change the category, please create a new product.
                    </>
                }
                position="bottom"
                disabled={!component?.id || !component?.type}
            >
                <div>
                    <ComponentTypeField
                        name="type"
                        componentTypes={NEW_COMPONENT_TYPES}
                        disabled={!!component?.id && !!component?.type}
                    />
                </div>
            </Tooltip>

            {userHasBulkUpload && <BulkUploadCsv />}

            <Stack w="100%" maw={600} mt="md">
                <TextField name="name" label="Product Name" />

                <MultilineTextField name="description" label="Description" autosize minRows={4} />

                <SimpleGrid cols={2} spacing={8}>
                    <TextField name="productIdentifier" label="Part number" />
                    <TextField name="productSeries" label="Product series" />
                </SimpleGrid>

                <SimpleGrid cols={2} spacing={8}>
                    <PriceField name="msrp" label="Avg. Price" />
                    <NumberField name="leadTime" label="Typ. Lead Time (in days)" allowNegative={false} />
                </SimpleGrid>

                <Text fz={10} c="dimmed" mt={-10} lh={1.2}>
                    We recognize that actual costs vary due to factors like volume, discounts, and customization. The
                    listed price is only an estimate to help users plan their budgets.
                </Text>

                <SimpleGrid cols={2} spacing={8}>
                    <ManufacturerField name="manufacturer" label="Manufacturer" disabled={!enableManufacturer} />
                    <DistributorsField name="distributorsDetails" label="Distributors" disabled={!enableDistributor} />
                </SimpleGrid>

                <RegionAvailabilityField name="regionAvailability" label="Region Availability" />

                <CompliancesField
                    name="compliance"
                    label="Compliances"
                    description="Check all that apply. Multiple options are allowed."
                    showCompliances={[Compliance.CE, Compliance.UL]}
                />

                <DCMicrogridField componentId={component?.id} />

                {enableDC && (
                    <CompliancesField
                        name="compliance"
                        label="We are a member of the following organizations"
                        description="Check all that apply. Multiple options are allowed."
                        showCompliances={[Compliance.CURRENT_OS, Compliance.EMERGE, Compliance.ODCA, Compliance.OTHER]}
                    />
                )}

                <Datasheet initialMode={DatasheetMode.CREATE}>
                    <Stack gap={4}>
                        <InputLabel>Images</InputLabel>
                        <ComponentImagesField columns={{ base: 2, sm: 4 }} editable />
                    </Stack>
                </Datasheet>

                <FormStatus />
            </Stack>

            {originalComponent && (
                <ExistsModal
                    component={originalComponent}
                    close={closeModal}
                    submitForm={handleSubmit((data) => onSubmit(data, formContext as any))}
                />
            )}
        </Stack>
    );
};

const DCMicrogridField = ({ componentId }: { componentId?: string }) => {
    const name = 'application';

    const application = useWatch({
        name,
        defaultValue: [],
    }) as Component['application'];

    const enableDC = application?.includes(ComponentApplication.DC_MICROGRID);

    const { setValue } = useFormContext<Component>();

    return (
        <Radio.Group
            label="Is this product suitable for DC Coupled Microgrid Applications?"
            defaultValue={enableDC ? 'yes' : componentId ? 'no' : undefined}
            onChange={(value) =>
                setValue(
                    name,
                    value === 'yes'
                        ? unique([...application, ComponentApplication.DC_MICROGRID])
                        : application.filter((app) => app !== ComponentApplication.DC_MICROGRID),
                )
            }
        >
            <Group>
                {[
                    {
                        value: 'yes',
                        label: (
                            <Text inherit span className="gradient-cyan-green" fw={600}>
                                Yes, this product is suitable for DC Coupled Microgrid Applications
                            </Text>
                        ),
                    },
                    {
                        value: 'no',
                        label: 'No',
                    },
                ].map((option) => (
                    <Radio value={option.value} label={option.label} key={option.value} required />
                ))}
            </Group>
        </Radio.Group>
    );
};

const ExistsModal = ({
    component,
    close,
    submitForm,
}: {
    component: Component;
    close: () => void;
    submitForm: () => void;
}) => {
    return (
        <Modal size="lg" opened withCloseButton={false} onClose={close}>
            <Stack align="center">
                <Stack align="center" maw={300} mx="auto" my="md">
                    <ThemeIcon radius={99} bg="gray.5" size="xl">
                        <IoAlertCircleOutline size={30} />
                    </ThemeIcon>
                    <Title ta="center" fz={24} fw={700}>
                        We found a similar product in our catalog
                    </Title>
                </Stack>

                <Stack bg="gray.0" p="lg" w="100%" gap="md">
                    <Text ta="center" fz={10} fw={600} c="gray.5" tt="uppercase">
                        Similar product
                    </Text>
                    <ComponentOverviewHit component={omit(component, ['images']) as Component} />
                </Stack>

                <Text>Are you sure you want to proceed adding a new product?</Text>

                <Group gap={8}>
                    <Button
                        onClick={() => {
                            submitForm();
                            close();
                        }}
                    >
                        Yes, add new product
                    </Button>
                    <Button variant="outline" onClick={close}>
                        No, cancel
                    </Button>
                </Group>
            </Stack>
        </Modal>
    );
};

const WrappedStep1: FC<ComponentSignupProps> = ({ nextStep, ...props }) => {
    const router = useRouter();
    const { component, setComponent } = useOptionalComponentContext();

    const prefillManufacturer = router.query.manufacturer as string;
    const prefillDistributor = router.query.distributor as string;

    const [originalComponent, setOriginalComponent] = useState<Component>();

    const onCreate = async (updatedComponent: Component) => {
        setComponent(updatedComponent);

        if (component) {
            nextStep();
            return;
        }

        const route = ComponentHelpers.urls.create({
            component: updatedComponent.id,
        });

        await router.replace(route, undefined, { shallow: false });
    };

    const onSubmit = useHandleComponentFormSubmit(onCreate, false);

    const user = useCurrentUser();
    const isStrictValidator = user?.developer;

    return (
        <Form<Component>
            defaultValues={{
                ...component,
                manufacturer: component?.manufacturer || prefillManufacturer || undefined,
                distributorsDetails: component?.distributorsDetails[0]?.distributor
                    ? component.distributorsDetails
                    : prefillDistributor
                      ? [{ distributor: prefillDistributor }]
                      : [],
                images: ComponentImagesSchema.parse(component?.images),
            }}
            onSubmit={async (values, state) => {
                if (!component) {
                    const existingComponent = await ComponentService.getExisting({
                        type: values.type,
                        productIdentifier: values.productIdentifier,
                        manufacturer: values.manufacturer as string,
                    });

                    if (existingComponent) {
                        setOriginalComponent(existingComponent);
                        return;
                    }
                }

                await onSubmit(
                    {
                        ...values,
                        id: component?.id || values.id,
                        visibility: ComponentVisibility.PRIVATE,
                    },
                    state,
                );
            }}
            zodSchema={isStrictValidator ? Step1ValidatorStrict : Step1Validator}
        >
            <Content nextStep={nextStep} {...props}>
                <Step1
                    originalComponent={originalComponent}
                    closeModal={() => setOriginalComponent(undefined)}
                    prefillManufacturer={prefillManufacturer}
                    prefillDistributor={prefillDistributor}
                />
            </Content>
        </Form>
    );
};

export { WrappedStep1 as Step1, NEW_COMPONENT_TYPES };
