import { FC } from 'react';

import { useSnapshot } from 'valtio';

import { Box, Drawer } from '@mantine/core';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { componentSignupState } from 'components/component-signup/state';

import { ComponentForm } from 'components/component-form';
import { Content } from 'components/component-signup/components/Content';
import { AISidebar } from 'components/component-signup/components/AISidebar';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';
import { GenerateDatasheet } from 'components/generate-datasheet/GenerateDatasheet';

import cx from './StepX.module.scss';

const StepAI: FC<ComponentSignupProps> = ({ nextStep, ...props }) => {
    const { component, setComponent } = useOptionalComponentContext();

    const { aiOpened, aiOpenedExpanded } = useSnapshot(componentSignupState);
    const aiAssistantWidth = aiOpenedExpanded ? '70%' : '40%';

    const closeAiAssistant = () => {
        componentSignupState.aiOpened = false;
    };

    return (
        <Box className={cx.root}>
            <ComponentForm
                initialValues={component ?? undefined}
                onUpdate={(updatedComponent) => {
                    setComponent(updatedComponent);
                    nextStep();
                }}
            >
                <Content nextStep={nextStep} {...props}>
                    <GenerateDatasheet />
                </Content>

                <Drawer
                    keepMounted
                    position="right"
                    withCloseButton={false}
                    opened={aiOpened}
                    onClose={closeAiAssistant}
                    styles={{
                        body: { padding: 0, height: '100%' },
                    }}
                    size={aiAssistantWidth}
                >
                    <AISidebar />
                </Drawer>
            </ComponentForm>
        </Box>
    );
};

export { StepAI };
