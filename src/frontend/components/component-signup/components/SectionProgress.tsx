import { useMemo } from 'react';

import { IoTimeOutline } from 'react-icons/io5';

import { Component, ComponentSection, getFilterFunction, getSectionProgress, getSectionProgressValues } from 'models';

import { ComponentProgressRing, ComponentProgressRingIcon } from 'components/component-progress/ComponentProgressRing';

function SectionProgress({ component, section }: { component?: Component; section: ComponentSection }) {
    const values = getSectionProgressValues(component ?? {}, section);
    const filter = getFilterFunction(section);

    const progress = useMemo(() => getSectionProgress(values, filter), [JSON.stringify(values), filter]);

    if (!component || progress === 0)
        return (
            <ComponentProgressRingIcon color="gray.4">
                <IoTimeOutline />
            </ComponentProgressRingIcon>
        );

    return <ComponentProgressRing progress={progress} />;
}

export { SectionProgress };
