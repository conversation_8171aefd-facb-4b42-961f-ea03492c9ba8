import { FC } from 'react';

import Link from 'next/link';

import { Box, Button, Flex, Group, Text } from '@mantine/core';
import { IoAddSharp, IoDuplicateOutline } from 'react-icons/io5';
import { TbArrowLeft, TbArrowRight } from 'react-icons/tb';

import { Component, ComponentVisibility } from 'models';

import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';

import { FormSubmit } from 'components/forms/FormSubmit';
import { ComponentService } from 'services/ComponentService';

import { PublishButton } from 'components/component-signup/components/PublishButton';
import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import { useAction } from 'hooks/use-action';
import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';
import { useSignupSteps } from 'components/component-signup/hooks/use-signup-steps';
import { useRouterQuery } from 'hooks/use-router-query';
import { useCompanyProfile } from 'hooks/use-company-profile';

import cx from './Actions.module.scss';

const Actions: FC<Pick<ComponentSignupProps, 'active' | 'nextStep' | 'prevStep'>> = ({
    active,
    nextStep,
    prevStep,
}) => {
    const { manufacturer, distributor } = useRouterQuery<{
        manufacturer?: string;
        distributor?: string;
    }>();

    const { company } = useCompanyProfile(manufacturer || distributor);

    const { component, setComponent } = useOptionalComponentContext();

    const signupSteps = useSignupSteps(component?.type);
    const prev = active > 0;
    const next = active < signupSteps.length - 1; // Show publish button on last step

    const [changeVisibility, publishing] = useAction(async (visibility: ComponentVisibility) => {
        if (!component) {
            console.error('Component signup - No component to update visibility');
            return;
        }

        try {
            await ComponentService.update(component.id, {
                visibility,
            } as Component);

            setComponent({ ...component, visibility });

            nextStep();
        } catch (error) {
            console.error('Error changing component visibility', error);
        }
    });

    if (component && active === signupSteps.length)
        return (
            <Flex className={cx.actions} data-actions>
                <Group ml="auto" gap={8}>
                    <ReferenceDesignButton
                        component={component}
                        companyId={manufacturer || distributor}
                        variant="outline"
                    />
                    <Button
                        component={Link}
                        href={ComponentHelpers.urls.duplicate(component.id)}
                        variant="outline"
                        leftSection={<IoDuplicateOutline />}
                    >
                        Add variant of this product
                    </Button>
                    <Button
                        component={Link}
                        href={ComponentHelpers.urls.create({
                            company,
                        })}
                        loading={publishing}
                        leftSection={<IoAddSharp />}
                    >
                        Add a new product
                    </Button>
                </Group>
            </Flex>
        );

    return (
        <Box className={cx.actions} data-actions>
            <Flex justify="space-between" gap={8}>
                {prev ? (
                    <Button onClick={prevStep} variant="outline" leftSection={<TbArrowLeft size={14} />}>
                        Previous Step
                    </Button>
                ) : (
                    <span />
                )}

                {next ? (
                    <FormSubmit rightSection={<TbArrowRight size={14} />} ml="auto">
                        Next Step
                    </FormSubmit>
                ) : (
                    <Group ml="auto" gap={8}>
                        <Button onClick={() => changeVisibility(ComponentVisibility.PRIVATE)} variant="outline">
                            Save for later
                        </Button>
                        <PublishButton
                            handlePublish={() => changeVisibility(ComponentVisibility.PUBLIC)}
                            variant="primary"
                        />
                    </Group>
                )}
            </Flex>

            <Text c="dimmed" ta="right" fz="sm" mt="xs">
                Don&apos;t have all the info at hand? You can always edit this info later
            </Text>
        </Box>
    );
};

export { Actions };
