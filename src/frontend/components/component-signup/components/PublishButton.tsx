import { FC } from 'react';

import { z } from 'zod';
import { Button, ButtonProps, Modal, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoCheckmark } from 'react-icons/io5';

import { Form } from 'components/forms/Form';
import { CheckboxField } from 'components/forms/fields/CheckboxField';
import { FormSubmit } from 'components/forms/FormSubmit';

import { getPublishCopy } from 'helpers/get-publish-copy';

const PublishValidator = z.object({
    verify: z.boolean(),
});

const PublishButton: FC<{ handlePublish: () => void } & ButtonProps> = ({ handlePublish, ...props }) => {
    const [opened, handlers] = useDisclosure();

    const onPublish = () => {
        handlePublish();
        handlers.close();
    };

    return (
        <>
            <Button variant="subtle" onClick={handlers.open} leftSection={<IoCheckmark size={12} />} {...props}>
                Publish product
            </Button>
            <Modal opened={opened} onClose={handlers.close} title="Publish Product?">
                <Form onSubmit={onPublish} zodSchema={PublishValidator}>
                    <Stack maw={600}>
                        <Text>
                            Before publishing, you must confirm that all submitted information is accurate and complete.
                        </Text>

                        <CheckboxField name="verify" required label={<Text>{getPublishCopy()}</Text>} />

                        <FormSubmit>Publish product</FormSubmit>
                    </Stack>
                </Form>
            </Modal>
        </>
    );
};

export { PublishButton };
