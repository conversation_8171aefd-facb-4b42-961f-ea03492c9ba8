import React, { <PERSON> } from 'react';

import { Box, BoxProps, Stack } from '@mantine/core';

import { Header } from 'components/component-signup/components/Header';
import { Actions } from 'components/component-signup/components/Actions';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';
import { WIDE_CONTENT_WIDTH } from 'components/page/Content';

const Content: FC<
    ComponentSignupProps & {
        children: React.ReactNode;
        wrapperProps?: BoxProps;
    }
> = ({ progress, children, wrapperProps, ...props }) => {
    return (
        <Stack data-content gap={0}>
            <Header progress={progress} />
            <Box p="xl" maw={WIDE_CONTENT_WIDTH} {...wrapperProps}>
                {children}
            </Box>
            <Actions {...props} />
        </Stack>
    );
};

export { Content };
