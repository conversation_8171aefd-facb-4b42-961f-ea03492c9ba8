import { useEffect } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useFormContext } from 'react-hook-form';
import { useSnapshot } from 'hooks/use-safe-snapshot';
import { useComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { ActionIcon, Blockquote, Box, Button, Flex, Group, Kbd, Stack, Table, Text, Tooltip } from '@mantine/core';
import { BsChevronBarLeft, BsChevronBarRight } from 'react-icons/bs';
import { IoClose } from 'react-icons/io5';

import { componentSignupState } from 'components/component-signup/state';

import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { SimpleButton } from 'elements/buttons';

import { AIServiceUtils } from 'helpers/AIServiceUtils';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { InternalTrackingService } from 'services/InternalTrackingService';

const replaceReferences = (inputText: string, references: any) => {
    let modifiedText = inputText;

    // Loop through each reference and replace the tag with "Reference N"
    references.forEach((ref: any, index: any) => {
        const referenceTag = ref.tag;
        const referenceReplacement = `<b>Reference ${index + 1}</b>`;
        modifiedText = modifiedText.replaceAll(new RegExp(referenceTag, 'g'), referenceReplacement);
    });

    return modifiedText;
};

const AISidebar = () => {
    const { component } = useComponentContext();
    const { setValue } = useFormContext();

    const { selectedResponse, key, fileUrl, aiOpenedExpanded } = useSnapshot(componentSignupState);

    const closeSidebar = () => {
        componentSignupState.aiOpened = false;
    };

    const toggleExpandSidebar = () => {
        componentSignupState.aiOpenedExpanded = !aiOpenedExpanded;
    };

    const openReferenece = (reference: any) => {
        componentSignupState.fileUrl = '';
        const pdfSettings = `#zoom=120&page=${reference.page_number}&search=Current`;

        componentSignupState.aiOpenedExpanded = true;

        InternalTrackingService.track('product.aiDatasheet.verify.reference', {
            componentId: component.id,
            componentType: component.type,
        });

        setTimeout(() => {
            componentSignupState.fileUrl = reference.document_url + pdfSettings;
        }, 1); // required
    };

    const setAnswer = () => {
        const result = AIServiceUtils.processResponse(selectedResponse.answer, component.type, key);

        if (result.success) {
            Object.entries(result.data ?? {}).forEach(([key, value]) => {
                setValue(key, value, { shouldDirty: true });
            });
        } else {
            LocalNotificationService.showError({
                message: 'Could not parse the answer',
            });
        }

        InternalTrackingService.track('product.aiDatasheet.verify.accept', {
            componentId: component.id,
            componentType: component.type,
        });

        closeSidebar();
    };

    useHotkeys(['esc'], () => {
        closeSidebar();
    });

    useEffect(() => {
        componentSignupState.fileUrl = '';
        componentSignupState.aiOpenedExpanded = false;
    }, [selectedResponse]);

    if (!selectedResponse) {
        return (
            <Box p="xs">
                <EmptyMessage>No response selected</EmptyMessage>
            </Box>
        );
    }

    return (
        <Stack p="xs" gap="md" h="100%">
            <Flex justify="space-between">
                <Tooltip label={aiOpenedExpanded ? 'Collapse sidebar' : 'Expand sidebar'} position="right">
                    <ActionIcon onClick={toggleExpandSidebar} variant="default">
                        {aiOpenedExpanded ? <BsChevronBarRight /> : <BsChevronBarLeft />}
                    </ActionIcon>
                </Tooltip>

                <ActionIcon onClick={closeSidebar} variant="default">
                    <IoClose />
                </ActionIcon>
            </Flex>

            <Table>
                <Table.Tr>
                    <Table.Th>Key</Table.Th>
                    <Table.Td colSpan={2}>
                        <Kbd>{key}</Kbd>
                    </Table.Td>
                </Table.Tr>
                <Table.Tr>
                    <Table.Th>Proposed Answer</Table.Th>
                    <Table.Td>
                        <Kbd>{selectedResponse.answer}</Kbd>
                    </Table.Td>
                    <Table.Td align="right">
                        <Button size="compact-sm" variant="gradient" onClick={setAnswer}>
                            Accept Answer
                        </Button>
                    </Table.Td>
                </Table.Tr>
            </Table>

            <Blockquote p="sm" fz="sm">
                <Stack>
                    <Text
                        inherit
                        dangerouslySetInnerHTML={{
                            __html: replaceReferences(selectedResponse.details, selectedResponse.references),
                        }}
                    />

                    {!!selectedResponse.references.length && (
                        <Group gap={4}>
                            {selectedResponse.references.map((reference: any, index: number) => (
                                <Tooltip
                                    fz="xs"
                                    label={`${reference.document_name} - Page ${reference.page_number}`}
                                    position="top"
                                    key={index}
                                >
                                    <SimpleButton onClick={() => openReferenece(reference)}>
                                        Reference {index + 1}
                                    </SimpleButton>
                                </Tooltip>
                            ))}
                        </Group>
                    )}
                </Stack>
            </Blockquote>

            {fileUrl ? (
                <Box style={{ flexGrow: 1 }}>
                    <iframe width="100%" height="100%" src={fileUrl} style={{ border: 'none' }} />
                </Box>
            ) : (
                !!selectedResponse.references.length && (
                    <EmptyMessage>Click on a reference to view the document here</EmptyMessage>
                )
            )}
        </Stack>
    );
};

export { AISidebar };
