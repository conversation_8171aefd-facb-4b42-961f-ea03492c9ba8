import React, { <PERSON> } from 'react';

import { Box } from '@mantine/core';

import { useSignupSteps } from 'components/component-signup/hooks/use-signup-steps';
import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { Finish } from 'components/component-signup/steps/Finish';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';

const Steps: FC<ComponentSignupProps> = ({ active, className, ...rest }) => {
    const stepProps = {
        active,
        ...rest,
    };
    const { component } = useOptionalComponentContext();
    const signupSteps = useSignupSteps(component?.type);

    const Component = signupSteps[active];

    if (!Component && !component) {
        return null;
    }

    if (!Component) {
        return (
            <Box data-steps className={className}>
                <Finish {...stepProps} />
            </Box>
        );
    }

    return (
        <Box data-steps className={className}>
            <Component {...stepProps} />
        </Box>
    );
};

export { Steps };
