import { FC } from 'react';

import { Badge, Box, Progress, Stack, Title } from '@mantine/core';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useRouterQuery } from 'hooks/use-router-query';
import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';

import cx from './Header.module.scss';

const Header: FC<Pick<ComponentSignupProps, 'progress'>> = ({ progress }) => {
    const { component } = useOptionalComponentContext();

    const { manufacturer: prefillManufacturer, distributor: prefillDistributor } = useRouterQuery<{
        manufacturer?: string;
        distributor?: string;
    }>();

    const { company: manufacturer } = useCompanyProfile(prefillManufacturer);
    const { company: distributor } = useCompanyProfile(prefillDistributor);

    return (
        <Box className={cx.header} data-header>
            <Title fw={700} px="xl" pb="xl">
                <Stack gap={8}>
                    {(manufacturer || distributor) && (
                        <Badge variant="outline" size="sm">
                            {manufacturer?.name || distributor?.name}
                        </Badge>
                    )}
                    Add {component?.name || 'new product'} to the product catalog
                </Stack>
            </Title>
            <Progress value={progress || 0} size={3} color="brand" className={cx.progress} />
        </Box>
    );
};

export { Header };
