import { useState } from 'react';
import { Button, FileButton, JsonInput, Modal, Stack, Text, Timeline } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoAddSharp, IoCloudDownloadOutline, IoCloudUploadOutline } from 'react-icons/io5';

import cx from './BulkUploadCvs.module.scss';

import { ComponentTypeField } from 'components/forms/fields/ComponentTypeField';
import { InlineFiltersManufacturer } from 'components/component-overview/inline-filters/filters/InlineFilters.Manufacturer';
import { ProductCsvService } from 'components/diagram/services/ProductCsvService';
import { NEW_COMPONENT_TYPES } from '../steps/Step1';
import { publicConfig } from '@public-config';

type UploadStatus = 'Submit' | 'Processing' | 'Success' | 'Error';
type ImageUploadStatus = 'Upload Images' | 'Processing' | 'Success' | 'Error';

type BulkUploadState = {
    file: File | null;
    manufacturer: string | null;
    type: string | null;
    bulkUploadId: string | null;
    uploadStatus: UploadStatus;
    imageUploadStatus: ImageUploadStatus;
    error: string | null;
    errorTemplate: string | null;
};

const BulkUploadCsv = () => {
    const [opened, handlers] = useDisclosure();

    return (
        <>
            <Button
                variant="outline"
                size="compact-sm"
                w="26%"
                leftSection={<IoCloudUploadOutline />}
                onClick={handlers.open}
            >
                Bulk Upload Products
            </Button>

            {opened && <UploadCsvModal handleClose={handlers.close} />}
        </>
    );
};

const UploadCsvModal = ({ handleClose }: { handleClose: () => void }) => {
    const [state, setState] = useState<BulkUploadState>({
        file: null,
        manufacturer: null,
        type: null,
        bulkUploadId: null,
        uploadStatus: 'Submit',
        imageUploadStatus: 'Upload Images',
        error: null,
        errorTemplate: null,
    });

    const resetMessages = () => {
        setState({
            ...state,
            ...{
                uploadStatus: 'Submit',
                imageUploadStatus: 'Upload Images',
                error: null,
                errorTemplate: null,
            },
        });
    };

    const updateState = (updatedFields: Partial<BulkUploadState>) => {
        setState((prev) => ({ ...prev, ...updatedFields }));
    };

    const isBulkUploadDisabled = () => {
        return (
            !state.file ||
            !state.type ||
            !state.manufacturer ||
            state.uploadStatus === 'Success' ||
            state.uploadStatus === 'Processing'
        );
    };

    const isBulkUploadImageDisabled = () => {
        return !state.file || !state.manufacturer || state.uploadStatus !== 'Success';
    };

    const handleSetFile = (file: File | null) => {
        resetMessages();
        updateState({ file });
    };

    const handleTypeClick = (typeValue: any) => {
        resetMessages();
        updateState({
            type: typeValue,
        });
    };

    const handleDownloadTemplate = () => {
        const link = document.createElement('a');
        link.download = 'download.txt';
        link.target = '_blank';
        link.href = `${publicConfig.urls.api}/bulk-upload/template?type=${state.type}`;
        link.addEventListener('click', async (event) => {
            event.preventDefault();
            const href = (event?.currentTarget as any).href;

            if (await ProductCsvService.checkTemplateIsAvailable(href)) {
                window.open(href, '_blank');
            } else {
                updateState({ errorTemplate: 'Template not available' });
                console.error(`Template not available: ${href}`);
            }
        });
        link.click();
    };

    const handleBulkUpload = async () => {
        const { file, type, manufacturer } = state;
        if (!file || !type || !manufacturer) return;

        updateState({ uploadStatus: 'Processing' });
        try {
            const response = await ProductCsvService.bulkUpload(file, type, manufacturer);
            updateState({
                bulkUploadId: response.bulkUploadId,
                uploadStatus: 'Success',
                error: JSON.stringify(response, null, 2),
            });
        } catch (e: any) {
            resetMessages();
            updateState({
                uploadStatus: 'Error',
                error: e.message,
            });
        }
    };

    const handleBulkUploadImages = async () => {
        const { file, type, manufacturer, bulkUploadId } = state;
        if (!file || !type) return;

        updateState({
            imageUploadStatus: 'Processing',
            file: null,
        });

        try {
            const response = await ProductCsvService.bulkUploadImages(file, type, manufacturer, bulkUploadId);
            resetMessages();
            updateState({
                imageUploadStatus: 'Success',
                error: JSON.stringify(response, null, 2),
                file: null,
            });
        } catch (e: any) {
            resetMessages();
            updateState({
                imageUploadStatus: 'Error',
                error: e.message,
            });
        }
    };

    return (
        <Modal opened onClose={handleClose} title="Upload your Products CSV" size="xl">
            <Timeline bulletSize={13} lineWidth={1} classNames={{ itemTitle: cx.timelineTitle }}>
                {/* Step 1: Select Product Type */}
                <Timeline.Item title="Select Product Type">
                    <ComponentTypeField name="type" componentTypes={NEW_COMPONENT_TYPES} onClick={handleTypeClick} />
                </Timeline.Item>

                {/* Step 2: Select Manufacturer */}
                <Timeline.Item title="Select Manufacturer">
                    <InlineFiltersManufacturer
                        isRequired
                        onSelectOptional={(manufacturer) => updateState({ manufacturer })}
                    />
                </Timeline.Item>

                {/* Step 3: Download Template */}
                <Timeline.Item title="Download template">
                    <Button
                        variant="outline"
                        size="xs"
                        leftSection={<IoCloudDownloadOutline />}
                        onClick={handleDownloadTemplate}
                    >
                        Download
                    </Button>
                    <Text c="dimmed" fz="sm" pt={8}>
                        This file provides a pre-formatted CSV template with the minimum fields to create a Product.
                    </Text>
                    {state.errorTemplate && <Text c="red">{state.errorTemplate}</Text>}
                </Timeline.Item>

                {/* Step 4: Upload CSV */}
                <Timeline.Item title="Upload your CSV">
                    <FileButton onChange={handleSetFile} accept="csv">
                        {(props) => <Button {...props}>Upload File</Button>}
                    </FileButton>
                </Timeline.Item>

                {/* Step 5: Submit */}
                <Timeline.Item title="Submit">
                    <Stack>
                        <Button
                            disabled={isBulkUploadDisabled()}
                            leftSection={<IoAddSharp />}
                            onClick={handleBulkUpload}
                        >
                            {state.uploadStatus}
                        </Button>

                        <Button
                            disabled={isBulkUploadImageDisabled()}
                            leftSection={<IoAddSharp />}
                            onClick={handleBulkUploadImages}
                        >
                            {state.imageUploadStatus}
                        </Button>
                    </Stack>

                    {state.error && <JsonInput mt="sm" autosize value={state.error} />}
                </Timeline.Item>
            </Timeline>
        </Modal>
    );
};

export { BulkUploadCsv };
