import { useEffect, useState } from 'react';

import { Button, Loader, Modal, ModalProps, SimpleGrid, Stack, Text } from '@mantine/core';

import Link from 'next/link';
import { IoAddSharp } from 'react-icons/io5';

import { Page } from 'components/page';
import { ComponentSignup } from 'components/component-signup/ComponentSignup';
import { CompanySignupInfo } from 'components/company-signup/components/CompanySignupInfo';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { useRouter } from 'next/router';
import { useCurrentUser } from 'hooks/use-current-user';
import { CompanyButton } from 'components/company-button/CompanyButton';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

const modalProps: ModalProps = {
    opened: true,
    onClose: () => {},
    withCloseButton: false,
    closeOnClickOutside: false,
    closeOnEscape: false,
    padding: 'lg',
};

const ComponentSignupStart = () => {
    const router = useRouter();
    const user = useCurrentUser();
    const { companies: userCompanies, isLoading } = useCurrentTeamCompanies();

    const [redirecting, setRedirecting] = useState(false);

    const handleCancel = () => {
        setRedirecting(true);
        router.back();
    };

    useEffect(() => {
        if (user?.developer) {
            return;
        }

        if (router.query.manufacturer || router.query.distributor) {
            return;
        }

        if (userCompanies.length === 1) {
            const company = userCompanies[0];
            const href = ComponentHelpers.urls.create({ company });

            router.replace(href);
        }
    }, [userCompanies]);

    if (user?.developer) {
        return <ComponentSignup />;
    }

    if (router.query.manufacturer || router.query.distributor) {
        return <ComponentSignup />;
    }

    let modal = null;

    if (isLoading) {
        return (
            <Page>
                <Page.CenteredContent>
                    <Loader />
                </Page.CenteredContent>
            </Page>
        );
    }

    if (userCompanies.length === 0 && !isLoading) {
        modal = (
            <Modal {...modalProps} title={'Create a company profile before adding products'}>
                <Stack>
                    <Text>With a profile, you can:</Text>

                    <CompanySignupInfo hidePremiumBadges />

                    <Stack gap={'xs'} mt="md">
                        <Button
                            component={Link}
                            href={CompanyProfileHelpers.urls.create()}
                            leftSection={<IoAddSharp />}
                        >
                            Create Company Profile
                        </Button>

                        <Button variant="subtle" bg="white" onClick={handleCancel} loading={redirecting}>
                            Back
                        </Button>
                    </Stack>
                </Stack>
            </Modal>
        );
    }

    if (userCompanies.length > 1) {
        modal = (
            <Modal {...modalProps} size="lg">
                <SimpleGrid cols={{ base: 2, sm: 4 }} spacing={8}>
                    {userCompanies.map((manufacturer) => {
                        return (
                            <CompanyButton
                                key={manufacturer.id}
                                label="Add product to"
                                company={manufacturer}
                                href={ComponentHelpers.urls.create({ company: manufacturer })}
                            />
                        );
                    })}
                    <CompanyButton
                        href={CompanyProfileHelpers.urls.create()}
                        label="Sign up new manufacturer"
                        icon={<IoAddSharp size={24} />}
                    />
                </SimpleGrid>

                <Button
                    fullWidth
                    variant="subtle"
                    bg="transparent"
                    onClick={handleCancel}
                    loading={redirecting}
                    mt="md"
                >
                    Cancel
                </Button>
            </Modal>
        );
    }

    return (
        <>
            <ComponentSignup />
            {modal}
        </>
    );
};

export { ComponentSignupStart };
