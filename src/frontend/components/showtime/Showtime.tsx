import React, { FC, useEffect } from 'react';

import { useRouter } from 'next/router';

import { Box, Card, Flex, Group, Stack, Text, Title } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { RouterService } from 'services/RouterService';
import { ShowtimeService } from 'services/ShowtimeService';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCompanyProfileBySlug } from 'hooks/use-company-profile';
import { useSetActiveEventOnMount } from 'components/plan/hooks/use-set-active-event';

import { Page } from 'components/page/Page';

import { ReplusLogo } from 'components/logo/ReplusLogo';
import { LoginButton } from 'components/header/components/LoginButton';
import { NotesSummary } from 'components/showtime/components/NotesSummary';
import { SavedProfiles } from 'components/showtime/components/SavedProfiles';
import { SaveProfileModal } from 'components/showtime/components/SaveProfileModal';

import cx from './Showtime.module.scss';

const Showtime: FC = () => {
    useSetActiveEventOnMount();

    const user = useCurrentUser();

    const router = useRouter();

    const { company } = useCompanyProfileBySlug(router.query.profile as string | undefined);

    const [saveProfileOpened, saveProfileHandlers] = useDisclosure(false);

    useEffect(() => {
        if (company?.id) {
            saveProfileHandlers.open();
        }
    }, [company?.id]);

    const closeModal = () => {
        saveProfileHandlers.close();
        RouterService.removeQuery('profile', 'replace');
    };

    const handleDelete = (profileId: string) => {
        ShowtimeService.deleteQrScan(profileId);
        closeModal();
    };

    return (
        <Page
            title="Showtime"
            showBackground
            hideFooter
            hideLicenseAgreement
            breadcrumbs={{ type: 'floating.fullWidth' }}
        >
            <Page.Hero pb={50} px={0} mx={0} mb={-40}>
                <Page.Content maw={800} py={0} my={0}>
                    <Box className={cx.hero}>
                        <Group gap="xs" className={cx.heroLeft}>
                            <ReplusLogo isWhite width={50} />

                            <Stack gap={0} align="start">
                                <Title fw={800}>Showtime</Title>
                                <Text fw={500} c="dimmed" fz="sm">
                                    Find and save your favorite profiles
                                </Text>
                            </Stack>
                        </Group>

                        <NotesSummary />
                    </Box>
                </Page.Content>
            </Page.Hero>

            <Page.Content maw={800}>
                <Stack>
                    {!user && (
                        <Card className="gradient-background-light">
                            <Flex style={{ opacity: 0.9 }} justify="space-between" align="center" gap="xs">
                                <Text fw={600}>Save your favorites — and don’t lose your notes.</Text>
                                <LoginButton
                                    buttonProps={{
                                        size: 'xs',
                                        variant: 'filled',
                                        color: 'brand',
                                        style: { flexShrink: 0 },
                                    }}
                                >
                                    Secure My RE+ List
                                </LoginButton>
                            </Flex>
                        </Card>
                    )}

                    <SavedProfiles handleEditProfile={(slug) => RouterService.setQuery('profile', slug)} />

                    {company && (
                        <SaveProfileModal
                            company={company}
                            opened={saveProfileOpened}
                            onClose={closeModal}
                            handleDelete={handleDelete}
                        />
                    )}
                </Stack>
            </Page.Content>
        </Page>
    );
};

export { Showtime };
