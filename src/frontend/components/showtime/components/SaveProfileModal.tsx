import { useEffect } from 'react';

import { useLocalStorage } from '@mantine/hooks';

import { CompanyProfile, QrScanPayload } from 'models';

import { ActionIcon, Button, Modal } from '@mantine/core';
import { IoClose } from 'react-icons/io5';

import { UserHelpers } from 'helpers/UserHelpers';

import { ShowtimeService } from 'services/ShowtimeService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { useLocalEvent } from 'hooks/use-local-event';
import { useCurrentUser } from 'hooks/use-current-user';
import { useSavedProfiles } from 'components/showtime/hooks/useSavedProfiles';

import { SaveProfileForm } from 'components/showtime/components/SaveProfileForm';

const SaveProfileModal = ({
    company,
    opened,
    onClose,
    handleDelete,
}: {
    company: CompanyProfile;
    opened: boolean;
    onClose: () => void;
    handleDelete?: (profileId: string) => void;
}) => {
    const user = useCurrentUser();
    const { localEvent } = useLocalEvent();
    const { qrScans, isLoading } = useSavedProfiles();

    const [_email, setEmail, removeEmail] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.email,
    });
    const [_name, setName, removeName] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.name,
    });

    const email = _email !== 'undefined' ? _email : undefined;
    const name = _name !== 'undefined' ? _name : undefined;

    const onSubmit = async (values: Partial<QrScanPayload>) => {
        if (values.id) {
            ShowtimeService.editQrScan(values.id, values);
        } else {
            ShowtimeService.createQrScan(values);
            setEmail(values.userInfo?.email);
            setName(values.userInfo?.name);
        }

        onClose();
    };

    const removeUser = () => {
        removeEmail();
        removeName();
    };

    const qrSave = qrScans.find((qrScan) => qrScan.company === company.id);

    const defaultValues: Partial<QrScanPayload> = {
        ...qrSave,
        user: user?.id,
        company: company.id,
        event: localEvent?.id,
    };

    if (!user) {
        defaultValues.userInfo = { email, name };
    }

    const isEditMode = !!defaultValues.id;

    useEffect(() => {
        if (isLoading || isEditMode) {
            return;
        }

        InternalTrackingService.track('qrScan.view', {
            company: company.id,
            email,
            name,
            event: localEvent?.id,
        });
    }, [isLoading, isEditMode]);

    if (!company) {
        return null;
    }

    return (
        <Modal
            zIndex={300}
            onClose={onClose}
            opened={opened}
            withCloseButton={false}
            closeOnClickOutside={isEditMode}
            closeOnEscape={isEditMode}
            styles={{
                overlay: {
                    backdropFilter: 'blur(2px)',
                },
                inner: {
                    alignItems: 'center',
                },
                content: {
                    position: 'relative',
                    background: 'var(--mantine-color-gray-1)',
                    backdropFilter: 'blur(10px)',
                },
            }}
            xOffset={10}
            yOffset={10}
        >
            {isEditMode && (
                <ActionIcon
                    onClick={onClose}
                    radius={99}
                    variant="white"
                    style={{ position: 'absolute', top: 10, right: 10, zIndex: 1 }}
                >
                    <IoClose />
                </ActionIcon>
            )}
            <SaveProfileForm
                company={company}
                onSubmit={onSubmit}
                defaultValues={defaultValues}
                handleRemoveUser={removeUser}
                showUserFields={!user && !qrSave}
            />
            {isEditMode && handleDelete && (
                <Button fullWidth variant="transparent" onClick={() => handleDelete(defaultValues.id!)} c="red" mt="xs">
                    Delete Note
                </Button>
            )}
        </Modal>
    );
};

export { SaveProfileModal };
