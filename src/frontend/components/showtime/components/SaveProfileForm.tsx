import { unique } from 'radash';

import { ActionIcon, Anchor, Card, Flex, Group, InputLabel, Stack, Text, Title } from '@mantine/core';
import { IoBookmark, IoSparkles } from 'react-icons/io5';

import { CompanyProfile, QrScan, QrScanSchema } from 'models';

import { getId } from 'helpers/getId';

import { useTeam } from 'hooks/use-team';
import { useUsers } from 'hooks/use-users';

import { Hero } from 'components/page/Hero';
import { ReplusLogo } from 'components/logo/ReplusLogo';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { FormStatus } from 'components/forms/FormStatus';
import { TagsField } from 'components/forms/fields/TagsField';
import { TextField } from 'components/forms/fields/TextField';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

const SaveProfileForm = ({
    company,
    onSubmit,
    defaultValues,
    handleRemoveUser,
    showUserFields,
}: {
    company: CompanyProfile;
    onSubmit: (values: Partial<QrScan>) => void;
    defaultValues: Partial<QrScan>;
    handleRemoveUser?: () => void;
    showUserFields?: boolean;
}) => {
    return (
        <Form<QrScan> zodSchema={QrScanSchema} onSubmit={onSubmit} defaultValues={defaultValues}>
            <Stack>
                <Hero p="lg" pb={50} mt={-16} mx={-20} mb={-50} w="calc(100% + 40px)">
                    <Group gap="xs">
                        <ReplusLogo isWhite width={50} />

                        <Stack gap={0} align="start">
                            <Title fw={800}>Save {company.name} profile</Title>
                            <Text fw={500} c="dimmed" fz="sm">
                                Add to My RE+ List
                            </Text>
                        </Stack>
                    </Group>
                </Hero>

                <CompanyProfileTeaser
                    company={company}
                    showActions={false}
                    showProductsPreview
                    cardProps={{ shadow: 'xs' }}
                />

                {showUserFields &&
                    (defaultValues.userInfo?.email ? (
                        <Text>
                            <InputLabel>Email</InputLabel>
                            <Text fw={500} c="dimmed">
                                {defaultValues.userInfo?.email}
                            </Text>
                            {handleRemoveUser && (
                                <Anchor fz="xs" fw={500} onClick={handleRemoveUser}>
                                    Change
                                </Anchor>
                            )}
                        </Text>
                    ) : (
                        <>
                            <TextField name="userInfo.name" label="Your name" />
                            <TextField name="userInfo.email" type="email" autoComplete="email" label="Your email" />
                        </>
                    ))}

                <ContactField company={company} />

                <Card className="gradient-background-light" p="xs" mt={-8}>
                    <Flex gap="xs" align="center">
                        <ActionIcon variant="white" radius={99} color="brand.4">
                            <IoSparkles />
                        </ActionIcon>
                        <Text fw={500} fz="sm">
                            Add your notes and get an AI-powered summary of the event
                        </Text>
                    </Flex>

                    <MultilineTextField name="note" placeholder="My Notes" mt="xs" />
                </Card>

                <FormSubmit variant="gradient" leftSection={<IoBookmark />} size="lg">
                    {defaultValues.id ? 'Update Notes' : 'Save Profile'}
                </FormSubmit>

                <FormStatus showValidationErrors />
            </Stack>
        </Form>
    );
};

const ContactField = ({ company }: { company: CompanyProfile }) => {
    const { team } = useTeam(company.team);
    const { users } = useUsers(
        unique([
            ...(company.users.map((user) => getId(user)) || []),
            ...(team?.users.map((user) => getId(user)) || []),
        ] as string[]),
    );

    return (
        <TagsField
            name="contact"
            label="Who did you talk to?"
            data={users.filter((user) => !!user.name).map((user) => user.name as string)}
            placeholder="Start typing name"
        />
    );
};

export { SaveProfileForm };
