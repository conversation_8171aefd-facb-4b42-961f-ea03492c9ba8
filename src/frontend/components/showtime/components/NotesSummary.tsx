import { useState } from 'react';

import { <PERSON><PERSON>, But<PERSON> } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { IoSparkles } from 'react-icons/io5';

import { UserHelpers } from 'helpers/UserHelpers';

import { ShowtimeService } from 'services/ShowtimeService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { useCurrentUser } from 'hooks/use-current-user';
import { useSavedProfiles } from 'components/showtime/hooks/useSavedProfiles';

import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';

const NotesSummary = () => {
    const user = useCurrentUser();
    const [_email] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.email,
    });
    const email = _email !== 'undefined' ? _email : undefined;

    const [summary, setSummary] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const { qrScans } = useSavedProfiles();

    const processStream = async (reader: ReadableStreamDefaultReader) => {
        const { value, done } = await reader.read();

        if (done) {
            setIsLoading(false);
            return;
        }

        if (value?.content) {
            setSummary((prev) => prev + value.content);
        }

        await processStream(reader);
    };

    const generateSummary = async () => {
        if (!user?.id && !email) {
            LocalNotificationService.showError({
                message: 'User information is missing',
            });
            return;
        }

        setSummary('');
        setIsLoading(true);

        try {
            const stream = await ShowtimeService.summarizeVisitorNotes({ userId: user?.id, email });
            const reader = stream.getReader();
            await processStream(reader);
        } catch (err) {
            setSummary('');
            console.error('Notes summarize error:', err);
            LocalNotificationService.showError({
                message: 'Failed to generate summary. Please try again.',
            });
        }
    };

    if (!qrScans?.length) {
        return null;
    }

    return (
        <>
            <Button
                size="xs"
                variant="gradient"
                onClick={generateSummary}
                loading={isLoading}
                leftSection={<IoSparkles />}
            >
                Summarize My Notes
            </Button>

            <Modal title="Notes Summary" opened={!!summary} onClose={() => setSummary('')} size="lg">
                <AIMarkdown>{summary}</AIMarkdown>
            </Modal>
        </>
    );
};

export { NotesSummary };
