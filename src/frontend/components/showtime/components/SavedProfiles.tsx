import React from 'react';

import Link from 'next/link';

import { <PERSON><PERSON>, <PERSON>ton, Card, Flex, Loader } from '@mantine/core';
import { BsPencilFill } from 'react-icons/bs';
import { Io<PERSON>erson } from 'react-icons/io5';

import { QrScan } from 'models';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useSavedProfiles } from 'components/showtime/hooks/useSavedProfiles';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { IconWithText } from 'elements/IconWithText';
import { GridSection } from 'components/section/GridSection';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { InAppSupportBadge } from 'elements/badge/InAppSupportBadge';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

const SavedProfiles = ({ handleEditProfile }: { handleEditProfile: (slug: string) => void }) => {
    const { qrScans, isLoading } = useSavedProfiles();

    return (
        <>
            {!qrScans?.length && !isLoading && (
                <EmptyMessage>
                    You are not following any profiles yet
                    <Button component={Link} href={RouterHelpers.urls.searchTab('profiles')}>
                        Browse profiles
                    </Button>
                </EmptyMessage>
            )}

            {isLoading && (
                <Card mx="auto" radius={99} p="xs" shadow="xs">
                    <Loader size="sm" color="primary.4" />
                </Card>
            )}

            <GridSection nbCols={1}>
                {qrScans?.map((profile) => (
                    <SavedProfile
                        key={profile.company}
                        profileId={profile.company}
                        contact={profile.contact}
                        note={profile.note}
                        handleEdit={handleEditProfile}
                    />
                ))}
            </GridSection>
        </>
    );
};

const SavedProfile = ({
    profileId,
    contact,
    note,
    handleEdit,
}: { profileId: string; handleEdit: (slug: string) => void } & Partial<Pick<QrScan, 'contact' | 'note'>>) => {
    const { company } = useCompanyProfile(profileId);

    if (!company) {
        return null;
    }

    return (
        <CompanyProfileTeaser company={company} showProductsPreview showActions={false}>
            <Card bg="yellow.0">
                {contact?.length && (
                    <IconWithText
                        icon={<IoPerson />}
                        iconColor="yellow.9"
                        text={`Talked to ${contact?.join(', ')}`}
                        textProps={{ fz: 'sm' }}
                    />
                )}
                <IconWithText
                    icon={<BsPencilFill />}
                    iconColor="yellow.9"
                    text={
                        <>
                            {note ? (
                                <>
                                    {note}{' '}
                                    <Anchor
                                        inherit
                                        onClick={() => handleEdit(company.slug)}
                                        c="yellow.9"
                                        td="underline"
                                        style={{ whiteSpace: 'nowrap' }}
                                    >
                                        (edit)
                                    </Anchor>
                                </>
                            ) : (
                                <Anchor inherit onClick={() => handleEdit(company.slug)} c="yellow.9" td="underline">
                                    Add your note
                                </Anchor>
                            )}
                        </>
                    }
                    textProps={{ fz: 'sm' }}
                />
            </Card>

            <Flex gap={4}>
                <Button
                    fullWidth
                    variant="outline"
                    size="xs"
                    component={Link}
                    href={CompanyProfileHelpers.urls.view(company.slug)}
                >
                    View profile
                </Button>
                <Button
                    fullWidth
                    variant="outline"
                    size="xs"
                    component={Link}
                    href={CompanyProfileHelpers.urls.view(company.slug, 'products')}
                >
                    View products
                </Button>
                <InAppSupportBadge company={company} fullWidth variant="outline" size="xs" label="Chat" />
            </Flex>
        </CompanyProfileTeaser>
    );
};

export { SavedProfiles };
