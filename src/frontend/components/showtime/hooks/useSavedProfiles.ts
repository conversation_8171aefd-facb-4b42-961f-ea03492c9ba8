import { useLocalStorage } from '@mantine/hooks';

import { UserHelpers } from 'helpers/UserHelpers';

import { useQrScans } from 'hooks/use-qr-scans';
import { useCurrentUser } from 'hooks/use-current-user';

const useSavedProfiles = () => {
    const [_email] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.email,
    });
    const email = _email !== 'undefined' ? _email : undefined;

    const user = useCurrentUser();

    const qrScans = useQrScans({ email, userId: user?.id });

    return qrScans;
};

export { useSavedProfiles };
