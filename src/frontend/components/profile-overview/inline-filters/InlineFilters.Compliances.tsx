import type { FC } from 'react';

import { Menu } from '@mantine/core';
import { IoCheckmarkOutline } from 'react-icons/io5';

import { Compliance, Compliances } from 'models';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

const COMPLIANCES = [Compliance.CURRENT_OS, Compliance.EMERGE, Compliance.ODCA, Compliance.OTHER];

const InlineFiltersCompliances: FC = () => {
    const {
        filters: { compliances },
    } = useProfileOverviewSearch();

    const onClick = (value: Compliance) => {
        ProfileOverviewService.setFilter(
            'compliances',
            compliances?.includes(value)
                ? compliances.filter((compliance) => compliance !== value)
                : [...(compliances ?? []), value],
        );
    };

    const onRemove = () => {
        ProfileOverviewService.setFilter('compliances', []);
    };

    const formattedValue = Compliances.options
        .filter(({ value }) => compliances?.includes(value))
        .map(({ label }) => label)
        .join(', ');

    return (
        <InlineFilters.Section
            label={!formattedValue && 'Organizations'}
            body={formattedValue}
            onRemove={compliances?.length ? onRemove : undefined}
            popoverContent={
                <>
                    <Menu.Label>Organizations</Menu.Label>
                    {Compliances.options
                        .filter(({ value }) => COMPLIANCES.includes(value))
                        .map(({ value, label }) => (
                            <Menu.Item
                                key={value}
                                onClick={() => onClick(value)}
                                rightSection={compliances?.includes(value) ? <IoCheckmarkOutline /> : null}
                            >
                                {label}
                            </Menu.Item>
                        ))}
                </>
            }
            active={!!compliances?.length}
        />
    );
};

export { InlineFiltersCompliances };
