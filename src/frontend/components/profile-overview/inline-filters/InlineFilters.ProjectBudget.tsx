import { Menu } from '@mantine/core';
import { IoCheckmarkOutline } from 'react-icons/io5';

import { PROJECT_BUDGETS } from 'models';

import { NumberHelpers } from 'helpers/NumberHelpers';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersProjectBudget = () => {
    const {
        filters: { projectBudget },
    } = useProfileOverviewSearch();

    const handleChange = (_projectBudget: number) => {
        ProfileOverviewService.setFilter('projectBudget', _projectBudget);
    };

    const handleRemove = () => {
        ProfileOverviewService.setFilter('projectBudget', undefined);
    };

    return (
        <InlineFilters.Section
            icon={projectBudget ? undefined : '$'}
            label={projectBudget ? 'Budget' : 'Project Budget'}
            body={projectBudget && NumberHelpers.formatPriceShort(projectBudget)}
            active={!!projectBudget}
            popoverContent={
                <>
                    <Menu.Label>Project Budget</Menu.Label>
                    {PROJECT_BUDGETS.map((budget) => (
                        <Menu.Item
                            key={budget}
                            onClick={() => handleChange(budget)}
                            rightSection={projectBudget === budget && <IoCheckmarkOutline />}
                        >
                            {NumberHelpers.formatPriceShort(budget)}
                        </Menu.Item>
                    ))}
                </>
            }
            onRemove={projectBudget ? handleRemove : undefined}
        />
    );
};

export { InlineFiltersProjectBudget };
