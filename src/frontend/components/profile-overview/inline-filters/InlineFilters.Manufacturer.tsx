import React from 'react';

import { IoBriefcaseOutline } from 'react-icons/io5';

import { PublishedStatus } from 'models';

import { InlineFilters } from './InlineFilters';
import { useProfileOverviewSearch } from '../hooks/use-profile-overview-search';
import { ProfileOverviewService } from '../services/ProfileOverviewService';
import { useCompanyProfiles } from 'hooks/use-company-profiles';

const InlineFiltersManufacturer = () => {
    const {
        filters: { manufacturer },
    } = useProfileOverviewSearch();

    const { companies } = useCompanyProfiles({ status: PublishedStatus.PUBLISHED });

    const selectedManufacturers = companies.filter(({ id }) =>
        typeof manufacturer === 'string' ? id === manufacturer : manufacturer?.includes(id),
    );

    if (!selectedManufacturers.length) return null;

    return (
        <InlineFilters.SectionWithIcon
            icon={<IoBriefcaseOutline />}
            onRemove={() => ProfileOverviewService.setFilter('manufacturer', undefined)}
            body={selectedManufacturers ? selectedManufacturers.map(({ name }) => name).join(', ') : undefined}
            active
        />
    );
};
export { InlineFiltersManufacturer };
