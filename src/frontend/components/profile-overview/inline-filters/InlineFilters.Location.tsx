import type { FC } from 'react';

import dynamic from 'next/dynamic';

import { Box } from '@mantine/core';
import { Geocoder } from '@mapbox/search-js-react';
import { IoLocationOutline } from 'react-icons/io5';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

const MapboxInput = dynamic(() => import('components/forms/MapboxInput.lazy'), {
    ssr: false,
});

const InlineFiltersLocation: FC = () => {
    const {
        filters: { location },
    } = useProfileOverviewSearch();

    const onRemove = () => {
        ProfileOverviewService.setFilter('location', undefined);
    };

    const onRetrieve: Parameters<typeof Geocoder>[0]['onRetrieve'] = (props) => {
        const {
            properties: { full_address, coordinates },
        } = props;

        ProfileOverviewService.setFilter('location', {
            label: full_address,
            coordinates,
        });
    };

    return (
        <InlineFilters.Section
            icon={<IoLocationOutline />}
            onRemove={location ? onRemove : undefined}
            body={
                <Box maw={`${location?.label?.length ?? 10}ch`}>
                    <MapboxInput
                        value={location?.label}
                        onRetrieve={onRetrieve}
                        placeholder="Location"
                        options={{
                            types: new Set(['country', 'region', 'place']),
                        }}
                        popoverOptions={{
                            offset: 11,
                        }}
                        theme={{
                            variables: {
                                border: 'none',
                                unit: 'var(--mantine-font-size-md)',
                                minWidth: '200px',
                            },
                        }}
                    />
                </Box>
            }
            active={!!location}
        />
    );
};

export { InlineFiltersLocation };
