import React, { useState } from 'react';

import { ActionIcon, Badge, Group, Menu, TextInput } from '@mantine/core';
import { IoCheckmarkOutline, IoClose } from 'react-icons/io5';

import { useCompanyApplicationTags } from 'hooks/use-company-application-tags';
import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { TextHelpers } from 'helpers/TextHelpers';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { InlineFiltersSection } from 'components/inline-filters/InlineFilters.Section';

const InlineFiltersApplication = () => {
    const {
        filters: { applicationTags },
    } = useProfileOverviewSearch();

    const isSelected = (tag: string) => applicationTags?.includes(tag) ?? false;

    const toggleApplication = (tag: string) => {
        ProfileOverviewService.setFilter(
            'applicationTags',
            isSelected(tag)
                ? (applicationTags?.filter((_tag) => _tag !== tag) ?? undefined)
                : [...(applicationTags ?? []), tag],
        );
    };

    const clearApplication = () => {
        ProfileOverviewService.setFilter('applicationTags', []);
    };

    const { applicationTags: applicationTagsSuggestions } = useCompanyApplicationTags();

    const renderSelected = () => {
        if (applicationTags?.length === 1) {
            return applicationTags[0];
        }

        if (applicationTags?.length) {
            return (
                <Group gap={4}>
                    Application{' '}
                    <Badge size="xs" variant="light">
                        {applicationTags?.length}
                    </Badge>
                </Group>
            );
        }

        return '';
    };

    const [search, setSearch] = useState('');

    const filteredTags = search
        ? applicationTagsSuggestions?.filter((tag) => TextHelpers.textMatches(tag, search))
        : applicationTagsSuggestions;

    return (
        <InlineFiltersSection
            label={!applicationTags?.length && 'Application'}
            body={renderSelected()}
            onRemove={applicationTags?.length ? clearApplication : undefined}
            active={!!applicationTags?.length}
            popoverContent={
                <>
                    <TextInput
                        miw={200}
                        mb={8}
                        value={search}
                        placeholder="Search application"
                        onChange={(event: React.ChangeEvent<HTMLInputElement>) => setSearch(event.currentTarget.value)}
                        rightSection={
                            search && (
                                <ActionIcon onClick={() => setSearch('')} size="xs" variant="light">
                                    <IoClose />
                                </ActionIcon>
                            )
                        }
                    />

                    {filteredTags?.map((tag) => (
                        <Menu.Item
                            key={tag}
                            onClick={() => toggleApplication(tag)}
                            rightSection={applicationTags?.includes(tag) ? <IoCheckmarkOutline /> : null}
                        >
                            {tag}
                        </Menu.Item>
                    ))}

                    {filteredTags?.length === 0 && <Menu.Item disabled>No application found</Menu.Item>}
                </>
            }
        />
    );
};

export { InlineFiltersApplication };
