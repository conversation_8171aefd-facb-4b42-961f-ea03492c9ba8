import React, { useState } from 'react';

import { ActionIcon, Badge, Group, Menu, TextInput } from '@mantine/core';
import { IoCheckmarkOutline, IoClose } from 'react-icons/io5';

import { CompanyService, CompanyServiceOptions } from 'models';

import { useCompanyServiceTags } from 'hooks/use-company-service-tags';
import { InlineFiltersSection } from 'components/inline-filters/InlineFilters.Section';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';
import { TextHelpers } from 'helpers/TextHelpers';

const EXCLUDE_SERVICES = [
    CompanyService.IN_APP_SUPPORT,
    CompanyService.ORGANIZATION,
    CompanyService.REPLUS,
    CompanyService.OTHER,
];

const SERVICES = CompanyServiceOptions.filter((service) => !EXCLUDE_SERVICES.includes(service.value));

const InlineFiltersServices = () => {
    const {
        filters: { services },
    } = useProfileOverviewSearch();

    const isSelected = (service: CompanyService) => services?.includes(service) ?? false;

    const toggleService = (service: CompanyService) => {
        ProfileOverviewService.setFilter(
            'services',
            isSelected(service)
                ? (services?.filter((_service) => _service !== service) ?? undefined)
                : [...(services ?? []), service],
        );
    };

    const clearServices = () => {
        ProfileOverviewService.setFilter('services', []);
    };

    const { serviceTags } = useCompanyServiceTags();

    const renderSelectedServices = () => {
        if (services?.length === 1) {
            return CompanyServiceOptions.find(({ value }) => value === services[0])?.label ?? services[0];
        }

        if (services?.length) {
            return (
                <Group gap={4}>
                    Services{' '}
                    <Badge size="xs" variant="light">
                        {services?.length}
                    </Badge>
                </Group>
            );
        }

        return '';
    };

    const [search, setSearch] = useState('');

    const filteredServices = SERVICES.filter(
        (service) => service.value !== CompanyService.IN_APP_SUPPORT && TextHelpers.textMatches(service.label, search),
    );

    // verify
    const filteredServiceTags = serviceTags.filter((tag) => TextHelpers.textMatches(tag, search));

    return (
        <InlineFiltersSection
            label={!services?.length && 'Services'}
            body={renderSelectedServices()}
            onRemove={services?.length ? clearServices : undefined}
            active={!!services?.length}
            popoverContent={
                <>
                    <TextInput
                        miw={200}
                        mb={8}
                        value={search}
                        placeholder="Search service"
                        onChange={(event: React.ChangeEvent<HTMLInputElement>) => setSearch(event.currentTarget.value)}
                        rightSection={
                            search && (
                                <ActionIcon onClick={() => setSearch('')} size="xs" variant="light">
                                    <IoClose />
                                </ActionIcon>
                            )
                        }
                    />

                    {filteredServices.map(({ value, label }) => (
                        <Menu.Item
                            key={value}
                            onClick={() => toggleService(value)}
                            rightSection={services?.includes(value) ? <IoCheckmarkOutline /> : null}
                        >
                            {label}
                        </Menu.Item>
                    ))}

                    {filteredServiceTags.map((tag) => (
                        <Menu.Item
                            key={tag}
                            onClick={() => toggleService(tag as CompanyService)}
                            rightSection={services?.includes(tag as CompanyService) ? <IoCheckmarkOutline /> : null}
                        >
                            {tag}
                        </Menu.Item>
                    ))}

                    {filteredServices.length === 0 && filteredServiceTags.length === 0 && (
                        <Menu.Item disabled>No services found</Menu.Item>
                    )}
                </>
            }
        />
    );
};

export { InlineFiltersServices };
