import type { FC } from 'react';

import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { InlineFiltersMoreButton as Component } from 'components/inline-filters/InlineFilters.MoreButton';

const InlineFiltersMoreButton: FC = () => {
    const { filters } = useProfileOverviewSearch();

    const showClearButton = !ProfileOverviewService.isInitialSearch(filters);

    if (!showClearButton) return null;

    return <Component showClearButton onClear={ProfileOverviewService.resetFilters} />;
};

export { InlineFiltersMoreButton };
