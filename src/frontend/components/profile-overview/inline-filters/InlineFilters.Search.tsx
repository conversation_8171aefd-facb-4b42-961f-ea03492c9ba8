import React from 'react';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { InlineFiltersSearch as Component } from 'components/inline-filters/InlineFilters.Search';

const InlineFiltersSearch = () => {
    const {
        filters: { search },
    } = useProfileOverviewSearch();

    const handleRemove = () => {
        ProfileOverviewService.setFilter('search', undefined);
    };

    const handleChange = (value: string) => {
        ProfileOverviewService.setFilter('search', value);
    };

    return <Component search={search} onRemove={handleRemove} onChange={handleChange} />;
};

export { InlineFiltersSearch };
