import type { FC } from 'react';

import { IoChatbubblesOutline } from 'react-icons/io5';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

const InlineFiltersInAppSupport: FC = () => {
    const {
        filters: { inAppSupport },
    } = useProfileOverviewSearch();

    const onRemove = () => {
        ProfileOverviewService.setFilter('inAppSupport', false);
    };

    return (
        <InlineFilters.Section
            icon={<IoChatbubblesOutline />}
            wrapperProps={{
                onClick: () => {
                    ProfileOverviewService.setFilter('inAppSupport', true);
                },
            }}
            label="Provides in-app support"
            onRemove={inAppSupport ? onRemove : undefined}
            active={!!inAppSupport}
        />
    );
};

export { InlineFiltersInAppSupport };
