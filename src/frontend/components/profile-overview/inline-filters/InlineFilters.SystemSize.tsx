import { Menu } from '@mantine/core';
import { IoCheckmarkOutline } from 'react-icons/io5';

import { POWER_LEVELS, powerConverter } from 'models';

import { FormatHelpers } from 'helpers/formatters';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersSystemSize = () => {
    const {
        filters: { systemSize },
    } = useProfileOverviewSearch();

    const handleChange = (_systemSize: number) => {
        ProfileOverviewService.setFilter('systemSize', _systemSize);
    };

    const handleRemove = () => {
        ProfileOverviewService.setFilter('systemSize', undefined);
    };

    return (
        <InlineFilters.Section
            label={systemSize ? 'Size' : 'System Size'}
            body={
                systemSize &&
                FormatHelpers.formatMeasurement(
                    {
                        value: systemSize,
                        unit: 'W',
                    },
                    powerConverter,
                )
            }
            active={!!systemSize}
            popoverContent={
                <>
                    <Menu.Label>System Size</Menu.Label>
                    {POWER_LEVELS.map((level) => (
                        <Menu.Item
                            key={level}
                            onClick={() => handleChange(level)}
                            rightSection={systemSize === level && <IoCheckmarkOutline />}
                        >
                            {FormatHelpers.formatMeasurement(
                                {
                                    value: level,
                                    unit: 'W',
                                },
                                powerConverter,
                            )}
                        </Menu.Item>
                    ))}
                </>
            }
            onRemove={systemSize ? handleRemove : undefined}
        />
    );
};

export { InlineFiltersSystemSize };
