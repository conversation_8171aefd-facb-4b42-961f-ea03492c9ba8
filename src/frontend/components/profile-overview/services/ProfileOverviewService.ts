import { publicConfig } from '@public-config';
import { debounce, isEqual } from 'radash';

import { ProfileQuery, PublishedStatus, removeUndefined } from 'models';

import { UserHelpers } from 'helpers/UserHelpers';

import { ApiService } from 'services/ApiService';
import { LocalStorageService } from 'services/LocalStorageService';

import {
    DEFAULT_PROFILE_OVERVIEW_SEARCH,
    profileOverviewSearch,
} from 'components/profile-overview/state/profile-overview-search';
import { ProfileOverviewSearch } from 'components/profile-overview/types';

const ProfileOverviewService = {
    controller: null as AbortController | null,

    debouncedSearch: debounce({ delay: 500 }, () => {
        ProfileOverviewService.search().then();
    }),

    search: async () => {
        const event = LocalStorageService.get(UserHelpers.localStorageKey.event) || null;

        const { page, filters } = profileOverviewSearch;

        // cancel previous search calls
        if (profileOverviewSearch.isLoading) {
            ProfileOverviewService.controller?.abort();
        }

        profileOverviewSearch.isLoading = true;

        try {
            const controller = new AbortController();
            const { signal } = controller;
            ProfileOverviewService.controller = controller;

            const limit = 48;
            const sort = 'relevance';

            const query: ProfileQuery = {
                search: filters.search ?? undefined,
                status: PublishedStatus.PUBLISHED,
                services: filters.services,
                applicationTags: filters.applicationTags,
                isRePlus: false,
                compliances: filters.compliances,
                event,
                manufacturer: filters.manufacturer,
                systemSize: filters.systemSize,
                projectBudget: filters.projectBudget,
                inAppSupport: filters.inAppSupport,
                location: filters.location,
            };

            const params = new URLSearchParams({
                query: JSON.stringify(query),
                sort,
                page: page.toString(),
            });

            if (limit) {
                params.set('limit', limit.toString());
            }

            const result = await ApiService.get(`${publicConfig.urls.api}/manufacturers/search?${params.toString()}`, {
                signal,
            });

            profileOverviewSearch.profiles = result.docs;
            profileOverviewSearch.totalPages = result.totalPages;
            if (!ProfileOverviewService.isInitialSearch(filters)) {
                profileOverviewSearch.totalProfiles = result.totalResults;
            }
            profileOverviewSearch.isLoading = false;

            return result;
        } catch (error: any) {
            if (error?.name === 'AbortError') {
                return;
            }

            profileOverviewSearch.profiles = [];
            profileOverviewSearch.error = true;
            profileOverviewSearch.isLoading = false;
        }
    },

    setPage: (page: number) => {
        profileOverviewSearch.page = page;
    },

    setFilter: <FilterKey extends keyof ProfileOverviewSearch['filters']>(
        key: FilterKey,
        value: ProfileOverviewSearch['filters'][FilterKey],
    ) => {
        profileOverviewSearch.filters[key] = value;
    },

    mergeFilters(filters: ProfileQuery) {
        Object.entries(filters).forEach(([key, value]) => {
            ProfileOverviewService.setFilter(key as keyof ProfileQuery, value);
        });
    },

    setSearchBarQuery: (searchBarQuery: string | undefined) => {
        profileOverviewSearch.searchBarQuery = searchBarQuery;
    },

    resetFilters: () => {
        ProfileOverviewService.mergeFilters(DEFAULT_PROFILE_OVERVIEW_SEARCH.filters);
    },

    isInitialSearch: (filters: ProfileOverviewSearch['filters']) => {
        return isEqual(removeUndefined(filters), removeUndefined(DEFAULT_PROFILE_OVERVIEW_SEARCH.filters));
    },
};

export { ProfileOverviewService };
