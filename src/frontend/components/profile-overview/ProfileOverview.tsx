import React, { useEffect } from 'react';

import { Stack, Title } from '@mantine/core';

import { Page } from 'components/page';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { SearchBar } from 'components/profile-overview/components/SearchBar';
import { InlineFilters } from 'components/profile-overview/inline-filters/InlineFilters';
import { ProfileOverviewHits } from 'components/profile-overview/components/ProfileOverviewHits';

const ProfileOverview = () => {
    const { profiles, isLoading } = useProfileOverviewSearch();

    // initial search
    useEffect(() => {
        ProfileOverviewService.debouncedSearch();
    }, []);

    return <ProfileOverviewHits profiles={profiles} isLoading={isLoading} />;
};

const WrappedProfileOverview = () => {
    return (
        <Page
            showBackground
            breadcrumbs={{
                type: 'floating.fullWidth',
            }}
            title="Profiles"
        >
            <Page.Hero>
                <Title>Profiles</Title>

                <Stack mt="xl" maw={1000} mx="auto" gap={8}>
                    <SearchBar />
                    <InlineFilters />
                </Stack>
            </Page.Hero>
            <Page.WideContent>
                <ProfileOverview />
            </Page.WideContent>
        </Page>
    );
};

export { WrappedProfileOverview as ProfileOverview };
