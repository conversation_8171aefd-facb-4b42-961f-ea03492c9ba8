import { proxy, subscribe } from 'valtio';

import { deepClone } from 'helpers/deep-clone';

import { ProfileOverviewSearch } from 'components/profile-overview/types';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

export const DEFAULT_PROFILE_OVERVIEW_SEARCH: ProfileOverviewSearch = {
    profiles: [],
    page: 0,
    totalPages: 1,
    totalProfiles: 0,
    isLoading: false,
    error: false,
    searchBarQuery: undefined,
    filters: {
        search: undefined,
        services: [],
        applicationTags: [],
        inAppSupport: undefined,
        compliances: [],
        manufacturer: undefined,
        systemSize: undefined,
        projectBudget: undefined,
        location: undefined,
    },
};

const profileOverviewSearch = proxy<ProfileOverviewSearch>(deepClone(DEFAULT_PROFILE_OVERVIEW_SEARCH));

subscribe(profileOverviewSearch, (changes) => {
    changes.forEach((change) => {
        const [type, path] = change;

        if (type === 'set' && path.includes('filters')) {
            profileOverviewSearch.page = 0;
            ProfileOverviewService.debouncedSearch();
        }

        if (type === 'set' && path.join('.') === 'page') {
            ProfileOverviewService.debouncedSearch();
        }
    });
});

export { profileOverviewSearch };
``;
