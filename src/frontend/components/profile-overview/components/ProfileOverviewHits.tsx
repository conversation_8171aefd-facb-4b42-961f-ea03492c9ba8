import React from 'react';

import { CompanyProfile } from 'models';

import { Loader } from 'components/search/components/Loader';
import { GridSection } from 'components/section/GridSection';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

const ProfileOverviewHits = ({ profiles = [], isLoading }: { profiles: CompanyProfile[]; isLoading?: boolean }) => {
    if (isLoading) {
        return <Loader />;
    }

    return profiles.length === 0 ? (
        <EmptyMessage>No profiles found matching your criteria</EmptyMessage>
    ) : (
        <GridSection nbCols={3}>
            {profiles.map((company) => (
                <CompanyProfileTeaser key={company.id} company={company} />
            ))}
        </GridSection>
    );
};

export { ProfileOverviewHits };
