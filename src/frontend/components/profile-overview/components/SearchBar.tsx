import React from 'react';

import { useHotkeys } from 'react-hotkeys-hook';

import { SearchBox } from 'components/search-box';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

const SearchBar = () => {
    const {
        filters: { search },
        searchBarQuery,
    } = useProfileOverviewSearch();

    const submit = () => {
        ProfileOverviewService.setFilter('search', searchBarQuery);
    };

    const reset = () => {
        ProfileOverviewService.setSearchBarQuery(undefined);
        ProfileOverviewService.setFilter('search', undefined);
    };

    useHotkeys('enter', submit, {
        enableOnFormTags: ['input'],
    });

    return (
        <SearchBox
            size="lg"
            showSubmitHint
            value={searchBarQuery ?? ''}
            placeholder="Search by name"
            autoComplete="off"
            onChange={(event) => {
                ProfileOverviewService.setSearchBarQuery(event.target.value);
                ProfileOverviewService.setFilter('search', event.target.value);
            }}
            handleReset={search ? reset : undefined}
        />
    );
};

export { SearchBar };
