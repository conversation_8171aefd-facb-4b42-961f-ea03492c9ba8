import { Inter } from 'next/font/google';

import { createTheme } from '@mantine/core';

import { IoCaretBack, IoCaretForward } from 'react-icons/io5';

const inter = Inter({ subsets: ['latin'] });

import cx from './theme.module.scss';
import rteClasses from './rte.module.scss';

export const theme = createTheme({
    fontFamily: `${inter.style.fontFamily}, sans-serif`,
    primaryColor: 'gray',
    primaryShade: 7,
    cursorType: 'pointer',
    focusRing: 'always',
    white: '#ffffff',
    black: '#004258',
    colors: {
        // blue
        primary: [
            '#E3F9FF',
            '#C3F1FF',
            '#A3E9FC',
            '#6BE9FC', // base color
            '#58C8E4',
            '#46A9D1',
            '#3388B6',
            '#216B94',
            '#1A4F7A',
            '#103B5F',
        ],
        // purple
        brand: [
            '#D3D1F8',
            '#B5AEEF',
            '#9791E6',
            '#7A76DB',
            '#5E5AC8',
            '#4A47B3',
            '#38339E',
            '#2E2C97', // base color
            '#252586',
            '#1A1A74',
        ],
        brandOriginal: [
            '#D3D1F8',
            '#B5AEEF',
            '#9791E6',
            '#7A76DB',
            '#5E5AC8',
            '#4A47B3',
            '#38339E',
            '#2E2C97',
            '#252586',
            '#1A1A74',
        ],
        connection: Array(10).fill('#868E96') as any,
        AC: Array(10).fill('#D9480F') as any,
        DC: Array(10).fill('#1864AB') as any,
    },
    defaultGradient: {
        deg: 130,
        from: 'primary.4',
        to: 'brandOriginal.7',
    },
    fontSizes: {
        xs: '0.6875rem',
        sm: '0.75rem',
        md: '0.8125rem',
        lg: '1rem',
        xl: '1.125rem',
    },
    headings: {
        fontFamily: `${inter.style.fontFamily}, sans-serif`,
        fontWeight: '500',
        sizes: {
            h1: {
                fontSize: '1.125rem',
                fontWeight: '600',
            },
            h2: {
                fontSize: '1rem',
                fontWeight: '600',
            },
            h3: {
                fontSize: '0.8125rem',
                fontWeight: '500',
            },
            h4: {
                fontSize: '0.75rem',
                fontWeight: '500',
            },
        },
    },
    breakpoints: {
        xs: '30em',
        sm: '48em',
        md: '64em',
        lg: '74em',
        xl: '90em',
        xxl: '100em',
    },
    components: {
        Button: {
            classNames: {
                root: cx.button,
            },
        },
        Modal: {
            defaultProps: {
                closeButtonProps: {
                    size: 'sm',
                },
                zIndex: 204,
            },
            styles: {
                title: {
                    fontWeight: 600,
                    fontSize: 'var(--mantine-h1-font-size)',
                },
                body: {
                    paddingLeft: 'var(--mantine-spacing-lg)',
                    paddingRight: 'var(--mantine-spacing-lg)',
                    paddingBottom: 'var(--mantine-spacing-lg)',
                },
                header: {
                    paddingTop: 'var(--mantine-spacing-lg)',
                    paddingLeft: 'var(--mantine-spacing-lg)',
                    paddingRight: 'var(--mantine-spacing-lg)',
                },
            },
        },
        Anchor: {
            styles: {
                root: {
                    color: 'var(--mantine-color-primary-7)',
                },
            },
        },
        ButtonGroup: {
            classNames: {
                group: cx.buttonGroup,
            },
        },
        TextInput: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        Textarea: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        NumberInput: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        Select: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        MultiSelect: {
            classNames: {
                input: [cx.input, cx.pillsInput],
            },
        },
        Checkbox: {
            defaultProps: {
                color: 'primary',
            },
        },
        ColorInput: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        InputBase: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        Autocomplete: {
            classNames: {
                root: cx.inputWrapper,
            },
        },
        PillsInput: {
            classNames: {
                input: [cx.input, cx.pillsInput],
            },
        },
        TagsInput: {
            classNames: {
                input: [cx.input, cx.pillsInput],
            },
        },
        RichTextEditor: {
            classNames: rteClasses,
        },
        Carousel: {
            defaultProps: {
                nextControlIcon: <IoCaretForward />,
                previousControlIcon: <IoCaretBack />,
            },
        },
    },
});
