import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { DiagramChatChannel, DiagramChatChannelListParams } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { ChatService } from 'components/diagram/services/ChatService';

type UseDiagramChatChannels = (props: Partial<DiagramChatChannelListParams>) => SWRResponse & {
    diagramChatChannels: DiagramChatChannel[];
    totalPages: number;
    totalDocs: number;
};

const useDiagramChatChannels: UseDiagramChatChannels = (props) => {
    const fetcher = async () => {
        return ChatService.adminList(props);
    };

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/diagramChatChannels/admin-list?query=${JSON.stringify(props)}`,
            fetcher,
            condition: true,
        }),
    );

    return {
        ...swr,
        diagramChatChannels: swr?.data?.docs || [],
        totalPages: swr?.data?.totalPages || 1,
        totalDocs: swr?.data?.totalDocs || 0,
    };
};

export { useDiagramChatChannels };
