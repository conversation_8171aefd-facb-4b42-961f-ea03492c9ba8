import { Flex, Text, TextProps } from '@mantine/core';

import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';

const UpdateMessage = ({ message, ...props }: { message: string } & TextProps) => {
    const [baseMessage, details] = message.split(':::details:');

    return (
        <Flex gap="xs" align="baseline">
            <Text c="dimmed" ta="left" className="animation-pulse" {...props}>
                <AIMarkdown>{baseMessage}</AIMarkdown>
            </Text>
            {details ? (
                <Text c="dimmed" ta="left" className="animation-pulse" {...props} size="xs">
                    <AIMarkdown>{details}</AIMarkdown>
                </Text>
            ) : null}
        </Flex>
    );
};

export { UpdateMessage };
