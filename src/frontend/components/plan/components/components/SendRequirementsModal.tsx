import { Box, Loader, Modal, Stack } from '@mantine/core';
import { useState } from 'react';
import Markdown from 'react-markdown';

interface ModalProps {
    requirements: string | undefined;
    isOpen: boolean;
    onClose: () => void;
}

export const SendRequirementsModal: React.FC<ModalProps> = ({ requirements, isOpen, onClose }) => {
    const [email, setEmail] = useState<string>('');

    const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        setEmail('');
        onClose();
    };

    if (!isOpen) return null;

    return (
        <Modal opened onClose={onClose} title={'Share Requirements to Match With a Supplier'} size="xl">
            <Stack gap="md">
                {!requirements ? (
                    <Loader size="md" color="gray.5" my="xs" mx="auto" />
                ) : (
                    <Box>
                        <Markdown>{requirements}</Markdown>
                    </Box>
                )}
                <input
                    type="email"
                    value={email}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    style={{ width: '200px' }}
                />
                <div>
                    <button onClick={handleSubmit} disabled={!email}>
                        Submit
                    </button>
                </div>
            </Stack>
        </Modal>
    );
};
