import React from 'react';

import { ActionIcon, BoxProps, Group, Stack, Text, Tooltip, Transition } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { TbThumbDown, TbThumbDownFilled, TbThumbUp, TbThumbUpFilled } from 'react-icons/tb';

import { ComponentChatRating, SearchAgentMessage } from 'models';

import { useArticle } from 'hooks/use-article';
import { useComponent } from 'hooks/use-component';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { SearchAgentService } from 'services/AISearchAgentService';
import { MarkdownRefType, SearchAgentHelpers } from 'helpers/SearchAgentHelpers';

import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';
import { CarouselSection } from 'components/section/CarouselSection';

import { TeaserLoader } from 'components/loaders/TeaserLoader';
import { ComponentOverviewHit } from 'components/component-overview';
import { CaseStudyHit } from 'components/case-study-search/components/CaseStudyHit';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

import cx from '../Body.module.scss';

const NEXT_STEPS_MARKER = '=== NEXT STEPS ===';

const renderTeaser = (id: string, type: MarkdownRefType, isStreaming?: boolean) => {
    if (isStreaming) {
        return <TeaserLoader opacity={0.1} />;
    }

    switch (type) {
        case 'company':
            return <Profile id={id} />;
        case 'product':
            return <Product id={id} />;
        case 'case_study':
            return <CaseStudy id={id} />;
        default:
            return <TeaserLoader opacity={0.1} />;
    }
};

export const Message = ({
    message,
    isStreaming,
    ...props
}: { message: SearchAgentMessage; isStreaming?: boolean } & BoxProps) => {
    const { markdownText, allReferences } = SearchAgentHelpers.parseTextToMarkdownAndReferences(message.content ?? '');

    const [content, nextStepsText] = markdownText.includes(NEXT_STEPS_MARKER)
        ? markdownText.split(NEXT_STEPS_MARKER)
        : [markdownText, ''];

    const isStreamingContent = isStreaming && !nextStepsText;
    const isStreamingNextSteps = isStreaming && !!nextStepsText;

    return (
        <Stack className={cx.block} data-type={message.role} {...props}>
            <AIMarkdown id={message.id} isStreaming={isStreamingContent}>
                {content}
            </AIMarkdown>

            {(nextStepsText || !isStreaming) && allReferences.length > 0 && (
                <CarouselSection nbCols={2} cols={{ base: 1, xs: 1, sm: 2, md: 2, lg: 2 }}>
                    {allReferences.map(({ type, id }) => (
                        <React.Fragment key={id}>{renderTeaser(id, type)}</React.Fragment>
                    ))}
                </CarouselSection>
            )}

            {nextStepsText && (
                <AIMarkdown id={`next-steps-${message.id}`} isStreaming={isStreamingNextSteps}>
                    {nextStepsText}
                </AIMarkdown>
            )}

            {message.role === 'assistant' && !isStreaming && <Feedback message={message} />}
        </Stack>
    );
};

const Profile = ({ id }: { id: string }) => {
    const { company } = useCompanyProfile(id);

    if (!company) return <TeaserLoader opacity={0.1} />;

    return <CompanyProfileTeaser company={company} showIntercom isExternalLink />;
};

const Product = ({ id }: { id: string }) => {
    const { component } = useComponent(id);

    if (!component) return <TeaserLoader opacity={0.1} />;

    return <ComponentOverviewHit component={component} showBooth showIntercom showEmptyImage isExternalLink />;
};

const CaseStudy = ({ id }: { id: string }) => {
    const { article } = useArticle(id);

    if (!article) return <TeaserLoader opacity={0.1} />;

    return <CaseStudyHit caseStudy={article} isExternalLink withBorder={false} />;
};

const Feedback = ({ message }: { message: SearchAgentMessage }) => {
    const user = useCurrentUser();

    const [ratings, setRatings] = useLocalStorage<{
        [key: string]: ComponentChatRating;
    }>({
        key: SearchAgentService.ratings.key,
        defaultValue: {},
    });

    const currentRating = ratings[message.id];

    const isThumbUp = currentRating === ComponentChatRating.THUMBS_UP;
    const isThumbDown = currentRating === ComponentChatRating.THUMBS_DOWN;

    const handleRate = (rating: ComponentChatRating) => {
        SearchAgentService.ratings.track(message, rating);

        setRatings((prev) => ({ ...prev, [message.id]: rating }));
    };

    return (
        <Group gap={2} wrap="nowrap" align="start">
            <Tooltip label="This answer is good">
                <ActionIcon
                    variant="subtle"
                    size="sm"
                    color="gray.6"
                    onClick={() => handleRate(ComponentChatRating.THUMBS_UP)}
                >
                    {isThumbUp ? <TbThumbUpFilled size={16} /> : <TbThumbUp size={16} strokeWidth={1.5} />}
                </ActionIcon>
            </Tooltip>
            <Tooltip label="This answer is bad">
                <ActionIcon
                    variant="subtle"
                    size="sm"
                    color="gray.6"
                    onClick={() => handleRate(ComponentChatRating.THUMBS_DOWN)}
                >
                    {isThumbDown ? <TbThumbDownFilled size={16} /> : <TbThumbDown size={16} strokeWidth={1.5} />}
                </ActionIcon>
            </Tooltip>
            <Transition mounted={isThumbDown && !user}>
                {(style) => (
                    <Text fz="sm" c="dimmed" ml="xs" style={style}>
                        Thank you for your feedback. Feel free to leave your email address so we can assist you further.
                    </Text>
                )}
            </Transition>
        </Group>
    );
};
