import { Button } from '@mantine/core';
import { BsPersonVcard } from 'react-icons/bs';
import { IoBookOutline } from 'react-icons/io5';

import Link from 'next/link';

import { RouterHelpers } from 'helpers/RouterHelpers';

export const Links = () => {
    return (
        <>
            <Button
                variant="transparent"
                component={Link}
                href={RouterHelpers.urls.searchTab('products')}
                leftSection={<IoBookOutline />}
            >
                Products
            </Button>
            <Button
                variant="transparent"
                component={Link}
                href={RouterHelpers.urls.searchTab('profiles')}
                leftSection={<BsPersonVcard />}
            >
                Profiles
            </Button>
        </>
    );
};
