import { Box, Button, Tooltip } from '@mantine/core';

import cx from './NextSteps.module.scss';

export type NextStep = {
    number: number;
    label: string;
};

const NextSteps = ({
    nextSteps,
    handleSubmit,
    mounted,
}: {
    nextSteps: NextStep[];
    handleSubmit: (step: string) => void;
    mounted: boolean;
}) => {
    return (
        <Box className={cx.root} data-mounted={mounted}>
            {nextSteps.map((step) => (
                <Tooltip key={step.number} label={step.label} position="top">
                    <Button
                        size="compact-xs"
                        variant="white"
                        onClick={() => handleSubmit(`#${step.number} ${step.label}`)}
                    >
                        {step.number}
                    </Button>
                </Tooltip>
            ))}
        </Box>
    );
};

export { NextSteps };
