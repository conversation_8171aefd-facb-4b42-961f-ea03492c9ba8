.container {
    position: relative;

    max-width: 800px;

    width: 100%;

    flex: 1;

    &::before {
        --height: 20px;

        content: '';

        position: absolute;
        right: 0;
        left: 0;
        top: calc(var(--height) * -1);

        height: var(--height);

        background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.2));
    }
}

.containerInner {
    position: relative;
    z-index: 2;

    display: flex;
    flex-direction: column;

    --radius: var(--mantine-radius-lg);
    --shadow: 0 0 20px var(--mantine-color-brand-4);
    --background-color: rgba(255, 255, 255, 1);

    border-radius: var(--radius);
    overflow: hidden;

    &:global(.animation-border-spin) {
        border-width: 4px;
        box-shadow: var(--shadow);

        --background-color: var(--mantine-color-primary-4);
        --animation-color: var(--mantine-color-brand-7);
    }
}

.controls {
    align-items: end;

    background-color: white;

    padding: 6px;

    :global([data-thread-history]),
    :global(.mantine-Button-root) {
        border-radius: calc(var(--radius) - 6px);
    }
}

.submitButton {
    border-radius: var(--mantine-radius-sm);
    border-bottom-right-radius: calc(var(--radius) - 6px);

    transition:
        background-color 0.2s ease-in-out,
        color 0.2s ease-in-out;
}

.requirementsButton {
    position: absolute;
    bottom: 6px;
    left: 110px;

    border-radius: calc(var(--radius) - 6px);

    transition:
        background-color 0.2s ease-in-out,
        color 0.2s ease-in-out;
}

.textarea {
    :global(.mantine-Input-input) {
        --input-font-size: 16px;

        padding: var(--mantine-spacing-md);
        border-radius: 0;

        width: 100%;
        height: 60px;

        background-color: white;
        border: none;

        transition: height 0.2s ease-in-out;

        [data-initial='true'] & {
            height: 100px;
        }
    }
}

.root {
    width: 100%;
}
