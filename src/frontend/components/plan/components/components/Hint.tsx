import { forwardRef } from 'react';

import { createPolymorphicComponent, Text, TextProps, Transition } from '@mantine/core';

interface HintProps extends TextProps {
    children?: React.ReactNode;
    hidden?: boolean;
}

// Create the polymorphic component with forwardRef
const _Hint = forwardRef<HTMLParagraphElement, HintProps>(
    (
        {
            children = (
                <>
                    Whether you’re working on a project, planning one, or just exploring,
                    <br />
                    the more you share, the better we can match you with exhibitors, products, and services.
                </>
            ),
            hidden = false,
            ...others
        },
        ref,
    ) => {
        return (
            <Transition mounted={!hidden} transition="fade" duration={300} timingFunction="ease">
                {(style) => (
                    <Text ref={ref} fz="sm" c="dimmed" ta="left" style={style} {...others} visibleFrom="sm">
                        {children}
                    </Text>
                )}
            </Transition>
        );
    },
);

// Set display name for better debugging
_Hint.displayName = 'Hint';

// Export the polymorphic component
export const Hint = createPolymorphicComponent<'p', HintProps>(_Hint);
