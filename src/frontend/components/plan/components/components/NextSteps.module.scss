.root {
    position: absolute;
    top: 0;
    transform: translateY(-100%);

    z-index: 1;

    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--mantine-spacing-xs);

    padding: var(--mantine-spacing-xs);
    margin: 0 var(--mantine-spacing-xl);
    border-radius: var(--mantine-spacing-md) var(--mantine-spacing-md) 0 0;

    background-color: var(--mantine-color-brand-9);

    transition: transform 500ms ease;
    transition-delay: 500ms;

    &[data-mounted='false'] {
        opacity: 0;
        pointer-events: none;

        transform: translateY(100%);
    }

    :global(.mantine-Button-root) {
        padding: 0 var(--mantine-spacing-xs);

        &:disabled {
            opacity: 0.5;
            background-color: rgba(255, 255, 255, 0.1);
        }
    }
}
