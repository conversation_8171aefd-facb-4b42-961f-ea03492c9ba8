import { useEffect } from 'react';
import { UserReferrer } from 'models';

import { useLocalEvent } from 'hooks/use-local-event';
import { useActiveEvents } from 'hooks/use-active-events';
import { useLocalUserInfo } from 'hooks/use-local-user-info';

const useSetActiveEventOnMount = () => {
    const { localEvent, setLocalEvent } = useLocalEvent();
    const { referrer } = useLocalUserInfo();

    const { events } = useActiveEvents();

    useEffect(() => {
        if (!localEvent && referrer === UserReferrer.REPLUS && events.length) {
            const futureEvents = events
                .filter((event) => new Date(event.end).getTime() >= new Date().getTime())
                .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());

            if (futureEvents.length) {
                setLocalEvent(futureEvents[0].id);
            }
        }
    }, [events, referrer]); // no dependency on localEvent
};

export { useSetActiveEventOnMount };
