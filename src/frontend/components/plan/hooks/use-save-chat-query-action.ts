import { useEffect } from 'react';
import { useRouter } from 'next/router';

import { SearchAgentService } from 'services/AISearchAgentService';

import { useSearchThread } from 'components/plan/hooks/use-search-thread';

const useSaveChatQueryAction = () => {
    const router = useRouter();
    const { create } = useSearchThread();

    useEffect(() => {
        const { action, id, ...updatedQuery } = router.query;

        if (action !== 'save-chat' || !id) {
            return;
        }

        SearchAgentService.updateCreatedBy(id as string);

        const newPath = {
            pathname: router.pathname,
            query: updatedQuery,
        };

        router.replace(newPath, undefined, { shallow: true }).then(() => {
            create();
        });
    }, [router.query, router]);

    return null;
};

export { useSaveChatQueryAction };
