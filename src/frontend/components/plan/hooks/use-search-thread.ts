import useSWRImmutable from 'swr/immutable';

import { useLocalStorage } from '@mantine/hooks';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { SearchAgentService } from 'services/AISearchAgentService';
import { useLocalEvent } from 'hooks/use-local-event';

export const useSearchThread = () => {
    const { localEvent } = useLocalEvent();

    const [searchThreadId, setSearchThreadId] = useLocalStorage<string | null>({
        key: SearchAgentService.activeThread.key,
        defaultValue: '',
    });

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: SearchAgentService.swr.searchThread(searchThreadId as string),
            fetcher: async () => {
                return SearchAgentService.get(searchThreadId! as string);
            },
            condition: !!searchThreadId,
        }),
    );

    const create = async () => {
        const newId = await SearchAgentService.create({
            event: localEvent?.id,
        });
        setSearchThreadId(newId);

        return newId;
    };

    return {
        searchThreadId: searchThreadId as string | undefined,
        create,
        setSearchThreadId,
        activeSearchThread: swr.data,
        ...swr,
    };
};
