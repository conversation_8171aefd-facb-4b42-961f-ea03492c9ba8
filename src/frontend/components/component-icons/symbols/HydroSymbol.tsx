import React, { FC } from 'react';

const HydroSymbol: FC = () => (
    <symbol id="icon-hydro" viewBox="0 0 30 30" fill="currentColor">
        <mask id="hydro-b" width="36" height="12" x="-3" y="18" maskUnits="userSpaceOnUse">
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M6 29.197c.883.51 1.907.803 3 .803a5.972 5.972 0 0 0 3-.803c.883.51 1.907.803 3 .803a5.972 5.972 0 0 0 3-.803c.883.51 1.907.803 3 .803a5.972 5.972 0 0 0 3-.803 6 6 0 1 0 0-10.395A5.972 5.972 0 0 0 21 18a5.972 5.972 0 0 0-3 .803A5.972 5.972 0 0 0 15 18a5.972 5.972 0 0 0-3 .803A5.972 5.972 0 0 0 9 18a5.973 5.973 0 0 0-3 .803 6 6 0 1 0 0 10.395Zm0-1.843A4.487 4.487 0 0 0 9 28.5a4.489 4.489 0 0 0 3-1.146 4.502 4.502 0 0 0 4.688.819A4.502 4.502 0 0 0 18 27.354a4.502 4.502 0 0 0 4.688.819A4.502 4.502 0 0 0 24 27.354a4.502 4.502 0 0 0 3 1.146 4.5 4.5 0 1 0-3-7.854 4.502 4.502 0 0 0-4.688-.819 4.502 4.502 0 0 0-1.312.819 4.502 4.502 0 0 0-4.688-.819 4.502 4.502 0 0 0-1.312.819 4.502 4.502 0 0 0-4.688-.819A4.5 4.5 0 0 0 6 20.646 4.486 4.486 0 0 0 3 19.5a4.5 4.5 0 1 0 1.688 8.673A4.503 4.503 0 0 0 6 27.354Z"
                clipRule="evenodd"
            />
        </mask>
        <g mask="url(#hydro-b)">
            <path fill="currentColor" d="M2.5 27h25v3h-25v-3Z" />
        </g>
        <path
            fill="currentColor"
            stroke="currentColor"
            d="M17 4.352v.376l.361.104a8.452 8.452 0 0 1 2.071.914l.318.194.277-.249c.607-.544 1.534-1.143 2.53-1.362.838-.185 1.72-.103 2.541.523L22.2 7.75l-.286.287.22.34A8.452 8.452 0 0 1 23.3 11.16l.076.344.35.044c.825.104 1.786.384 2.528.932.615.454 1.088 1.096 1.213 2.02h-4.09l-.088.39a8.436 8.436 0 0 1-.75 2.04l-.163.311.24.259c.547.592 1.17 1.527 1.407 2.54.2.854.127 1.756-.51 2.594l-2.775-2.775-.306-.306-.347.26a8.47 8.47 0 0 1-2.758 1.365l-.325.092-.036.335c-.089.827-.357 1.82-.906 2.593-.457.644-1.11 1.14-2.06 1.27v-4.016l-.417-.07a8.452 8.452 0 0 1-3.507-1.453l-.304-.216-.294.23c-.655.511-1.535.997-2.45 1.138-.76.117-1.553-.001-2.298-.569l2.766-2.766.268-.267-.185-.33a8.44 8.44 0 0 1-.88-2.309l-.075-.344-.35-.044c-.825-.104-1.786-.384-2.528-.932-.616-.454-1.088-1.096-1.213-2.02h4.09l.088-.39c.215-.95.59-1.839 1.094-2.639l.205-.324-.26-.282c-.548-.592-1.172-1.527-1.41-2.54-.2-.854-.126-1.757.512-2.595l3.003 3.004.282.282.338-.213a8.444 8.444 0 0 1 3.17-1.196l.364-.058.051-.366c.115-.82.404-1.758.95-2.477.452-.595 1.086-1.05 1.99-1.173v3.819Zm-3.067 8.993 1.897-2.85-.346 1.575-.133.607h1.419l-1.738 2.71.313-1.435.132-.607h-1.544ZM15 21a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z"
        />
    </symbol>
);

export { HydroSymbol };
