import React, { FC } from 'react';

const WindSymbol: FC = () => (
    <symbol id="icon-wind" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M15.629 11.1a2.001 2.001 0 0 1 1.345 2.222L26 18c-1.86-.112-5.84-.485-6.88-1.085-.796-.46-1.958-1.584-2.771-2.438a2 2 0 0 1-3.123-.553L4.25 19.665c1.027-1.555 3.34-4.815 4.38-5.415 1.031-.596 3.33-1.165 4.374-1.38a2.001 2.001 0 0 1 1.476-1.802L14 .5c.833 1.667 2.5 5.3 2.5 6.5 0 1.06-.52 2.978-.871 4.1ZM16 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"
            clipRule="evenodd"
        />
        <path fill="currentColor" d="M15 15h-.5L13 29.5h4L15.5 15H15Z" />
    </symbol>
);

export { WindSymbol };
