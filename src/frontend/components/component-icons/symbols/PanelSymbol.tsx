import React, { FC } from 'react';

const PanelSymbol: FC = () => (
    <symbol id="icon-panel" viewBox="0 0 30 30" fill="currentColor">
        <path fill="currentColor" d="M22 24H8v1.5h14V24ZM8 21h14v1.5H8V21Z" />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="m15 3 8.66 15.75H6.34L15 3Zm1.32 6-.782 3.341H17L13.811 17l.774-3.324H13L16.32 9Z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M4 1a1 1 0 0 1 1-1h20a1 1 0 0 1 1 1v28a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V1Zm1.5 27.5v-27h19v27h-19Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { PanelSymbol };
