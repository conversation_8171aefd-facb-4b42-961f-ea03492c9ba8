import React, { FC } from 'react';

export const MotorSymbol: FC = () => (
    <symbol id="icon-motor" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            d="M12.296 10.773H10.36V19.5h1.517v-6.008h.08l2.413 5.982h1.133l2.412-5.97h.081V19.5h1.517v-8.727H17.58l-2.591 6.324h-.103l-2.59-6.324Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M3.041 14C3.55 7.84 8.71 3 15 3s11.45 4.84 11.959 11H30v2h-3.041C26.45 22.16 21.29 27 15 27S3.55 22.16 3.041 16H0v-2h3.041ZM15 25.5C9.201 25.5 4.5 20.799 4.5 15S9.201 4.5 15 4.5 25.5 9.201 25.5 15 20.799 25.5 15 25.5Z"
            clipRule="evenodd"
        />
    </symbol>
);
