import React, { FC } from 'react';

const ContactorSymbol: FC = () => (
    <symbol id="icon-contactor" fill="currentColor" viewBox="0 0 30 30">
        <path fill="currentColor" d="M15.75 0v6.5H25V8H5V6.5h9.25V0h1.5Z" />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M8 12a3 3 0 1 1-2.905 3.75H0v-1.5h5.095A3.001 3.001 0 0 1 8 12Zm-1.5 3a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM22 12a3 3 0 0 1 2.905 2.25H30v1.5h-5.095A3.001 3.001 0 0 1 19 15a3 3 0 0 1 3-3Zm0 1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { ContactorSymbol };
