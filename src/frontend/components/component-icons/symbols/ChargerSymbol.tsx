import React, { FC } from 'react';

const ChargerSymbol: FC = () => (
    <symbol id="icon-charger" viewBox="0 0 30 30" fill="none">
        <path
            fill="currentColor"
            d="M2 0h1.5v3h3V0H8v3h1a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H5.5v14.5h21v-21H12V3h15a1 1 0 0 1 1 1v22a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V11H1a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h1V0Z"
        />
        <path
            fill="currentColor"
            d="M10.717 11.727V19h4.759v-1.104h-3.441V15.91h3.167v-1.104h-3.167v-1.974h3.412v-1.105h-4.73Zm8.94 5.725-1.892-5.725h-1.446L18.88 19h1.627l2.564-7.273H21.62l-1.89 5.725h-.074Z"
        />
    </symbol>
);

export { ChargerSymbol };
