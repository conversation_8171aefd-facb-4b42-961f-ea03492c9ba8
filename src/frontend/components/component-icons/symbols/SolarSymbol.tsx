import React, { FC } from 'react';

const SolarSymbol: FC = () => (
    <symbol id="icon-solar" viewBox="0 0 30.4 30.4" fill="currentColor">
        <path
            fill="currentColor"
            d="M15.75-.083v4.166h-1.5V-.083h1.5ZM5.306 4.545l-.76 1.293 3.72 2.189.76-1.293-3.72-2.189ZM20 11a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M5 17h20l5 13H0l5-13Zm-.547 5.6 1.577-4.1h5.195l-.954 4.1H4.454Zm-.577 1.5-1.692 4.4H8.9l1.023-4.4H3.876Zm7.587 0-1.023 4.4h9.434l-1.024-4.4h-7.387Zm8.927 0 1.024 4.4h6.402l-1.692-4.4H20.39Zm5.157-1.5H20.04l-.954-4.1h4.884l1.577 4.1Zm-7.047 0h-6.688l.953-4.1h4.781l.954 4.1Z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            d="m25.457 5.839-3.724 2.188-.76-1.294 3.724-2.188.76 1.294ZM2.917 12.25v1.5h4.166v-1.5H2.917Zm20 0v1.5h4.166v-1.5h-4.166Z"
        />
    </symbol>
);

export { SolarSymbol };
