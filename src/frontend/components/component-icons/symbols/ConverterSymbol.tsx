import React, { FC } from 'react';

const ConverterSymbol: FC = () => (
    <symbol id="icon-converter" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            d="M13.62 9.317h1.066a1.881 1.881 0 0 0-.198-.696 1.633 1.633 0 0 0-.416-.514 1.779 1.779 0 0 0-.592-.32 2.332 2.332 0 0 0-.733-.11 2.13 2.13 0 0 0-1.05.26 1.879 1.879 0 0 0-.744.76c-.183.334-.275.741-.275 1.221 0 .479.09.885.27 1.219.18.334.427.588.738.763.312.173.666.26 1.06.26.3 0 .566-.046.798-.137.233-.09.43-.213.592-.366.162-.155.289-.326.381-.514a1.62 1.62 0 0 0 .169-.57l-1.066-.007a.859.859 0 0 1-.1.283.72.72 0 0 1-.434.341c-.096.03-.202.045-.318.045a.973.973 0 0 1-.539-.147.97.97 0 0 1-.352-.441 1.885 1.885 0 0 1-.123-.729c0-.278.04-.515.121-.711a.992.992 0 0 1 .352-.45.954.954 0 0 1 .547-.156c.12 0 .228.018.326.052a.722.722 0 0 1 .433.369.924.924 0 0 1 .087.295Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M7.955 12.1H6.342V7.736h1.61c.445 0 .828.088 1.15.262.322.174.57.424.745.75.175.326.262.715.262 1.168 0 .455-.087.845-.262 1.172a1.791 1.791 0 0 1-.744.752c-.322.173-.705.26-1.148.26Zm-.558-.9h.517c.245 0 .451-.04.62-.123a.817.817 0 0 0 .386-.407c.088-.189.132-.44.132-.754s-.044-.564-.132-.75a.815.815 0 0 0-.39-.405 1.422 1.422 0 0 0-.63-.125h-.503V11.2Z"
            clipRule="evenodd"
        />
        <path
            fill="currentColor"
            d="M19.68 22.998v.763h-2.913v-.763h2.913Zm4.047-2.783H22.66a.924.924 0 0 0-.087-.296.72.72 0 0 0-.433-.369.987.987 0 0 0-.325-.05.954.954 0 0 0-.548.155.992.992 0 0 0-.352.45c-.08.195-.121.433-.121.711 0 .29.041.533.123.729a.971.971 0 0 0 .352.44.973.973 0 0 0 .54.148c.116 0 .221-.015.317-.045a.719.719 0 0 0 .435-.341.859.859 0 0 0 .1-.283l1.065.006a1.616 1.616 0 0 1-.169.571 1.83 1.83 0 0 1-.381.514c-.162.153-.36.275-.592.366a2.172 2.172 0 0 1-.797.137 2.15 2.15 0 0 1-1.061-.26 1.872 1.872 0 0 1-.738-.763c-.18-.334-.27-.74-.27-1.219 0-.48.091-.887.275-1.22.183-.335.43-.588.743-.762a2.13 2.13 0 0 1 1.05-.26c.265 0 .51.037.734.111a1.8 1.8 0 0 1 .592.32c.17.14.309.31.416.514.106.203.172.435.198.696Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M3 4a1 1 0 0 1 1-1h22a1 1 0 0 1 1 1v22a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4Zm1.5 20.453V4.5h19.925L4.5 24.453ZM5.575 25.5H25.5V5.547L5.575 25.5Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { ConverterSymbol };
