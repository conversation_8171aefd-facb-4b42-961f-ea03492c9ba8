import React, { FC } from 'react';

const LoadSymbol: FC = () => (
    <symbol id="icon-load" viewBox="0 0 30 30" fill="none">
        <path fill="currentColor" d="M10 2.75h10v2.5H10v-2.5Zm0 4h10v2.5H10v-2.5Z" />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M20 15h5.151c.882 0 1.332 1.058.721 1.693L15.721 27.25a1 1 0 0 1-1.442 0L4.128 16.693c-.611-.635-.16-1.693.72-1.693H10v-3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v3Zm-8.5 1.5v-4h7v4h5.477L15 25.836 6.023 16.5H11.5Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { LoadSymbol };
