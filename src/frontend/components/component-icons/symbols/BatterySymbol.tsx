import React, { <PERSON> } from 'react';

const BatterySymbol: FC = () => (
    <symbol id="icon-battery" viewBox="0 0 30 30" fill="currentColor">
        <path fill="currentColor" d="M14.172 14.012 15.151 9 11 16.014h1.981L12.014 21 16 14.012h-1.828Z" />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M28 8a1 1 0 0 0-1-1H1a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h26a1 1 0 0 0 1-1v-3h1a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1h-1V8Zm-1.5.5h-25v13h25v-13Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { BatterySymbol };
