import React, { <PERSON> } from 'react';

const CustomSymbol: FC = () => (
    <symbol id="icon-custom" viewBox="0 0 30 30">
        <path
            stroke="currentColor"
            strokeWidth={2}
            fill="none"
            fillRule="evenodd"
            d="M3 4a1 1 0 0 1 1-1h22a1 1 0 0 1 1 1v22a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { CustomSymbol };
