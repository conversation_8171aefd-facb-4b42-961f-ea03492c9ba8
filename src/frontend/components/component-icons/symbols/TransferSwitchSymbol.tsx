import React, { FC } from 'react';

export const TransferSwitchSymbol: FC = () => (
    <symbol id="icon-transferSwitch" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M15.75 4.14538C16.483 4.44207 17 5.16066 17 6C17 7.10457 16.1046 8 15 8C13.8954 8 13 7.10457 13 6C13 5.16066 13.517 4.44207 14.25 4.14538V0H15.75V4.14538ZM14.25 30L14.25 23V22.8461L14.3106 22.7046L21.8106 5.20456L23.1894 5.79544L15.75 23.1539L15.75 30H14.25ZM24 17C24.8393 17 25.5579 16.483 25.8546 15.75H30V14.25H25.8546C25.5579 13.517 24.8393 13 24 13C22.8954 13 22 13.8954 22 15C22 16.1046 22.8954 17 24 17ZM24 14.5C23.7239 14.5 23.5 14.7239 23.5 15C23.5 15.2761 23.7239 15.5 24 15.5C24.2761 15.5 24.5 15.2761 24.5 15C24.5 14.7239 24.2761 14.5 24 14.5ZM14.5 6C14.5 5.72386 14.7239 5.5 15 5.5C15.2761 5.5 15.5 5.72386 15.5 6C15.5 6.27614 15.2761 6.5 15 6.5C14.7239 6.5 14.5 6.27614 14.5 6Z"
            clipRule="evenodd"
        />
    </symbol>
);
