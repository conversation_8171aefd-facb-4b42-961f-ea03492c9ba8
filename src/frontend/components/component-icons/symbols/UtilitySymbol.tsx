import React, { FC } from 'react';

const UtilitySymbol: FC = () => (
    <symbol id="icon-utility" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M6.25.75v.974l-.64 3.628 1.477.26.593-3.362h2.09L7.187 14.94H2.25v.987l-.652 3.31 1.472.29.608-3.086h3.204L4.548 27.917a.735.735 0 0 0 .716.885.75.75 0 0 0 .71-.102l8.941-6.668 9.114 6.672a.75.75 0 0 0 .705.098.735.735 0 0 0 .715-.885l-2.334-11.476h3.207l.608 3.085 1.472-.29-.652-3.31v-.985h-4.94L20.23 2.25h2.09l.594 3.363 1.477-.26-.64-3.63V.75H6.25Zm17.394 25.813-1.95-9.586-5.52 4.117 7.47 5.47Zm-8.737-6.396 4.997-3.726H9.817l5.09 3.726Zm-6.642-3.003-1.907 9.378 7.291-5.437-5.383-3.941Zm3.46-14.914h6.243l-3.122 3.842-3.122-3.842Zm-.726 1.477 2.884 3.55-4.81 5.921 1.926-9.47Zm3.847 4.736L9.583 14.94H20.11l-5.264-6.478Zm4.092-5.036-3.129 3.85 5.218 6.422-2.09-10.272Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { UtilitySymbol };
