import React, { FC } from 'react';

const HVACSymbol: FC = () => (
    <symbol id="icon-hvac" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            stroke="currentColor"
            d="M2 23.5v.5h26V4H2v19.5Zm11.22-3.555c.08.102.278.35.679.412.265.057.554.02.771-.025a2.82 2.82 0 0 0 .613-.204c1.723-.728 2.885-2.057 3.01-3.712v-.015c.067-1.483-.52-2.366-1.156-2.861-.797-.62-1.847-.848-2.925-.706A2.509 2.509 0 0 0 12.98 11.7c.45-.891 1.167-1.794 1.725-2.252a.793.793 0 0 0 .392-.643l-.499-.03.5.03v-.003c.007-.13.026-.446-.231-.76-.159-.219-.399-.385-.591-.496a2.794 2.794 0 0 0-.592-.257c-1.77-.608-3.521-.394-4.719.755l-.005.005-.005.005c-1.04 1.059-1.193 2.11-1.05 2.903.177.978.79 1.837 1.666 2.45a2.53 2.53 0 0 0-.07.504c-.98-.152-2.052-.55-2.658-.935a.793.793 0 0 0-.733-.173l-.003.001c-.125.033-.433.114-.65.456-.16.219-.242.499-.287.716a2.793 2.793 0 0 0-.06.642c-.028 1.871.72 3.47 2.185 4.25l.007.003.007.004c1.33.658 2.375.476 3.085.093.913-.492 1.553-1.396 1.847-2.48.214-.02.42-.067.614-.138.266.967.337 2.132.226 2.852a.793.793 0 0 0 .137.74l.002.003Zm-6.144-4.191a4.87 4.87 0 0 1-.117-.604c.975.39 2.157.68 3.165.715.093.095.19.18.294.256-.17.62-.508 1.076-.878 1.272-.37.166-.796.183-1.348-.09a2.547 2.547 0 0 1-1.116-1.55Zm4.917-6.95c.196.012.404.038.61.077-.675.806-1.318 1.838-1.665 2.786a2.28 2.28 0 0 0-.335.2c-.536-.356-.864-.819-.935-1.231-.043-.403.074-.813.506-1.253a2.547 2.547 0 0 1 1.819-.579Zm2.411 6.232a2.46 2.46 0 0 0 .117-.372c.636-.094 1.189.032 1.517.291.301.271.488.655.46 1.27-.1.63-.4 1.195-.968 1.646a4.89 4.89 0 0 1-.505.35c-.036-1.05-.246-2.248-.62-3.185ZM1.5 4a.5.5 0 0 1 .5-.5h26a.5.5 0 0 1 .5.5v20a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5V4Zm19 17.5v-1h5v1h-5Zm-1-7.5a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Zm-7 0a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"
        />
    </symbol>
);

export { HVACSymbol };
