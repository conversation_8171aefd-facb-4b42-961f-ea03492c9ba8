import React, { FC } from 'react';

export const TransformerSymbol: FC = () => (
    <symbol id="icon-transformer" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M15 21.93a8 8 0 0 1-11.965-6.18H0v-1.5h3.035A8 8 0 0 1 15 8.07a8 8 0 0 1 11.965 6.18H30v1.5h-3.035A8 8 0 0 1 15 21.93Zm-4-.43a6.5 6.5 0 1 1 2.642-12.44A7.98 7.98 0 0 0 11 15a7.98 7.98 0 0 0 2.642 5.94c-.807.36-1.701.56-2.642.56Zm5.358-12.44a6.5 6.5 0 1 1 0 11.881A7.98 7.98 0 0 0 19 15a7.98 7.98 0 0 0-2.642-5.94ZM15 9.875A6.489 6.489 0 0 1 17.5 15a6.489 6.489 0 0 1-2.5 5.124A6.489 6.489 0 0 1 12.5 15 6.49 6.49 0 0 1 15 9.876Z"
            clipRule="evenodd"
        />
    </symbol>
);
