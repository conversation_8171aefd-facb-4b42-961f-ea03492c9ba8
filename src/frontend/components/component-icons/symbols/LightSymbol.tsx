import React, { FC } from 'react';

const LightSymbol: FC = () => (
    <symbol id="icon-light" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            d="M5 6.25h20v1.5H5v-1.5Zm-3 4h26v1.5H2v-1.5Zm3.078 5.318-.964-1.149-3.192 2.678.964 1.15 3.192-2.679Zm2.836 2.652-2.678 3.19-1.15-.964 2.679-3.192 1.149.964Zm3.526 1.412-1.409-.513-1.425 3.915 1.41.513 1.425-3.915Zm4.357.618v4.167h-1.5V20.25h1.5Zm4.242-1.131-1.41.513 1.425 3.915 1.41-.513-1.425-3.915Zm3.243-1.864 2.678 3.192-1.149.964-2.678-3.192 1.149-.964Zm2.651-2.835-.965 1.149 3.192 2.678.965-1.15-3.192-2.678Z"
        />
    </symbol>
);

export { LightSymbol };
