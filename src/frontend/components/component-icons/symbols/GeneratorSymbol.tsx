import React, { FC } from 'react';

const GeneratorSymbol: FC = () => (
    <symbol id="icon-generator" viewBox="0 0 30 30" fill="currentColor">
        <path
            fill="currentColor"
            d="M16.995 12.942c.125.181.223.387.294.618h1.61a3.244 3.244 0 0 0-.412-1.177 3.326 3.326 0 0 0-.806-.92 3.58 3.58 0 0 0-1.112-.597 4.167 4.167 0 0 0-1.35-.213c-.572 0-1.1.103-1.586.307a3.59 3.59 0 0 0-1.27.886 4.11 4.11 0 0 0-.844 1.411c-.199.554-.298 1.183-.298 1.888 0 .92.169 1.716.507 2.386a3.745 3.745 0 0 0 1.42 1.547c.607.36 1.313.541 2.117.541.722 0 1.36-.146 1.918-.439a3.26 3.26 0 0 0 1.312-1.261c.318-.546.477-1.192.477-1.939v-1.048h-3.54v1.236h2.01a2.21 2.21 0 0 1-.264 1.052 1.801 1.801 0 0 1-.754.725c-.324.17-.707.255-1.15.255-.49 0-.918-.118-1.287-.354-.37-.238-.658-.586-.866-1.044-.207-.457-.31-1.015-.31-1.674 0-.654.103-1.208.31-1.662.21-.458.5-.804.866-1.04a2.265 2.265 0 0 1 1.252-.354c.262 0 .503.034.725.103.221.065.417.163.588.294a1.9 1.9 0 0 1 .443.473Z"
        />
        <path
            fill="currentColor"
            fillRule="evenodd"
            d="M3.023 14.25C3.41 7.972 8.624 3 15 3c6.375 0 11.59 4.972 11.977 11.25H30v1.5h-3.023C26.59 22.028 21.375 27 15 27 8.624 27 3.41 22.028 3.023 15.75H0v-1.5h3.023ZM15 25.5C9.201 25.5 4.5 20.799 4.5 15S9.201 4.5 15 4.5 25.5 9.201 25.5 15 20.799 25.5 15 25.5Z"
            clipRule="evenodd"
        />
    </symbol>
);

export { GeneratorSymbol };
