import React, { FC } from 'react';

const CircuitBreakerSymbol: FC = () => (
    <symbol id="icon-breaker" fill="currentColor" viewBox="0 0 30 30">
        <path
            fill="currentColor"
            d="M6.774 15.536a.748.748 0 0 1-.524.214H0v-1.5h5.95L20.17.03l1.061 1.06L6.868 15.454a.905.905 0 0 1-.094.082Zm15.766-1.714-2.946-2.947-1.178 1.179L21.362 15l-2.946 2.946 1.178 1.179 2.947-2.947 2.946 2.947 1.179-1.179-2.197-2.196H30v-1.5h-5.53l2.196-2.196-1.179-1.179-2.946 2.947Z"
        />
    </symbol>
);

export { CircuitBreakerSymbol };
