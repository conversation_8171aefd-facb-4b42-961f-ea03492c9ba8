import {
    BatteryIcon,
    BusIcon,
    CapacitorIcon,
    ChargerIcon,
    CircuitBreakerIcon,
    ContactorIcon,
    ConverterIcon,
    CustomIcon,
    DisconnectIcon,
    FuseIcon,
    GeneratorIcon,
    GroundingIcon,
    HVACIcon,
    HydroIcon,
    LoadIcon,
    LightIcon,
    MeterIcon,
    MotorIcon,
    PanelIcon,
    SolarIcon,
    SolutionIcon,
    TransformerIcon,
    UtilityIcon,
    WindIcon,
} from 'components/component-icons/icons';
import { TextIcon } from './icons/TextIcon';

import { TransferSwitchIcon } from './icons/TransferSwitchIcon';

import { Component } from 'models';

const lookup: Record<Component['type'], any> = {
    battery: BatteryIcon,
    bus: BusIcon,
    breaker: CircuitBreakerIcon,
    capacitor: CapacitorIcon,
    charger: ChargerIcon,
    combinerBox: BusIcon,
    contactor: ContactorIcon,
    converter: ConverterIcon,
    custom: CustomIcon,
    disconnect: DisconnectIcon,
    fuse: FuseIcon,
    generator: GeneratorIcon,
    grounding: GroundingIcon,
    hvac: HVACIcon,
    hydro: HydroIcon,
    load: LoadIcon,
    light: LightIcon,
    meter: MeterIcon,
    motor: MotorIcon,
    other: TextIcon,
    panel: PanelIcon,
    powerDistributionUnit: BusIcon,
    rapidShutdownDevice: TextIcon,
    utility: UtilityIcon,
    solar: SolarIcon,
    solution: SolutionIcon,
    transferSwitch: TransferSwitchIcon,
    transformer: TransformerIcon,
    wind: WindIcon,
    cable: BusIcon,
} as const;

const getComponentIcon = (type: keyof typeof lookup) => {
    return lookup[type];
};

export { getComponentIcon };
