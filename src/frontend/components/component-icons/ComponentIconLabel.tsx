import React, { <PERSON> } from 'react';

import { Box } from '@mantine/core';
import { BsBox } from 'react-icons/bs';

import { DiagramComponentInstance } from 'models';

const ComponentIconLabel: FC<{
    componentInstance: Pick<DiagramComponentInstance, 'label' | 'rowSpan' | 'colSpan'>;
}> = ({ componentInstance }) => {
    const { label, rowSpan, colSpan } = componentInstance;

    if (!label) {
        return null;
    }

    const rotate = rowSpan > 1 && colSpan === 1;

    return (
        <Box
            data-component-icon-label
            style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',

                width: '100%',
                height: '100%',
            }}
        >
            <Box
                data-component-icon-label-text
                style={{
                    textAlign: 'center',
                    transform: rotate ? 'rotate(-90deg)' : 'none',

                    fontSize: 12,
                    fontWeight: 600,
                }}
            >
                {label}
            </Box>
            <Box
                data-component-icon-label-icon
                style={{
                    display: 'none',
                    justifyContent: 'center',
                    alignItems: 'center',

                    width: '100%',
                    height: '100%',
                }}
            >
                <BsBox />
            </Box>
        </Box>
    );
};

export { ComponentIconLabel };
