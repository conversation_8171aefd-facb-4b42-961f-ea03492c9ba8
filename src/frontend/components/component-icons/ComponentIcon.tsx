import React, { FC } from 'react';

import { getComponentIcon } from './get-component-icon';

import { Component, ComponentTeaser, DiagramComponentInstance, DiagramConnection } from 'models';
import { PortFilter } from 'models';

const ComponentIcon: FC<{
    type: Component['type'];
    component?: Component | ComponentTeaser;
    componentInstance?: DiagramComponentInstance;
    connections?: DiagramConnection[];
    color?: string;
    resizing?: boolean;
    ports?: PortFilter[];
}> = ({
    type,
    component = undefined,
    componentInstance = undefined,
    connections = undefined,
    color = 'currentColor',
    resizing,
    ports,
}) => {
    const solutionComponentReplacement =
        type === 'solution' &&
        componentInstance?.componentType === 'solution' &&
        'baseComponents' in componentInstance &&
        Array.isArray(componentInstance.baseComponents) &&
        componentInstance.baseComponents.length === 1
            ? componentInstance.baseComponents[0]
            : null;

    const Icon = getComponentIcon(solutionComponentReplacement ?? type);

    return Icon ? (
        <Icon
            type={type}
            componentInstance={componentInstance}
            component={component}
            color={color}
            connections={connections}
            resizing={resizing}
            ports={ports}
        />
    ) : null;
};

export { ComponentIcon };
