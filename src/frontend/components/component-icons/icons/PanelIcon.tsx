import React, { <PERSON> } from 'react';

import { PanelSymbol } from '../symbols';

import { ComponentIconLabel } from '../ComponentIconLabel';

import { DiagramComponentInstance } from 'models';

const PanelIcon: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) =>
    componentInstance?.label ? (
        <ComponentIconLabel componentInstance={componentInstance} />
    ) : (
        <svg viewBox="0 0 30 30">
            <defs>
                <PanelSymbol />
            </defs>
            <use href="#icon-panel" width={30} height={30} />
        </svg>
    );

export { PanelIcon };
