import React, { FC } from 'react';

import { Edge, VoltageType } from 'models';
import {
    BOTTOM,
    ISOLATED,
    LEFT,
    LEFT_ISO,
    LEFT_PORT,
    OVER_ISO,
    OVER_PORT,
    PortConnection,
    PortConnections,
    RIGHT,
    RIGHT_ISO,
    RIGHT_PORT,
    TOP,
} from './DiagramConverterIconUtils';

export const ICON_SIZE = 40;
export const ISO_OFFSET = 3;
export const TEXT_OFFSET = 1;
export const TEXT_WIDTH = 12;
export const TEXT_HEIGHT = 7;

export const textPositionConfig = {
    topLeft: {
        x: TEXT_OFFSET,
        y: TEXT_OFFSET,
    },
    topRight: {
        x: ICON_SIZE - TEXT_OFFSET - TEXT_WIDTH,
        y: TEXT_OFFSET,
    },
    topMiddle: {
        x: ICON_SIZE / 2 - TEXT_WIDTH / 2,
        y: TEXT_OFFSET,
    },
    bottomLeft: {
        x: TEXT_OFFSET,
        y: ICON_SIZE - TEXT_OFFSET - TEXT_HEIGHT,
    },
    bottomRight: {
        x: ICON_SIZE - TEXT_OFFSET - TEXT_WIDTH,
        y: ICON_SIZE - TEXT_OFFSET - TEXT_HEIGHT,
    },
    bottomMiddle: {
        x: ICON_SIZE / 2 - TEXT_WIDTH / 2,
        y: ICON_SIZE - TEXT_OFFSET - TEXT_HEIGHT,
    },
    leftMiddle: {
        x: TEXT_OFFSET,
        y: ICON_SIZE / 2 - TEXT_HEIGHT / 2,
    },
    rightMiddle: {
        x: ICON_SIZE - TEXT_OFFSET - TEXT_WIDTH,
        y: ICON_SIZE / 2 - TEXT_HEIGHT / 2,
    },
};

export type TextPosition = (typeof textPositionConfig)['topLeft'];

const TextAC = () => (
    <path d="M1.242 6.907H0L2.26.093h1.436L5.96 6.907H4.718L3.004 1.49h-.05L1.241 6.907Zm.04-2.672h3.386v.992H1.282v-.992ZM12 2.392h-1.173a1.61 1.61 0 0 0-.184-.542 1.372 1.372 0 0 0-.793-.655 1.69 1.69 0 0 0-.55-.087c-.348 0-.658.093-.927.28-.27.183-.481.454-.634.811-.152.355-.229.789-.229 1.301 0 .521.077.96.23 1.317.154.355.365.624.633.806.27.18.577.27.924.27.193 0 .372-.028.54-.08a1.446 1.446 0 0 0 .796-.636c.094-.155.159-.333.194-.533L12 4.651a2.794 2.794 0 0 1-.853 1.664 2.586 2.586 0 0 1-.821.505c-.314.12-.662.18-1.044.18a2.79 2.79 0 0 1-1.511-.416c-.444-.277-.793-.677-1.048-1.2-.255-.524-.382-1.152-.382-1.884 0-.734.128-1.362.386-1.883.257-.524.607-.924 1.05-1.201A2.778 2.778 0 0 1 9.282 0c.357 0 .69.053.997.16.307.106.58.263.821.469.24.204.438.454.593.752.157.295.259.632.307 1.011Z" />
);

const TextDC = () => (
    <path d="M2.195 6.907H0V.093h2.24c.643 0 1.195.137 1.657.41.464.27.82.66 1.07 1.167.248.508.373 1.116.373 1.823 0 .71-.126 1.32-.377 1.83a2.68 2.68 0 0 1-1.078 1.175c-.469.272-1.032.409-1.69.409ZM1.174 5.839h.964c.452 0 .828-.087 1.13-.26.301-.175.528-.435.68-.781.152-.349.228-.783.228-1.305 0-.52-.076-.953-.228-1.297a1.605 1.605 0 0 0-.674-.775c-.295-.173-.662-.26-1.1-.26h-1V5.84ZM12 2.392h-1.183a1.599 1.599 0 0 0-.187-.542 1.387 1.387 0 0 0-.8-.655 1.72 1.72 0 0 0-.554-.087c-.352 0-.664.093-.936.28-.272.183-.485.454-.639.811-.154.355-.23.789-.23 1.301 0 .521.076.96.23 1.317.156.355.37.624.64.806.271.18.582.27.932.27.194 0 .376-.028.544-.08.171-.056.324-.137.459-.244.137-.106.252-.237.345-.392.095-.155.16-.333.196-.533L12 4.651a2.774 2.774 0 0 1-.86 1.664 2.614 2.614 0 0 1-.83.505c-.316.12-.667.18-1.053.18-.569 0-1.077-.139-1.524-.416-.447-.277-.8-.677-1.057-1.2C6.42 4.86 6.29 4.231 6.29 3.5c0-.734.13-1.362.39-1.883.259-.524.612-.924 1.059-1.201A2.822 2.822 0 0 1 9.257 0c.361 0 .696.053 1.006.16.31.106.587.263.83.469.242.204.44.454.597.752A2.9 2.9 0 0 1 12 2.392Z" />
);

const TextACDC: FC<{ voltageType: VoltageType; position: TextPosition }> = ({ voltageType, position }) => (
    <g transform={`translate(${position.x}, ${position.y})`} stroke="none" fill="var(--mantine-color-gray-7)">
        {voltageType === 'AC' && <TextAC />}
        {voltageType === 'DC' && <TextDC />}
    </g>
);

const IsoatedFullWidthLine = () => (
    <line
        x1={0 + ISO_OFFSET / 2}
        y1={ICON_SIZE + ISO_OFFSET / 2}
        x2={ICON_SIZE + ISO_OFFSET / 2}
        y2={ISO_OFFSET / 2}
        strokeWidth={1.2}
    />
);

const FullWidthLine = () => <line x1={0} y1={ICON_SIZE} x2={ICON_SIZE} y2={0} strokeWidth={1.2} />;

const IsolatedTriangle = () => (
    <>
        <line x1={ISO_OFFSET} y1={ICON_SIZE} x2={ICON_SIZE / 2} y2={ICON_SIZE / 2 + ISO_OFFSET} strokeWidth={1.2} />
        <line
            x1={ICON_SIZE - ISO_OFFSET}
            y1={ICON_SIZE}
            x2={ICON_SIZE / 2}
            y2={ICON_SIZE / 2 + ISO_OFFSET}
            strokeWidth={1.2}
        />
    </>
);

const Triangle = () => (
    <>
        <line x1={0} y1={ICON_SIZE} x2={ICON_SIZE / 2} y2={ICON_SIZE / 2} strokeWidth={1.2} />
        <line x1={ICON_SIZE} y1={ICON_SIZE} x2={ICON_SIZE / 2} y2={ICON_SIZE / 2} strokeWidth={1.2} />
    </>
);

const DiagramConverterIcon: FC<{ portConnections: PortConnections }> = ({ portConnections }) => {
    const getRotation = (def: number, isFullWidthLine?: boolean) => {
        let rotate = '0deg';

        if (def & LEFT) {
            rotate = '90deg';
        } else if (def & TOP) {
            rotate = '180deg';
        } else if (def & RIGHT) {
            rotate = '-90deg';
        }

        if (def & LEFT || def & RIGHT) {
            if (isFullWidthLine && def & RIGHT_PORT) {
                rotate = '0';
            }
        }

        if (def & TOP || def & BOTTOM) {
            if (isFullWidthLine && def & RIGHT_PORT) {
                rotate = '90deg';
            }
        }

        return rotate;
    };

    const getTextPosition = (def: number, isFullWidthLine?: boolean) => {
        let textAttrs = {} as TextPosition;

        if (def & BOTTOM) {
            textAttrs = isFullWidthLine ? textPositionConfig.bottomRight : textPositionConfig.bottomMiddle;
        } else if (def & LEFT) {
            textAttrs = isFullWidthLine ? textPositionConfig.bottomLeft : textPositionConfig.leftMiddle;
        } else if (def & TOP) {
            textAttrs = isFullWidthLine ? textPositionConfig.topLeft : textPositionConfig.topMiddle;
        } else if (def & RIGHT) {
            textAttrs = isFullWidthLine ? textPositionConfig.topRight : textPositionConfig.rightMiddle;
        }

        return textAttrs;
    };

    const drawLines = ({ def, voltageType }: { def: number; voltageType?: VoltageType | null }) => {
        let isFullWidthLine = true;
        let isIsolated = !!(def & ISOLATED);

        if (def & RIGHT_PORT && def & OVER_PORT) {
            isFullWidthLine = false;
        }
        if (def & LEFT_PORT && def & RIGHT_PORT) {
            isFullWidthLine = false;
        }

        if (def & LEFT_ISO && def & RIGHT_ISO) {
            isIsolated = true;
        }
        if (def & LEFT_ISO && def & OVER_ISO) {
            isIsolated = true;
        }
        if (def & RIGHT_ISO && def & OVER_ISO) {
            isIsolated = true;
        }

        const rotate = getRotation(def, isFullWidthLine);

        if (isFullWidthLine) {
            return (
                <g>
                    <g
                        style={{
                            transform: `rotate(${rotate})`,
                            transformOrigin: '50% 50%',
                        }}
                    >
                        {isIsolated ? <IsoatedFullWidthLine /> : <FullWidthLine />}
                    </g>
                    {voltageType && (
                        <TextACDC voltageType={voltageType} position={getTextPosition(def, isFullWidthLine)} />
                    )}
                </g>
            );
        } else {
            return (
                <g>
                    <g
                        style={{
                            transform: `rotate(${rotate})`,
                            transformOrigin: '50% 50%',
                        }}
                    >
                        {isIsolated ? <IsolatedTriangle /> : <Triangle />}
                    </g>
                    {voltageType && <TextACDC voltageType={voltageType} position={getTextPosition(def)} />}
                </g>
            );
        }
    };

    const drawSimpleLines = ({
        def,
        voltageType,
        showIsolated,
    }: {
        def: number;
        voltageType?: VoltageType | null;
        showIsolated?: boolean;
    }) => {
        const rotate =
            (def & LEFT && def & LEFT_PORT) ||
            (def & TOP && def & RIGHT_PORT) ||
            (def & BOTTOM && def & RIGHT_PORT) ||
            (def & RIGHT && def & LEFT_PORT);

        let textPosition = {} as TextPosition;

        if (rotate) {
            if (def & TOP) {
                textPosition = textPositionConfig.topRight;
            } else if (def & LEFT) {
                textPosition = textPositionConfig.bottomLeft;
            } else if (def & BOTTOM) {
                textPosition = textPositionConfig.bottomLeft;
            } else if (def & RIGHT) {
                textPosition = textPositionConfig.topRight;
            }
        } else {
            if (def & LEFT || def & TOP) {
                textPosition = textPositionConfig.topLeft;
            } else if (def & RIGHT || def & BOTTOM) {
                textPosition = textPositionConfig.bottomRight;
            }
        }

        const isIsolated = showIsolated && (def & ISOLATED || def & LEFT_ISO || def & RIGHT_ISO || def & OVER_ISO);

        return (
            <g>
                <g
                    style={{
                        transform: rotate ? 'rotate(90deg)' : 'none',
                        transformOrigin: '50% 50%',
                    }}
                >
                    {isIsolated ? (
                        <line
                            x1={0 + ISO_OFFSET / 2}
                            y1={ICON_SIZE + ISO_OFFSET / 2}
                            x2={ICON_SIZE + ISO_OFFSET / 2}
                            y2={ISO_OFFSET / 2}
                            strokeWidth={1.2}
                        />
                    ) : (
                        <line x1={0} y1={ICON_SIZE} x2={ICON_SIZE} y2={0} strokeWidth={1.2} />
                    )}
                </g>
                {voltageType && <TextACDC voltageType={voltageType} position={textPosition} />}
            </g>
        );
    };

    return (
        <>
            <g>
                <g strokeLinecap="round" stroke="var(--mantine-color-gray-5)">
                    {Object.keys(portConnections).map((key, index) => {
                        const connection = portConnections[key as Edge] as PortConnection;
                        let lineDef = connection.lineDef;

                        if (connection.leftPort !== undefined) {
                            lineDef = lineDef | LEFT_PORT;
                        }
                        if (connection.rightPort !== undefined) {
                            lineDef = lineDef | RIGHT_PORT;
                        }
                        if (connection.overPort !== undefined) {
                            lineDef = lineDef | OVER_PORT;
                        }

                        if (connection.isolated) {
                            lineDef = lineDef | ISOLATED;
                        }
                        if (connection.leftPort?.isolated) {
                            lineDef = lineDef | LEFT_ISO;
                        }
                        if (connection.rightPort?.isolated) {
                            lineDef = lineDef | RIGHT_ISO;
                        }
                        if (connection.overPort?.isolated) {
                            lineDef = lineDef | OVER_ISO;
                        }

                        if (Object.keys(portConnections).length < 3) {
                            return (
                                <React.Fragment key={key}>
                                    {drawSimpleLines({
                                        def: lineDef,
                                        voltageType: connection.voltageType,
                                        showIsolated: index === 1,
                                    })}
                                </React.Fragment>
                            );
                        }

                        return (
                            <React.Fragment key={key}>
                                {drawLines({ def: lineDef, voltageType: connection.voltageType })}
                            </React.Fragment>
                        );
                    })}
                    {!Object.keys(portConnections).length && drawLines({ def: BOTTOM })}
                </g>
            </g>
        </>
    );
};

export { DiagramConverterIcon };
