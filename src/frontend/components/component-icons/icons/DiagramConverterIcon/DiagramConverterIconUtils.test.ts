import { expect, test } from '@jest/globals';

import { DiagramComponentInstanceConverter, DiagramConnection } from 'models';
import { BOTTOM, LEFT, RIGHT, getPortConnections } from './DiagramConverterIconUtils';

test('getPortConnections should return the port connections for a component instance', () => {
    const componentInstance: DiagramComponentInstanceConverter = {
        id: 'component-1',
        specifications: {
            electrical: {
                // @ts-ignore
                ports: [{ isolated: false }, { isolated: true }, { isolated: false }],
            },
        },
        configuration: {
            // @ts-ignore
            ports: [{ voltageType: 'AC' }, { voltageType: 'AC' }, { voltageType: 'AC' }],
        },
    };

    // [component-2] (1) ---- (0) [component-1] (1) ---- (2) [component-3]
    //                                (2)
    //                                 |
    //                                (3)
    //                           [component-4]

    const connections: DiagramConnection[] = [
        {
            from: { componentInstanceId: 'component-1', port: 1, edge: 'right' },
            to: { componentInstanceId: 'component-3', port: 2, edge: 'left' },
        },
        {
            from: { componentInstanceId: 'component-1', port: 0, edge: 'left' },
            to: { componentInstanceId: 'component-2', port: 1, edge: 'right' },
        },
        {
            from: { componentInstanceId: 'component-4', port: 3, edge: 'top' },
            to: { componentInstanceId: 'component-1', port: 2, edge: 'bottom' },
        },
    ] as DiagramConnection[];

    const expectedPortConnections = {
        left: {
            port: 0,
            voltageType: 'AC',
            isolated: false,
            lineDef: LEFT,
            leftPort: undefined,
            rightPort: { port: 2, voltageType: 'AC', isolated: false, lineDef: 0 },
            overPort: { port: 1, voltageType: 'AC', isolated: true, lineDef: 0 },
        },
        right: {
            port: 1,
            voltageType: 'AC',
            isolated: true,
            lineDef: RIGHT,
            leftPort: { port: 2, voltageType: 'AC', isolated: false, lineDef: 0 },
            rightPort: undefined,
            overPort: { port: 0, voltageType: 'AC', isolated: false, lineDef: 0 },
        },
        bottom: {
            port: 2,
            voltageType: 'AC',
            isolated: false,
            lineDef: BOTTOM,
            leftPort: { port: 0, voltageType: 'AC', isolated: false, lineDef: 0 },
            rightPort: { port: 1, voltageType: 'AC', isolated: true, lineDef: 0 },
            overPort: undefined,
        },
    };

    const result = getPortConnections({ componentInstance, connections });
    expect(result).toEqual(expectedPortConnections);
});

test('getPortConnections should default isolated to false when the diagram is corrupted', () => {
    const onlyPort0 = [{ isolated: true }];
    const componentInstance: DiagramComponentInstanceConverter = {
        id: 'component-1',
        specifications: {
            electrical: {
                // @ts-ignore
                ports: onlyPort0,
            },
        },
        configuration: {
            // @ts-ignore
            ports: [{ voltageType: 'AC' }],
        },
    };

    // [component-1] --99--> [component-2]
    const connections: DiagramConnection[] = [
        {
            from: { componentInstanceId: 'component-1', port: 99, edge: 'left' },
            to: { componentInstanceId: 'component-2', port: 99, edge: 'right' },
        },
    ] as DiagramConnection[];

    const expectedPortConnections = {
        left: {
            port: 99,
            voltageType: undefined,
            isolated: false,
            lineDef: 1,
            leftPort: undefined,
            rightPort: undefined,
            overPort: undefined,
        },
    };

    const result = getPortConnections({ componentInstance, connections });
    expect(result).toEqual(expectedPortConnections);
});
