import { isNumber } from 'radash';
import { DiagramComponentInstanceConverter, DiagramConnection, Edge, PortFilter, VoltageType } from 'models';

export type PortConnection = {
    port: number;
    isolated: boolean;
    voltageType: VoltageType | null;
    leftPort?: PortConnection;
    rightPort?: PortConnection;
    overPort?: PortConnection;
    lineDef: number;
};

export type PortConnections = {
    [key in Edge]?: PortConnection;
};

export const LEFT = 1;
export const RIGHT = 2;
export const BOTTOM = 4;
export const TOP = 8;

export const LEFT_PORT = 16;
export const RIGHT_PORT = 32;
export const OVER_PORT = 64;

export const ISOLATED = 128;
export const LEFT_ISO = 256;
export const RIGHT_ISO = 512;
export const OVER_ISO = 1024;

export const getPortFilterConnections = (ports: PortFilter[]) => {
    const portConnections: PortConnections = {};

    if (ports.length >= 1) {
        portConnections['top'] = {
            voltageType: ports[0].voltageType,
            isolated: false,
            lineDef: 0,
            port: 1,
        };
    }

    if (ports.length >= 2) {
        portConnections['bottom'] = {
            voltageType: ports[1].voltageType,
            isolated: false,
            lineDef: 0,
            port: 2,
        };
    }

    if (ports.length >= 3) {
        portConnections['right'] = {
            voltageType: ports[2].voltageType,
            isolated: false,
            lineDef: 0,
            port: 3,
        };
    }

    return addAdjacentPorts(portConnections);
};

export const getPortConnections = ({
    componentInstance,
    connections,
}: {
    componentInstance: DiagramComponentInstanceConverter;
    connections: DiagramConnection[];
}) => {
    const portConnections: PortConnections = {};

    connections.forEach((connection) => {
        if (connection.from.componentInstanceId === componentInstance.id && isNumber(connection.from.port)) {
            portConnections[connection.from.edge] = {
                port: connection.from.port,
                voltageType: componentInstance.configuration.ports[connection.from.port]?.voltageType,
                isolated: !!componentInstance.specifications.electrical.ports[connection.from.port]?.isolated,
                lineDef: 0,
            };
        }

        if (connection.to.componentInstanceId === componentInstance.id && isNumber(connection.to.port)) {
            portConnections[connection.to.edge] = {
                port: connection.to.port,
                voltageType: componentInstance.configuration.ports[connection.to.port]?.voltageType,
                isolated: !!componentInstance.specifications.electrical.ports[connection.to.port]?.isolated,
                lineDef: 0,
            };
        }
    });

    const adjustedPorts = addAdjacentPorts(portConnections);
    return adjustedPorts;
};

const addAdjacentPorts = (portConnections: PortConnections) =>
    Object.keys(portConnections).reduce((result: PortConnections, key) => {
        let leftPort = portConnections.left;
        let rightPort = portConnections.right;
        let overPort = portConnections.top;
        const portKey = key as Edge;
        let lineDef = BOTTOM;

        if (portKey === 'left') {
            leftPort = portConnections.top;
            rightPort = portConnections.bottom;
            overPort = portConnections.right;
            lineDef = LEFT;
        } else if (portKey === 'top') {
            leftPort = portConnections.right;
            rightPort = portConnections.left;
            overPort = portConnections.bottom;
            lineDef = TOP;
        } else if (portKey === 'right') {
            leftPort = portConnections.bottom;
            rightPort = portConnections.top;
            overPort = portConnections.left;
            lineDef = RIGHT;
        }

        result[portKey] = {
            ...portConnections[portKey],
            leftPort,
            rightPort,
            overPort,
            lineDef,
        } as PortConnection;

        return result;
    }, {});
