import React, { <PERSON> } from 'react';

import { ComponentIconLabel } from '../ComponentIconLabel';

import { ComponentType, DiagramComponentInstance, getComponentDefinition } from 'models';

const TextIcon: FC<{ componentInstance: DiagramComponentInstance | undefined; type: ComponentType }> = ({
    componentInstance,
    type,
}) => {
    const { indicator } = getComponentDefinition(type);

    if (!indicator) {
        return null;
    }

    return (
        <ComponentIconLabel
            componentInstance={{
                rowSpan: componentInstance?.rowSpan ?? 1,
                colSpan: componentInstance?.colSpan ?? 1,
                label: indicator,
            }}
        />
    );
};

export { TextIcon };
