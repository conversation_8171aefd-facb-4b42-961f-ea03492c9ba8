import React, { <PERSON> } from 'react';

import { CustomSymbol } from '../symbols';

import { ComponentIconLabel } from '../ComponentIconLabel';

import { DiagramComponentInstance } from 'models';

const CustomIcon: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) =>
    componentInstance?.label ? (
        <ComponentIconLabel componentInstance={componentInstance} />
    ) : (
        <svg viewBox="0 0 30 30">
            <defs>
                <CustomSymbol />
            </defs>
            <use href="#icon-custom" width={30} height={30} />
        </svg>
    );

export { CustomIcon };
