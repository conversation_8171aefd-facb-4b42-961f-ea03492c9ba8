import React, { <PERSON> } from 'react';

import { BusSymbol } from 'components/component-icons/symbols';

import { ComponentIconLabel } from '../ComponentIconLabel';

import { DiagramComponentInstance } from 'models';

const BusIcon: FC<{
    color?: string;
    componentInstance: DiagramComponentInstance;
}> = ({ color = 'currentColor', componentInstance }) => {
    return componentInstance?.label ? (
        <ComponentIconLabel componentInstance={componentInstance} />
    ) : (
        <svg viewBox="0 0 30 30">
            <defs>
                <BusSymbol />
            </defs>
            <use href="#icon-bus" width={30} height={30} color={color} />
        </svg>
    );
};

export { BusIcon };
