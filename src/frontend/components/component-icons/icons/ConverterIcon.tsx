import React, { FC } from 'react';

import { ConverterSymbol } from '../symbols';

import { DiagramComponentInstanceConverter, DiagramConnection } from 'models';

import { getDiagramConverterIcon } from 'components/diagram/hooks';
import { PortFilter } from 'models';
import { DiagramConverterIcon, getPortConnections, getPortFilterConnections } from './DiagramConverterIcon';

const ConverterIcon: FC<{
    componentInstance?: DiagramComponentInstanceConverter;
    connections?: DiagramConnection[];
    resizing?: boolean;
    ports?: PortFilter[];
}> = ({ componentInstance, connections, resizing, ports }) => {
    const { isSmallConverter } = getDiagramConverterIcon(componentInstance);

    const staticIcon = (
        <svg viewBox="0 0 30 30">
            <defs>
                <ConverterSymbol />
            </defs>
            <use href="#icon-converter" width={30} height={30} />
        </svg>
    );

    if (ports && ports.length > 0) {
        const portConnections = getPortFilterConnections(ports);

        return (
            <svg
                viewBox="0 0 40 40"
                style={{
                    outlineOffset: '2px',
                    outline: '2px solid transparent',
                    border: '2px solid var(--mantine-color-gray-5)',
                    borderRadius: 'var(--mantine-radius-sm)',
                }}
            >
                <DiagramConverterIcon portConnections={portConnections} />
            </svg>
        );
    }

    if (!componentInstance || !connections?.length || !isSmallConverter) {
        return staticIcon;
    }

    if (resizing) return null;

    const portConnections = getPortConnections({ componentInstance, connections });

    return (
        <svg viewBox="0 0 40 40 ">
            <DiagramConverterIcon portConnections={portConnections} />;
        </svg>
    );
};

export { ConverterIcon };
