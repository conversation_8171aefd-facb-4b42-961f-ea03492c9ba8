import React, { FC } from 'react';

import { SolutionSymbol } from '../symbols';

import { ComponentIconLabel } from '../ComponentIconLabel';

import { DiagramComponentInstance } from 'models';

const SolutionIcon: FC<{
    componentInstance?: DiagramComponentInstance;
}> = ({ componentInstance }) =>
    componentInstance?.label ? (
        <ComponentIconLabel componentInstance={componentInstance} />
    ) : (
        <svg viewBox="0 0 30 30">
            <defs>
                <SolutionSymbol />
            </defs>
            <use href="#icon-solution" width={30} height={30} />
        </svg>
    );

export { SolutionIcon };
