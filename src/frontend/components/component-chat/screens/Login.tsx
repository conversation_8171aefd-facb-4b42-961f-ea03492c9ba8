import { useRouter } from 'next/router';

import { Button } from '@mantine/core';

import { ModalService } from 'services/ModalService';
import { ComponentChatPromo } from 'components/component-chat/components/ComponentChatPromo';

const Login = () => {
    const router = useRouter();

    const openLoginModal = () => {
        ModalService.openLoginModal({
            title: 'Register or login to unlock AI',
            redirect: `/${router.asPath}?action=chat`,
        });
    };

    return (
        <ComponentChatPromo>
            <Button
                fullWidth
                size="lg"
                color="brand"
                fz="lg"
                variant="gradient"
                gradient={{ from: 'blue', to: 'ai', deg: 90 }}
                onClick={openLoginModal}
            >
                Login to unlock AI
            </Button>
        </ComponentChatPromo>
    );
};

export { Login };
