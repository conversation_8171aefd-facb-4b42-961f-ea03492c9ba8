import { Button } from '@mantine/core';
import { ComponentChatPromo } from 'components/component-chat/components/ComponentChatPromo';

const AccessGranted = () => {
    return (
        <ComponentChatPromo showDemo={true}>
            <Button
                fullWidth
                size="lg"
                color="brand"
                fz="lg"
                variant="gradient"
                gradient={{ from: 'blue', to: 'ai', deg: 90 }}
                style={{ pointerEvents: 'none', opacity: 0.5 }}
            >
                Your access has been granted
            </Button>
        </ComponentChatPromo>
    );
};

export { AccessGranted };
