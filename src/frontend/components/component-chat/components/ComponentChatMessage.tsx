import { FC, useState } from 'react';

import { ActionIcon, Flex, Stack, Text, UnstyledButton } from '@mantine/core';
import { TbMinus, TbPlus } from 'react-icons/tb';

import { ComponentChatAnswer } from 'components/component-chat/components/ComponentChatAnswer';

import classes from './styles.module.scss';
import { ComponentChatMessage as ComponentChatMessageType } from 'models';

const ComponentChatMessage: FC<ComponentChatMessageType & { children?: React.ReactNode }> = ({
    question,
    answer,
    isHistory,
    children,
    ...rest
}) => {
    const [open, setOpen] = useState(!isHistory);

    return (
        <Flex gap="xs" px="xs" pt="xs">
            {isHistory && (
                <ActionIcon size="xs" color="brand" variant="light" mt={2} onClick={() => setOpen(!open)}>
                    {open ? <TbMinus size={10} /> : <TbPlus size={10} />}
                </ActionIcon>
            )}

            <Stack gap={4}>
                {isHistory ? (
                    <UnstyledButton
                        className={classes.questionButton}
                        onClick={isHistory ? () => setOpen(!open) : undefined}
                    >
                        {question}
                    </UnstyledButton>
                ) : (
                    <Text fw={600}>{question}</Text>
                )}

                {open && answer && <ComponentChatAnswer answer={answer} {...rest} />}

                {children}
            </Stack>
        </Flex>
    );
};

export { ComponentChatMessage };
