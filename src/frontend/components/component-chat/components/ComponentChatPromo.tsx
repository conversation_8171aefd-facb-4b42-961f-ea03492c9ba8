import React, { FC, useState } from 'react';

import { Card, Image, SegmentedControl, Stack, Text, ThemeIcon } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

const aiGradient = { from: 'blue', to: 'ai', deg: 90 };

const ComponentChatPromo: FC<{
    showDemo?: boolean;
    children: React.ReactNode;
}> = ({ showDemo, children }) => {
    const designer = 'Designer';
    const datasheet = 'Datasheet';
    const [imageSrc, setImageSrc] = useState(`/images/ai-gif-${designer}.gif`);

    return (
        <Stack pb="xs" pt="xs" py="xl" px="md" align="center" ta="center">
            <Stack align="center" gap={0}>
                <ThemeIcon variant="gradient" gradient={aiGradient} size="xl" radius={99}>
                    <BsRobot size={20} />
                </ThemeIcon>
                <Text fw={800} fz={24} variant="gradient" gradient={aiGradient}>
                    Complex docs made easy
                </Text>
                <Text fw={800} fz={24} variant="gradient" gradient={aiGradient}>
                    AI-powered answers
                </Text>
            </Stack>
            <Text fz="md" fw={600} maw={600}>
                Our AI-driven document processor instantly answers all your questions about Microgrid Components,
                streamlining your research and decision-making process.
            </Text>
            <SegmentedControl
                size="xs"
                onChange={(value) => {
                    if (value === datasheet) setImageSrc(`/images/ai-gif-${datasheet}.gif`);
                    if (value === designer) setImageSrc(`/images/ai-gif-${designer}.gif`);
                }}
                data={[designer, datasheet]}
            />
            {showDemo && (
                <Card shadow="xl" p={0}>
                    <Image src={imageSrc} alt="AI" h={450} style={{ transform: 'scale(1.01)' }} />
                </Card>
            )}
            {children}
        </Stack>
    );
};

export { ComponentChatPromo };
