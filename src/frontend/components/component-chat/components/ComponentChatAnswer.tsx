import React, { FC } from 'react';

import { ActionIcon, Group, Stack, Tooltip } from '@mantine/core';
import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';
import { TbMessage, TbThumbDown, TbThumbUp } from 'react-icons/tb';
import { IoTrashOutline } from 'react-icons/io5';

import { AIService } from 'services/AIService';
import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';
import { ComponentChatMessage, ComponentChatRating } from 'models';
import { useHistory } from 'components/component-chat/hooks/useHistory';

const ComponentChatAnswer: FC<
    Omit<ComponentChatMessage, 'question' | 'answer'> & {
        answer?: string | React.ReactNode;
    }
> = ({ id, references = [], rating, isStreaming, answer }) => {
    return (
        <Stack gap={8} mb="xs">
            {typeof answer === 'string' ? (
                <AIMarkdown id={id ?? ''} references={references} isStreaming={isStreaming}>
                    {answer}
                </AIMarkdown>
            ) : (
                answer
            )}
            {id && <FeedbackButtons id={id} rating={rating} />}
        </Stack>
    );
};

const FeedbackButtons: FC<Pick<ComponentChatMessage, 'rating'> & { id: string }> = ({ id, rating: currentRating }) => {
    const { changeMessageRating, chatComponentId, chatProjectId, deleteMessage } = useComponentChat();
    const { mutate } = useHistory(chatComponentId, chatProjectId);

    const handleRating = async (rating: ComponentChatRating) => {
        try {
            await AIService.leaveFeedback(id, rating);

            changeMessageRating(id, rating);

            await AIService.updateMessageRating(id, rating);
            await mutate();
        } catch {
            // error
        }
    };

    const handleDelete = async () => {
        deleteMessage(id);
        await AIService.deleteMessage(id);
        mutate();
    };

    const isThumbUp = currentRating === ComponentChatRating.THUMBS_UP;
    const isThumbDown = currentRating === ComponentChatRating.THUMBS_DOWN;

    return (
        <Group gap={2}>
            <Tooltip label="This answer is good">
                <ActionIcon
                    variant={isThumbUp ? 'light' : 'subtle'}
                    size="sm"
                    color={isThumbUp ? 'green.5' : 'gray.6'}
                    onClick={() => handleRating(ComponentChatRating.THUMBS_UP)}
                >
                    <TbThumbUp size={16} strokeWidth={1.5} />
                </ActionIcon>
            </Tooltip>
            <Tooltip label="This answer is bad">
                <ActionIcon
                    variant={isThumbDown ? 'light' : 'subtle'}
                    size="sm"
                    color={isThumbDown ? 'red.5' : 'gray.6'}
                    onClick={() => handleRating(ComponentChatRating.THUMBS_DOWN)}
                >
                    <TbThumbDown size={16} strokeWidth={1.5} />
                </ActionIcon>
            </Tooltip>
            <Tooltip label="Leave feedback">
                <ActionIcon
                    variant="subtle"
                    size="sm"
                    color="gray.6"
                    //@ts-ignore
                    onClick={() => window?.usersnapLogger('custom_button_click')}
                >
                    <TbMessage size={16} strokeWidth={1.5} />
                </ActionIcon>
            </Tooltip>
            <ActionIcon variant="subtle" size="sm" color="red.6" onClick={handleDelete}>
                <IoTrashOutline size={14} />
            </ActionIcon>
        </Group>
    );
};

export { ComponentChatAnswer };
