import { FC } from 'react';

import { Button, ButtonProps } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

import { TrackingService } from 'services/TrackingService';

const ComponentChatButton: FC<{ toggleChatOpen: () => void } & ButtonProps> = ({ toggleChatOpen, ...props }) => {
    return (
        <Button
            color="brand"
            leftSection={<BsRobot />}
            onClick={() => {
                TrackingService.trackComponentChat('floating_button');

                toggleChatOpen();
            }}
            {...props}
        >
            Ask the Docs
        </Button>
    );
};

export { ComponentChatButton };
