import React, { FC } from 'react';

import './styles.module.scss';
import { Component, FeatureKey } from 'models';
import { useCurrentUser } from 'hooks/use-current-user';

import { Login } from 'components/component-chat/screens/Login';
import { ComponentChat } from 'components/component-chat/screens/ComponentChat';
import { RequestAccess } from 'components/component-chat/screens/RequestAccess';
import { ComponentChatScrollArea } from 'components/component-chat/components/ComponentChatScrollArea';
import { useFeatureAccess } from 'hooks/use-feature-access';

const ComponentChatSidebar: FC<{ component: Component }> = ({ component }) => {
    const user = useCurrentUser();
    const hasAiAccess = useFeatureAccess(FeatureKey.ARTIFICIAL_INTELLIGENCE);

    if (!user) {
        return (
            <ComponentChatScrollArea paddingBottom={0}>
                <Login />
            </ComponentChatScrollArea>
        );
    }

    if (hasAiAccess) {
        return <ComponentChat component={component} />;
    }

    return (
        <ComponentChatScrollArea paddingBottom={0}>
            <RequestAccess />
        </ComponentChatScrollArea>
    );
};

export { ComponentChatSidebar };
