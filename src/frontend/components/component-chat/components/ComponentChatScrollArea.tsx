import React, { FC, useRef, useEffect } from 'react';

import { ScrollArea } from '@mantine/core';

const CHAT_PADDING = 160;

const ComponentChatScrollArea: FC<{
    paddingBottom?: number;
    children?: React.ReactNode;
}> = ({ paddingBottom = CHAT_PADDING, children }) => {
    const viewport = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (viewport.current) {
            const current = viewport.current;

            current.scrollTo({
                top: current.scrollHeight,
                behavior: 'smooth',
            });
        }
    }, [viewport, children]);

    return (
        <ScrollArea.Autosize
            className="ai-chat-scroll-area"
            type="auto"
            mah={`calc(100dvh - ${paddingBottom}px)`} // quick 'n dirty
            viewportRef={viewport}
            style={{
                flexGrow: 1,
                flexShrink: 1,
            }}
        >
            {children}
        </ScrollArea.Autosize>
    );
};

export { ComponentChatScrollArea };
