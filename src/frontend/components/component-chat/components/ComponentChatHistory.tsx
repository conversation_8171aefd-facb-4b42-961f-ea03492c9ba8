import { FC } from 'react';

import { Anchor, Divider } from '@mantine/core';

import { useHistory } from 'components/component-chat/hooks/useHistory';

import { ComponentChatMessage } from 'components/component-chat/components/ComponentChatMessage';
import { AIService } from 'services/AIService';

const ComponentChatHistory: FC<{ componentId: string | undefined; projectId: string | undefined }> = ({
    componentId,
    projectId,
}) => {
    const { history, mutate } = useHistory(componentId, projectId);

    const clearHistory = async () => {
        await mutate([], { revalidate: false });
        await AIService.deleteAllMessages();
    };

    if (!history.length) return null;

    return (
        <>
            <Divider label="You've asked before" labelPosition="left" m="xs" mb={0} />

            {history.map((historyMessage) => (
                <ComponentChatMessage key={historyMessage.id} {...historyMessage} isHistory />
            ))}

            <Anchor size="xs" px="xs" pt="xs" onClick={clearHistory}>
                Clear history
            </Anchor>
        </>
    );
};

export { ComponentChatHistory };
