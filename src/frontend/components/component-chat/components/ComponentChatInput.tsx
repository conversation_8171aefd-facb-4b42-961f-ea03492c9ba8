import React, { FC } from 'react';

import {
    ActionIcon,
    ActionIconProps,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    TextareaProps,
    UnstyledButton,
} from '@mantine/core';
import { useFocusTrap } from '@mantine/hooks';
import { IoSendSharp } from 'react-icons/io5';

import { AISuggestion, ComponentType } from 'models';

import { isTouchDevice } from 'helpers/isTouchDevice';

import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';
import { useAISuggestions } from 'components/diagram/hooks/use-ai-suggestions';

import cx from './styles.module.scss';

const ComponentChatInput: FC<{
    userInput: string;
    setUserInput: (input: string) => void;
    handleSubmit: (overrideQuestion?: string) => Promise<void>;
    componentType: ComponentType | undefined;
    disabled?: boolean;
}> = ({ userInput, setUserInput, handleSubmit, componentType, disabled }) => {
    const aiSuggestions = useAISuggestions();
    const { chatOpen } = useComponentChat();

    const focusTrapRef = useFocusTrap(chatOpen);

    let filteredSuggestions = [] as AISuggestion[];
    if (componentType) {
        filteredSuggestions = aiSuggestions.filter((aiSuggestions) => aiSuggestions.types.includes(componentType));
    }

    const sendSuggestion = (suggestion: string) => {
        handleSubmit(suggestion);
    };

    const isTouch = isTouchDevice();

    return (
        <div ref={focusTrapRef}>
            <ComponentChatInputComponent
                textareaProps={{
                    autoFocus: !isTouch,
                    value: userInput,
                    onChange: (event) => {
                        setUserInput(event.target.value);
                    },
                    onKeyDown: (event) => {
                        if (event.code !== 'Enter') return;

                        event.preventDefault();

                        if (disabled) {
                            return;
                        }

                        if (userInput.length) {
                            handleSubmit();
                        }
                    },
                }}
                sendButtonProps={{
                    disabled: disabled || !userInput,
                    onClick: () => handleSubmit(),
                }}
            >
                {!!filteredSuggestions.length && (
                    <SimpleGrid cols={filteredSuggestions.length > 1 ? 2 : 2} spacing={4}>
                        {filteredSuggestions.map((aiSuggestion) => (
                            <UnstyledButton
                                key={aiSuggestion.suggestion}
                                className={cx.suggestion}
                                onClick={() => sendSuggestion(aiSuggestion.suggestion)}
                                disabled={disabled}
                            >
                                {aiSuggestion.suggestion}
                                <IoSendSharp size={10} />
                            </UnstyledButton>
                        ))}
                    </SimpleGrid>
                )}
            </ComponentChatInputComponent>
        </div>
    );
};

const ComponentChatInputComponent = ({
    textareaProps,
    sendButtonProps,
    children,
}: {
    textareaProps?: TextareaProps;
    sendButtonProps?: ActionIconProps & { onClick?: () => void };
    children?: React.ReactNode;
}) => {
    return (
        <Stack gap={4} p="xs" style={{ flexShrink: 0, position: 'sticky', bottom: 0 }} bg="white">
            <Text fw={600}>Explore the product documentation, ask a question!</Text>

            {children}

            <Textarea
                data-autofocus
                rows={4}
                placeholder='Ask a question eg. "What are the wire sizing options?", "Can I connect it to a battery?"'
                rightSection={
                    <ActionIcon size="sm" variant="transparent" {...sendButtonProps}>
                        <IoSendSharp size={16} stroke="1.5" />
                    </ActionIcon>
                }
                {...textareaProps}
            />
        </Stack>
    );
};

export { ComponentChatInput, ComponentChatInputComponent };
