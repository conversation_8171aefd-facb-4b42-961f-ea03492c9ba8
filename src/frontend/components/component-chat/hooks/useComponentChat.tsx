import { proxy } from 'valtio';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { ComponentChatMessage } from 'models';

type ComponentChatState = {
    open: boolean;
    componentId: string | undefined;
    projectId: string | undefined;
    messages: ComponentChatMessage[];
};

const componentChatStateDefaults: ComponentChatState = {
    open: false,
    componentId: undefined,
    projectId: undefined,
    messages: [],
};

const componentChatState = proxy(componentChatStateDefaults);

const useComponentChat = () => {
    const componentChat = useSnapshot(componentChatState);

    const toggleChatOpen = () => {
        componentChatState.open = !componentChatState.open;
    };

    const setChatOpen = () => {
        componentChatState.open = true;
    };

    const setActiveComponentId = (componentId: string) => {
        componentChatState.componentId = componentId;
    };

    const setActiveProjectId = (projectId: string) => {
        componentChatState.projectId = projectId;
    };

    const addMessage = (message: ComponentChatMessage) => {
        componentChatState.messages.push(message);
    };

    const updateMessage = (messageId: string, message: Partial<ComponentChatMessage>) => {
        const index = componentChatState.messages.findIndex((message) => {
            return message.id === messageId;
        });

        if (index !== -1) {
            componentChatState.messages[index] = {
                ...componentChatState.messages[index],
                ...message,
            };
        }
    };

    const clearMessages = () => {
        componentChatState.messages = [];
    };

    const deleteMessage = (messageId: string) => {
        componentChatState.messages = componentChatState.messages.filter((message) => {
            return message.id !== messageId;
        });
    };

    const changeMessageRating = (messageId: string, rating: NonNullable<ComponentChatMessage['rating']>) => {
        updateMessage(messageId, { rating });
    };

    return {
        chatComponentId: componentChat.componentId,
        chatProjectId: componentChat.projectId,
        chatOpen: componentChat.open,
        messages: componentChat.messages,
        setChatOpen,
        toggleChatOpen,
        setActiveComponentId,
        setActiveProjectId,
        addMessage,
        updateMessage,
        changeMessageRating,
        clearMessages,
        deleteMessage,
    };
};

export { useComponentChat, componentChatState, componentChatStateDefaults };
