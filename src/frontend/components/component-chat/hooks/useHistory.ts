import useSWR from 'swr';

import { ComponentChatMessagePayload } from 'models';

import { AIService } from 'services/AIService';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

const useHistory = (componentId: string | undefined, projectId: string | undefined) => {
    const aiMessagesURL = componentId
        ? `/api/aiMessages/?componentId=${componentId}`
        : `/api/aiMessages/?projectId=${projectId}`;

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: aiMessagesURL,
            fetcher: () => {
                if (componentId) {
                    return AIService.getComponentMessages(componentId!);
                }
                if (projectId) {
                    return AIService.getProjectMessages(projectId!);
                }
                throw new Error('aiMessages: projectId or componentId must be provided');
            },
            condition: Boolean(componentId || projectId),
        }),
        {
            revalidateOnFocus: false,
        },
    );

    return {
        ...swr,
        history: (swr.data?.docs as ComponentChatMessagePayload[]) ?? [],
    };
};

export { useHistory };
