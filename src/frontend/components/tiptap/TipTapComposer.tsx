import React, { FC, createContext, useContext, useEffect, useState } from 'react';

import { Box, FileButton, Loader, Tooltip, UnstyledButton } from '@mantine/core';
import { IoNavigateOutline } from 'react-icons/io5';
import { TbMoodPlus, TbPaperclip } from 'react-icons/tb';
import { RichTextEditor, useRichTextEditorContext } from '@mantine/tiptap';

import { useEditor } from '@tiptap/react';
import { Editor, Extension, Node } from '@tiptap/react';

import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import StarterKit from '@tiptap/starter-kit';
import { EmojiPicker } from 'components/emoji-picker/EmojiPicker';

import { LocalFile } from 'models';

import { FilesPreview } from './TipTapComposer.FilesPreview';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import cx from './TipTapComposer.module.scss';

const LocalContext = createContext<{
    onSubmit: () => Promise<void> | void;
    files: LocalFile[];
    setFiles: any;
    submitting: boolean;
}>({
    onSubmit: () => {},
    files: [],
    setFiles: () => {},
    submitting: false,
});

const PreventEnter = Extension.create({
    addKeyboardShortcuts(this) {
        return {
            Enter: () => true,
        };
    },
});

const TipTapComposer: FC<{
    placeholder?: string;
    autofocus?: boolean;
    extensions?: (Extension | Node)[];
    initial?: {
        content: any;
        files: LocalFile[];
    };
    onInit?: (editor: Editor) => void;
    onSubmit: (content: object, files: LocalFile[]) => Promise<void> | void;
    onEscape?: () => void;
    children: React.ReactNode;
}> & {
    Button: FC<{
        onClick: (editor: Editor) => void;
        loading?: boolean;
        disabled?: boolean;
        children: React.ReactNode;
    }>;
    EmojiButton: FC;
    FilesButton: FC;
    Content: typeof RichTextEditor.Content;
    Footer: FC<{
        children: React.ReactNode;
    }>;
} = ({
    placeholder = '',
    autofocus = false,
    extensions = [],
    initial,
    onInit,
    onSubmit,
    onEscape = () => {},
    children,
}) => {
    const [submitting, setSubmitting] = useState(false);
    const [files, setFiles] = useState<LocalFile[]>(initial?.files || []);

    const editor = useEditor(
        {
            extensions: [
                StarterKit.configure({
                    heading: false,
                }),
                Link,
                Placeholder.configure({
                    placeholder,
                }),
                PreventEnter,
                ...extensions,
            ],
            content: initial?.content,
            autofocus,
        },
        [placeholder],
    );

    useEffect(() => {
        setTimeout(() => {
            if (editor) {
                onInit?.(editor);
                editor.commands.focus();
            }
        }, 100);
    }, [editor]);

    const empty = !!editor?.isEmpty && files.length === 0;
    const uploading = files.some((file) => file === null);

    const submit = async () => {
        setSubmitting(true);

        await onSubmit(editor!.getJSON(), files.filter(Boolean));

        editor!.commands.setContent('');
        setFiles([]);

        setSubmitting(false);
    };

    return editor ? (
        <LocalContext.Provider value={{ onSubmit: submit, files, setFiles, submitting }}>
            <RichTextEditor
                editor={editor}
                className={cx.composer}
                onKeyDown={(event) => {
                    if (
                        event.key === 'Enter' &&
                        !(event.shiftKey || event.metaKey || event.ctrlKey) &&
                        !empty &&
                        !uploading
                    ) {
                        submit().then();
                    }

                    if (event.key === 'Escape' && empty) {
                        onEscape();
                    }
                }}
            >
                {children}
            </RichTextEditor>
        </LocalContext.Provider>
    ) : null;
};

const Button: typeof TipTapComposer.Button = ({ onClick, loading = false, disabled = false, children, ...props }) => {
    const { editor } = useRichTextEditorContext();

    return loading ? (
        <Box className={cx.buttonLoader}>
            <Loader size={16} />
        </Box>
    ) : (
        <UnstyledButton
            className={cx.button}
            onClick={() => {
                onClick(editor!);
            }}
            disabled={disabled}
            {...props}
        >
            {children}
        </UnstyledButton>
    );
};

TipTapComposer.Button = Button;

TipTapComposer.EmojiButton = () => {
    const { editor } = useRichTextEditorContext();

    return (
        <EmojiPicker
            onEmojiSelect={(emoji) => {
                editor?.commands.focus();
                editor?.commands.insertContent(emoji);
            }}
        >
            <div>
                <TipTapComposer.Button onClick={() => {}}>
                    <TbMoodPlus />
                </TipTapComposer.Button>
            </div>
        </EmojiPicker>
    );
};

TipTapComposer.FilesButton = () => {
    const { files, setFiles } = useContext(LocalContext);

    const onChange = async (uploads: File[]) => {
        const indexOffset = files.length;

        uploads.forEach((file, index) => {
            FileService.create({
                file,
                group: 'diagram-chat-message',
            })
                .then((upload) => {
                    setFiles((state: LocalFile[]) => {
                        state[index + indexOffset] = {
                            file: upload.id,
                        };

                        return [...state];
                    });
                })
                .catch((error) => {
                    if (error instanceof FileSizeLimitExceededError) {
                        LocalNotificationService.showError({ title: error.title, message: error.message });
                    } else {
                        throw error;
                    }
                });
        });
    };

    return (
        <FileButton onChange={onChange} multiple>
            {({ onClick }) => (
                <UnstyledButton className={cx.button} onClick={onClick}>
                    <TbPaperclip />
                </UnstyledButton>
            )}
        </FileButton>
    );
};

TipTapComposer.Content = RichTextEditor.Content;

const Footer: typeof TipTapComposer.Footer = ({ children }) => {
    const { editor } = useRichTextEditorContext();
    const { files, setFiles, onSubmit, submitting } = useContext(LocalContext);

    const empty = !!editor?.isEmpty && files.length === 0;
    const uploading = files.some((file) => file === null);

    return editor ? (
        <React.Fragment>
            <Box className={cx.footer}>
                <Box className={cx.footerSection}>{children}</Box>
                <Box className={cx.footerSection}>
                    <TipTapComposer.Button
                        onClick={onSubmit}
                        disabled={empty || uploading}
                        loading={submitting}
                        data-color="green"
                    >
                        <Tooltip label="⌘/ctrl + ↵" position="left">
                            <div style={{ width: 16, height: 16 }}>
                                <IoNavigateOutline strokeWidth={1.5} />
                            </div>
                        </Tooltip>
                    </TipTapComposer.Button>
                </Box>
            </Box>
            <FilesPreview
                files={files}
                handleDelete={(fileToDelete) => {
                    setFiles(files.filter((file) => file.file !== fileToDelete));
                }}
            />
        </React.Fragment>
    ) : null;
};

TipTapComposer.Footer = Footer;

TipTapComposer.displayName = 'TipTapComposer';

export { TipTapComposer };
