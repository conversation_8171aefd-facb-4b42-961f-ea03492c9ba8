import { SearchService } from 'components/component-overview/services/SearchService';
import { AIService } from 'services/AIService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { DesignLibraryService } from 'services/DesignLibraryService';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';
import { AiSearchFiltersHelpers, GeneratedSearchFilters } from 'models';

import { useSearchHistory } from 'hooks/use-search-history';

import { useHash } from '@mantine/hooks';
import { useAction } from 'hooks/use-action';

export const useComponentSearchSubmit = () => {
    const [, setHash] = useHash();
    const { addSearchHistory } = useSearchHistory();

    const [submit, submitting] = useAction(async (query: string) => {
        const shortcut = AiSearchFiltersHelpers.checkForQueryResultShortcut(query);
        if (shortcut) {
            finish(shortcut);
            return;
        }

        try {
            const start = new Date().getTime();

            const previousFilter = SearchService.getCleanFilters();
            const aiServiceResponse = await AIService.fetchSearchFilters(query);
            const generatedFilters = AiSearchFiltersHelpers.parseSearchFiltersResponse(aiServiceResponse, query);

            addSearchHistory(query);

            InternalTrackingService.track('product.search.query', {
                query,
                filters: {
                    previous: previousFilter,
                    calculated: generatedFilters,
                },
                duration: new Date().getTime() - start,
            });

            finish(generatedFilters);
        } catch (error) {
            console.error('Error fetching AI search filters:', (error as any)?.message || error);

            finish(AiSearchFiltersHelpers.defaultFilters(query));
        }
    });

    const finish = (generatedFilters: GeneratedSearchFilters) => {
        updateStateFromGeneratedFilters(generatedFilters);
        setHash(getHashFromGeneratedFilters(generatedFilters));
    };

    return { submit, submitting };
};

const updateStateFromGeneratedFilters = (generatedFilters: GeneratedSearchFilters) => {
    SearchService.resetFilters();
    ProfileOverviewService.resetFilters();
    DesignLibraryService.resetFilters();
    CaseStudySearchService.resetFilters();

    if (generatedFilters.productFilters) {
        SearchService.mergeFilters(generatedFilters?.productFilters);
    }
    if (generatedFilters.profileFilters) {
        ProfileOverviewService.mergeFilters(generatedFilters.profileFilters);
    }
    if (generatedFilters.designFilters) {
        DesignLibraryService.mergeFilters(generatedFilters.designFilters);
    }
    if (generatedFilters.caseStudyFilters) {
        CaseStudySearchService.mergeFilters(generatedFilters.caseStudyFilters);
    }
};

const HashFilterMap: Record<string, keyof GeneratedSearchFilters> = {
    products: 'productFilters',
    profiles: 'profileFilters',
    designs: 'designFilters',
};

const getHashFromGeneratedFilters = (generatedFilters: GeneratedSearchFilters) => {
    if (onlyManufacturer(generatedFilters)) {
        return 'profiles';
    }

    for (const [hash, filterKey] of Object.entries(HashFilterMap)) {
        if (hasSearchOrManufacturer(generatedFilters[filterKey])) {
            return hash;
        }
    }

    for (const [hash, filterKey] of Object.entries(HashFilterMap)) {
        if (generatedFilters[filterKey]) {
            return hash;
        }
    }

    return 'products';
};

const onlyManufacturer = (filters: GeneratedSearchFilters) =>
    Object.values(filters).every((filter) => filter && 'manufacturer' in filter && Object.keys(filter).length === 1);

const hasSearchOrManufacturer = (filter: GeneratedSearchFilters[keyof GeneratedSearchFilters]) =>
    filter && ('search' in filter || 'manufacturer' in filter);
