import { proxy } from 'valtio';
import { useSnapshot } from 'hooks/use-safe-snapshot';

export enum ComponentSearchTab {
    COMPONENTS = 'components',
    COMPANIES = 'companies',
    CASE_STUDIES = 'caseStudies',
}

const componentSearchTabsState = proxy({
    tab: 'components' as ComponentSearchTab,
});

const useComponentSearchTabs = () => {
    const { tab } = useSnapshot(componentSearchTabsState);

    const setTab = (tab: ComponentSearchTab) => {
        componentSearchTabsState.tab = tab;
    };

    return {
        tab,
        setTab,
    };
};

export { useComponentSearchTabs };
