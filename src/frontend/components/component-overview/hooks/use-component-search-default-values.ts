import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { useRouter } from 'next/router';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { SearchService } from '../services/SearchService';

const useComponentSearchDefaultValues = () => {
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        const queryAsString = searchParams?.get('query');

        if (queryAsString) {
            const query = JSON.parse(queryAsString);

            if (query) {
                SearchService.mergeFilters(query);

                router.replace(RouterHelpers.urls.search(), undefined, { shallow: true }).then();
            }
        }
    }, [searchParams]);
};

export { useComponentSearchDefaultValues };
