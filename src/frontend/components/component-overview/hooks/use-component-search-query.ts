import { useState } from 'react';

import { SearchService } from 'components/component-overview/services/SearchService';
import { DesignLibraryService } from 'services/DesignLibraryService';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';
import { componentSearchBar } from 'components/component-overview/state/component-search-bar';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { useCaseStudySearch } from 'components/case-study-search/hooks/use-case-study-search';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';
import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

export const useComponentSearchQuery = () => {
    const [query, setQuery] = useState(componentSearchBar.query || '');

    const productFilters = useComponentSearchFilters();
    const { filters: caseStudyFilters } = useCaseStudySearch();
    const { filters: designLibraryFilters } = useDesignLibrarySearch();
    const { filters: profileOverviewFilters } = useProfileOverviewSearch();

    const showReset =
        !!query ||
        !SearchService.isInitialSearch(productFilters) ||
        !CaseStudySearchService.isInitialSearch(caseStudyFilters) ||
        !DesignLibraryService.isInitialSearch(designLibraryFilters) ||
        !ProfileOverviewService.isInitialSearch(profileOverviewFilters);

    const resetAll = showReset
        ? () => {
              setQuery('');
              SearchService.resetFilters();
              ProfileOverviewService.resetFilters();
              DesignLibraryService.resetFilters();
              CaseStudySearchService.resetFilters();
          }
        : undefined;

    return { resetAll, query, setQuery };
};
