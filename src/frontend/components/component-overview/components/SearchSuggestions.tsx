import React from 'react';

import { Stack, Text, UnstyledButton } from '@mantine/core';

import { InternalTrackingService } from 'services/InternalTrackingService';

import { CarouselSection } from 'components/section/CarouselSection';

import cx from './SearchSuggestions.module.scss';

export const SEARCH_SUGGESTIONS = [
    '40 kWh battery',
    '600 V to 300 V DC/DC converter',
    'Unidirectional converter 48 V to 12 V',
    '15kW EV charger with type 2 connectors',
    '20 A 500 V DC breaker',
    '400 W solar module',
];

const SearchSuggestions = ({ handleSearch }: { handleSearch: (suggestion: string) => void }) => (
    <Stack gap={4} align="start" w="100%" data-suggestions>
        <Text c="dimmed" fz="xs" fw={500}>
            Search suggestions
        </Text>
        <CarouselSection cols={{ xs: 2, sm: 3, md: 4, lg: 4, xl: 6, xxl: 6 }} w="100%">
            {SEARCH_SUGGESTIONS.map((suggestion) => (
                <UnstyledButton
                    className={cx.suggestion}
                    onClick={(event) => {
                        event.preventDefault();

                        InternalTrackingService.track('product.search.suggestion', {
                            suggestion,
                        });

                        handleSearch(suggestion);
                    }}
                    key={suggestion}
                >
                    {suggestion}
                </UnstyledButton>
            ))}
        </CarouselSection>
    </Stack>
);

export { SearchSuggestions };
