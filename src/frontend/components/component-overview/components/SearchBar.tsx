import React, { FC, useMemo } from 'react';
import { Box, InputProps, Menu } from '@mantine/core';
import { IoSearch } from 'react-icons/io5';

import { useTypewriter } from 'components/typewriter/hooks/use-typewriter';

import { SearchBox } from 'components/search-box';

import { useIsMobile } from 'hooks/use-is-mobile';
import { useComponentSearchSubmit } from '../hooks/use-component-search-submit';
import { useComponentSearchQuery } from '../hooks/use-component-search-query';
import { useFocusWithin } from '@mantine/hooks';

import { SearchBarAutocomplete } from './SearchBarAutocomplete';
import { SearchSuggestions } from './SearchSuggestions';

const SearchBar: FC<{
    size?: InputProps['size'];
    searchSuggestions?: boolean;
    placeholder?: string;
    showAIButton?: boolean;
}> = ({ size = 'lg', searchSuggestions = true, showAIButton, placeholder: overridePlaceholder }) => {
    const { ref, focused } = useFocusWithin();

    const isMobile = useIsMobile();

    const defaultPlaceholder = 'Search for companies, their products, services, or reference designs…';
    const cyclingPlaceholder = useTypewriter([
        'Search products',
        'Search companies',
        'Search designs',
        'Search case studies',
    ]);

    const placeholder =
        overridePlaceholder || (isMobile ? (focused ? 'Search' : cyclingPlaceholder) : defaultPlaceholder);

    const { query, setQuery, resetAll } = useComponentSearchQuery();
    const { submitting, submit } = useComponentSearchSubmit();

    const autocompleteOpen = useMemo(() => {
        return focused && !(document.activeElement as HTMLElement)?.dataset.aiButton;
    }, [focused]);

    // replace sparkles
    return (
        <>
            <Menu
                opened={autocompleteOpen}
                position="bottom-start"
                offset={0}
                width="target"
                trapFocus={false}
                shadow="xl"
            >
                <Menu.Target>
                    <Box ref={ref}>
                        <SearchBox
                            value={query ?? ''}
                            placeholder={placeholder}
                            leftSection={<IoSearch size={16} />}
                            size={size}
                            autoComplete="off"
                            onChange={(event) => {
                                setQuery(event.target.value);
                            }}
                            isLoading={submitting}
                            submit={() => {
                                (document.activeElement as HTMLElement)?.blur();
                                submit(query);
                            }}
                            handleReset={resetAll}
                            autocompleteOpen={autocompleteOpen}
                            showAIButton={showAIButton}
                        />
                    </Box>
                </Menu.Target>
                <SearchBarAutocomplete
                    query={query}
                    setQuery={setQuery}
                    handleSearch={(_query) => {
                        setQuery(_query);
                        submit(_query);
                    }}
                />
            </Menu>
            {searchSuggestions && (
                <SearchSuggestions
                    handleSearch={(suggestion) => {
                        setQuery(suggestion);
                        submit(suggestion);
                    }}
                />
            )}
        </>
    );
};

export { SearchBar };
