import React, { FC, useEffect } from 'react';

import { Box } from '@mantine/core';

import { InternalTrackingService } from 'services/InternalTrackingService';

import { useComponentSearchBar } from 'components/component-overview/hooks/use-component-search-bar';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const ImpressionTracker: FC<{
    namespace: string;
    data: object;
    children: React.ReactNode;
}> = ({ namespace, data, children }) => {
    const { query } = useComponentSearchBar();
    const filters = useComponentSearchFilters();

    useEffect(() => {
        InternalTrackingService.track(`${namespace}.view`, {
            ...data,
            search: {
                query,
                filters,
            },
        });
    }, []);

    const handleClick = () => {
        InternalTrackingService.track(`${namespace}.click`, {
            ...data,
            search: {
                query,
                filters,
            },
        });
    };

    return (
        <Box display="grid" onClick={handleClick}>
            {children}
        </Box>
    );
};

export { ImpressionTracker };
