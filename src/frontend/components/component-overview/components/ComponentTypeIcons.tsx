import React, { FC } from 'react';

import { Grid, GridColProps, UnstyledButton } from '@mantine/core';

import { ComponentCount, ComponentType } from 'models';

import { HeroComponentIcon } from 'components/page/Hero';
import { ComponentLanding } from 'components/component-landing';

import cx from './ComponentTypeIcons.module.scss';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { useCurrentUser } from 'hooks/use-current-user';

const componentTypeGridSpan: GridColProps['span'] = { base: 8, md: 4 };

const ComponentTypeIcons: FC<{
    componentCount?: ComponentCount;
}> = ({ componentCount }) => {
    const user = useCurrentUser();

    const { type: value } = useComponentSearchFilters();

    const toggleType = (type: ComponentType) => {
        if (value === type) {
            SearchService.setFilter('type', undefined);
            return;
        }

        SearchService.setFilter('type', type);
    };

    const enabledComponents = componentCount
        ? (Object.keys(componentCount)
              .filter((key) => componentCount?.[key as keyof ComponentCount] > 0)
              .sort(
                  (key, nextKey) =>
                      componentCount?.[nextKey as keyof ComponentCount] - componentCount?.[key as keyof ComponentCount],
              ) as ComponentType[])
        : [];

    return (
        <Grid gutter="xs" columns={32} className={cx.grid}>
            {enabledComponents.map((component) => (
                <Grid.Col key={component} span={componentTypeGridSpan}>
                    <UnstyledButton onClick={() => toggleType(component)} w="100%">
                        <HeroComponentIcon type={component} selected={value === component} />
                    </UnstyledButton>
                </Grid.Col>
            ))}

            {user && (
                <Grid.Col span={componentTypeGridSpan} data-add-product>
                    <ComponentLanding.AddProduct />
                </Grid.Col>
            )}
        </Grid>
    );
};

export { ComponentTypeIcons };
