import React from 'react';

import { range } from 'radash';

import { Menu, MenuItemProps as MantineMenuItemProps } from '@mantine/core';
import {
    IoArchiveOutline,
    IoBatteryHalfOutline,
    IoBriefcaseOutline,
    IoCashOutline,
    IoGlobeOutline,
    IoTimerOutline,
} from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { InlineFiltersProps } from 'components/component-overview/inline-filters/InlineFilters';

import { useDefaultRegion } from 'hooks/use-default-region';
import { ENERGY_DEFAULT_UNIT, Region } from 'models';

type MenuItemProps = MantineMenuItemProps & {
    onClick: () => void;
    label: string;
};

const REGION_FALLBACK = Region.EU;

const FiltersDropdown = ({ hideFilters }: { hideFilters?: InlineFiltersProps['hideFilters'] }) => {
    const user = useCurrentUser();
    const defaultRegion = useDefaultRegion();

    const { type, manufacturer, distributor, energyCapacity, includeArchived, regionAvailability } =
        useComponentSearchFilters();

    const showCapacity = type === 'battery';
    const disableCapacity = !!energyCapacity;

    const disableArchived = !!includeArchived;

    const disableManufacturers = manufacturer !== null;
    const disableDistributors = distributor !== null;
    const disableRegionAvailability = !!regionAvailability;

    const middleFilters: MenuItemProps[] = [];

    if (!hideFilters?.includes('manufacturer') && !disableManufacturers) {
        middleFilters.push({
            label: 'Filter by manufacturer',
            onClick: () => {
                SearchService.setFilter('manufacturer', undefined);
            },
            leftSection: <IoBriefcaseOutline />,
        });
    }

    if (!hideFilters?.includes('distributor') && !disableDistributors) {
        middleFilters.push({
            label: 'Filter by distributor',
            onClick: () => {
                SearchService.setFilter('distributor', undefined);
            },
            leftSection: <IoBriefcaseOutline />,
        });
    }

    if (!hideFilters?.includes('regionAvailability') && !disableRegionAvailability) {
        middleFilters.push({
            label: 'Filter by region',
            onClick: () => {
                SearchService.setFilter('regionAvailability', [defaultRegion ?? REGION_FALLBACK]);
            },
            leftSection: <IoGlobeOutline />,
        });
    }

    if (!hideFilters?.includes('hasPrice')) {
        middleFilters.push({
            label: 'Has price',
            onClick: () => {
                SearchService.setFilter('hasPrice', true);
            },
            leftSection: <IoCashOutline />,
        });
    }

    if (!hideFilters?.includes('hasLeadTime')) {
        middleFilters.push({
            label: 'Has lead time',
            onClick: () => {
                SearchService.setFilter('hasLeadTime', true);
            },
            leftSection: <IoTimerOutline />,
        });
    }

    // if (!hideFilters?.includes('compliances') && !disableCompliances) {
    //     middleFilters.push({
    //         label: `Filter for ${Compliances.options.map(({ label }) => label).join(', ')}`,
    //         onClick: () => {
    //             SearchService.setFilter('compliances', []);
    //         },
    //         leftSection: <IoCheckmarkCircleOutline />,
    //         disabled: disableCompliances,
    //     });
    // }

    // if (!hideFilters?.includes('aiEnabled')) {
    //     middleFilters.push({
    //         label: 'AI enabled',
    //         onClick: () => {
    //             SearchService.setFilter('aiEnabled', true);
    //         },
    //         leftSection: <BsRobot />,
    //         disabled: disableAiEnabled,
    //     });
    // }

    // if (!hideFilters?.includes('inAppSupport')) {
    //     middleFilters.push({
    //         label: 'With manufacturer support',
    //         onClick: () => {
    //             SearchService.setFilter('inAppSupport', true);
    //         },
    //         leftSection: <IoChatbubblesOutline />,
    //         disabled: disableInAppSupport,
    //     });
    // }

    if (showCapacity && !hideFilters?.includes('capacity')) {
        middleFilters.push({
            label: 'Filter by capacity',
            onClick: () => {
                SearchService.setFilter('energyCapacity', { unit: ENERGY_DEFAULT_UNIT });
            },
            leftSection: <IoBatteryHalfOutline />,
            disabled: disableCapacity,
        });
    }

    if (user?.developer && !hideFilters?.includes('archived')) {
        middleFilters.push({
            label: 'Include archived components',
            onClick: () => {
                SearchService.setFilter('includeArchived', true);
            },
            leftSection: <IoArchiveOutline />,
            disabled: disableArchived,
        });
    }

    return (
        <>
            <FilteredMenuItems items={middleFilters} />

            {!hideFilters?.includes('ports') && (
                <>
                    <Menu.Divider />
                    <PortFilters />
                </>
            )}
        </>
    );
};

const PortFilters = () => {
    const { ports, addPortFilter, maxNbPorts, getPortAddFilters } = usePorts();
    const nbPorts = Math.min((ports?.length || 0) + 1, maxNbPorts);

    return (
        <>
            {[...range(nbPorts - 1)].map((index) => {
                const filters = getPortAddFilters(index);

                if (!filters.length) return null;

                const portFilters: MenuItemProps[] = filters.map(({ type, label, defaultValue, addLabel }) => ({
                    label: addLabel || `Filter by ${label}`,
                    onClick: () => {
                        addPortFilter(index, type, defaultValue);
                    },
                    leftSection: <div />,
                }));

                return <FilteredMenuItems key={index} label={`Port ${index + 1}`} items={portFilters} />;
            })}
        </>
    );
};

const FilteredMenuItems = ({ label, items }: { label?: string; items: MenuItemProps[] }) => {
    if (items.length === 0) return null;

    return (
        <>
            {label && <Menu.Label>{label}</Menu.Label>}
            {items.map(({ label, ...props }, index) => (
                <Menu.Item key={index} {...props}>
                    {label}
                </Menu.Item>
            ))}
        </>
    );
};

export { FiltersDropdown };
