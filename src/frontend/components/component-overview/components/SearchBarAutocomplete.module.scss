.dropdown {
    --menu-item-hover: white;

    backdrop-filter: blur(5px);

    background-color: rgba(255, 255, 255, 0.9);

    border-radius: 0 0 var(--mantine-radius-sm) var(--mantine-radius-sm);
}

.icon {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 36px;
    height: 36px;

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);
    background-color: var(--mantine-color-white);

    padding: 2px;

    color: var(--mantine-color-gray-7);

    > * {
        max-width: 100%;
        max-height: 100%;
    }

    [data-component-icon-label-text] {
        font-size: 8px !important;
    }

    [data-component-icon-label-icon] {
        display: none !important;
    }
}

.componentIcon {
    > * {
        width: 60%;
        height: 60%;

        text-align: center;
    }
}
