import { Charger, Component, VoltageType } from 'models';

type Port = Component['electrical']['ports'][number];
type ChargingPort = Charger['electrical']['chargingPorts'][number];
type MergedPorts<PortType extends Port | ChargingPort> = { index: number | [number, number]; port: PortType }[];

export const getComponentMergedPorts = (ports: Port[]) =>
    ports?.reduce(getPortCollapseFunction(portsAreSimilar), [] as MergedPorts<Port>);

export const getComponentMergedChargingPorts = (chargingPorts: ChargingPort[]) =>
    chargingPorts?.reduce(getPortCollapseFunction(chargingPortsAreSimilar), [] as MergedPorts<ChargingPort>);

const getPortCollapseFunction =
    <PortType extends Port | ChargingPort>(portsAreSimilar: (a: PortType, b: PortType) => boolean) =>
    (mergedPorts: MergedPorts<PortType>, port: PortType, index: number): MergedPorts<PortType> => {
        if (mergedPorts.length === 0) {
            return [{ index, port }];
        }

        const [{ index: lastPortIndexOrRange, port: lastPort }] = mergedPorts.splice(-1, 1);

        if (portsAreSimilar(port, lastPort)) {
            const rangeStartIndex = Array.isArray(lastPortIndexOrRange)
                ? lastPortIndexOrRange[0]
                : lastPortIndexOrRange;

            return [...mergedPorts, { index: [rangeStartIndex, index], port }];
        }

        return [...mergedPorts, { index: lastPortIndexOrRange, port: lastPort }, { index, port }];
    };

const voltageSpecs = ['min', 'nom', 'max'] as const;
const powerSpecs = ['nom', 'max'] as const;
const currentSpecs = ['nom', 'max'] as const;

const portsAreSimilar = (a: Port, b: Port): boolean => {
    return (
        a.powerFlowDirection === b.powerFlowDirection &&
        (('DC' in a &&
            a.DC.enabled &&
            'DC' in b &&
            b.DC.enabled &&
            voltageSpecs.every((spec) => a.DC.voltage[spec] === b.DC.voltage[spec]) &&
            // @ts-ignore
            powerSpecs.every((spec) => a.DC.power?.[spec] === b.DC.power?.[spec]) &&
            // @ts-ignore
            currentSpecs.every((spec) => a.DC.current?.[spec] === b.DC.current?.[spec])) ||
            (!portHasVoltageType(a, 'DC') && !portHasVoltageType(b, 'DC'))) &&
        (('AC' in a &&
            a.AC.enabled &&
            'AC' in b &&
            b.AC.enabled &&
            voltageSpecs.every((spec) => a.AC.voltage[spec] === b.AC.voltage[spec]) &&
            // @ts-ignore
            powerSpecs.every((spec) => a.AC.power?.[spec] === b.AC.power?.[spec]) &&
            // @ts-ignore
            currentSpecs.every((spec) => a.AC.current?.[spec] === b.AC.current?.[spec])) ||
            (!portHasVoltageType(a, 'AC') && !portHasVoltageType(b, 'AC')))
    );
};

const chargerVoltageSpecs = ['min', 'max'] as const;
const chargerPowerSpecs = ['min', 'nom', 'max'] as const;
const chargerCurrentSpecs = ['min', 'max'] as const;

const chargingPortsAreSimilar = (a: ChargingPort, b: ChargingPort): boolean => {
    return (
        a.voltageType === b.voltageType &&
        chargerVoltageSpecs.every((spec) => a.voltage[spec] === b.voltage[spec]) &&
        chargerPowerSpecs.every((spec) => a.power?.[spec] === b.power?.[spec]) &&
        chargerCurrentSpecs.every((spec) => a.current?.[spec] === b.current?.[spec]) &&
        a.connector === b.connector &&
        a.features.every((feature) => b.features.includes(feature))
    );
};

const portHasVoltageType = (port: Port, voltageType: VoltageType) => {
    // @ts-ignore
    return voltageType in port && port[voltageType].enabled;
};
