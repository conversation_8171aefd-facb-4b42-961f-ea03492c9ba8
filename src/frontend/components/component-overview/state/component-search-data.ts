import { proxy, subscribe } from 'valtio';

import { SearchService } from 'components/component-overview/services/SearchService';
import { deepClone } from 'helpers/deep-clone';
import { ComponentSearchData } from 'models';

export const DEFAULT_COMPONENT_SEARCH_DATA = {
    hasSearched: false,
    docs: [],
    page: 0,
    totalPages: 1,
    totalComponents: 0,
    isLoading: false,
    error: false,
};

const componentSearchData = proxy<ComponentSearchData>(deepClone(DEFAULT_COMPONENT_SEARCH_DATA));

subscribe(componentSearchData, (changes) => {
    changes.forEach((change) => {
        const [type, path] = change;

        if (type === 'set' && path.join('.') === 'page') {
            SearchService.debouncedSearch();
        }
    });
});

export { componentSearchData };
