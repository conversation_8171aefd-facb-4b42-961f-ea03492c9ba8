import { proxy, subscribe } from 'valtio';

import { componentSearchData } from 'components/component-overview/state/component-search-data';
import { SearchService } from 'components/component-overview/services/SearchService';
import { deepClone } from 'helpers/deep-clone';
import { ComponentQuery, SortType } from 'models';
import { componentSearchWeights } from './component-search-weights';

export const DEFAULT_SEARCH_COMPONENT_FILTERS: ComponentQuery = {
    query: '',
    search: '',
    type: undefined,
    manufacturer: '',
    distributor: undefined,
    ports: [],
    charging: undefined,
    includeArchived: false,
    energyCapacity: undefined,
    sort: SortType.RELEVANCE,
    aiEnabled: false,
    inAppSupport: undefined,
    regionAvailability: undefined,
    compliances: undefined,
    priorityManufacturer: undefined,
    productsForCompatibility: undefined,
    application: undefined,
    maxPrice: undefined,
    hasPrice: undefined,
    hasLeadTime: undefined,
    event: undefined,
    cable: {
        voltageType: 'DC',
    },
};

const componentSearchFilters = proxy<ComponentQuery>(deepClone(DEFAULT_SEARCH_COMPONENT_FILTERS));

const searchAgainIfChanges = (changes: unknown[]) => {
    if (changes.length) {
        componentSearchData.page = 0;
        SearchService.debouncedSearch();
    }
};

subscribe(componentSearchFilters, searchAgainIfChanges);
subscribe(componentSearchWeights, searchAgainIfChanges);

export { componentSearchFilters };
