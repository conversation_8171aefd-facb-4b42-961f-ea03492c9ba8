import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { VoltageField } from 'components/component-fields/VoltageField';
import { FormatHelpers } from 'helpers/formatters';
import { CurrentField } from 'components/component-fields/CurrentField';
import { PowerField } from 'components/component-fields/PowerField';
import { currentConverter, voltageConverter, powerConverter } from 'models';

type FilterType = 'voltage' | 'current' | 'power';

const formatValueForType = (type: FilterType, value?: number) => {
    switch (type) {
        case 'voltage':
            return FormatHelpers.formatValue(value, voltageConverter);
        case 'current':
            return FormatHelpers.formatValue(value, currentConverter);
        case 'power':
            return FormatHelpers.formatValue(value, powerConverter);
    }
};

const InlineFiltersMeasurementValue: FC<{
    type: FilterType;
    name: string;
    value?: number;
    placeholder: string;
    onRemove?: () => void;
    active: boolean;
}> = ({ type, name, value, placeholder, onRemove, active }) => {
    const label = formatValueForType(type, value);

    return (
        <InlineFilters.Section
            onRemove={onRemove}
            body={
                <InlineFilters.MeasurementWrapper placeholder={placeholder} label={label}>
                    {type === 'voltage' && <VoltageField name={name} size="xs" hideIcons fields={['value']} />}
                    {type === 'current' && <CurrentField name={name} size="xs" hideIcons fields={['value']} />}
                    {type === 'power' && <PowerField name={name} size="xs" hideIcons fields={['value']} />}
                </InlineFilters.MeasurementWrapper>
            }
            active={active}
        />
    );
};

export { InlineFiltersMeasurementValue };
