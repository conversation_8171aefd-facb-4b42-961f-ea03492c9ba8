import { UnstyledButton } from '@mantine/core';
import { IoArrowBack } from 'react-icons/io5';

import { SearchService } from 'components/component-overview/services/SearchService';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersBack = () => {
    return (
        <UnstyledButton onClick={SearchService.resetHasSearched}>
            <InlineFilters.Section
                label={<IoArrowBack style={{ marginTop: 5 }} />}
                body="Back to overview"
                active={false}
            />
        </UnstyledButton>
    );
};

export { InlineFiltersBack };
