import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { CurrentField } from 'components/component-fields/CurrentField';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { FormatHelpers } from 'helpers/formatters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { currentConverter } from 'models';

const InlineFiltersCurrent: FC<{
    index: number;
}> = ({ index }) => {
    const name = `ports[${index}].current`;

    const { ports } = useComponentSearchFilters();
    const current = ports ? ports[index]?.current : undefined;

    const label = FormatHelpers.formatMinNomMax({ ...current, unit: 'A' }, currentConverter);

    const { removePortFilter } = usePorts();

    return (
        <InlineFilters.Section
            body={
                <InlineFilters.MeasurementWrapper label={label} placeholder="Current">
                    <CurrentField name={name} size="xs" hideIcons fields={['nom']} />
                </InlineFilters.MeasurementWrapper>
            }
            onRemove={() => removePortFilter(index, 'current')}
            active={!!current}
        />
    );
};

export { InlineFiltersCurrent };
