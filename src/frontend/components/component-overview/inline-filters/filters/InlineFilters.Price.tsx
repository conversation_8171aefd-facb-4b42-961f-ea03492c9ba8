import React from 'react';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

import { IoCashOutline } from 'react-icons/io5';

const InlineFiltersMaxPrice = () => {
    const { maxPrice } = useComponentSearchFilters();

    const onRemove = () => {
        SearchService.setFilter('maxPrice', undefined);
    };

    return (
        <InlineFilters.Number
            icon="$"
            name="maxPrice"
            value={maxPrice ?? undefined}
            formattedValue={maxPrice ? `${maxPrice} Max.` : undefined}
            label="Max. price"
            placeholder="Max. price"
            onRemove={maxPrice ? onRemove : undefined}
            active={!!maxPrice}
        />
    );
};

const InlineFiltersPriceFlag = () => {
    const { hasPrice } = useComponentSearchFilters();

    const onRemove = () => {
        SearchService.setFilter('hasPrice', undefined);
    };

    return (
        <InlineFilters.Section
            icon={<IoCashOutline />}
            label={!hasPrice && 'Has price'}
            body={hasPrice && 'Has price'}
            wrapperProps={{
                onClick: () => {
                    SearchService.setFilter('hasPrice', hasPrice ? undefined : true);
                },
            }}
            onRemove={hasPrice ? onRemove : undefined}
            active={hasPrice !== undefined}
        />
    );
};

export { InlineFiltersPriceFlag, InlineFiltersMaxPrice };
