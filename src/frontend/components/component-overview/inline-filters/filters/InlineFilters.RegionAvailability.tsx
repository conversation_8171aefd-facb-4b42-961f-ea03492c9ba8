import type { FC } from 'react';

import { Flex, Menu } from '@mantine/core';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { IoCheckmarkOutline, IoGlobeOutline } from 'react-icons/io5';
import { Region, RegionAvailability } from 'models';

const InlineFiltersRegionAvailability: FC = () => {
    const { regionAvailability } = useComponentSearchFilters();

    const onClick = (value: Region) => {
        SearchService.setFilter(
            'regionAvailability',
            regionAvailability?.includes(value)
                ? regionAvailability.filter((region) => region !== value)
                : [...(regionAvailability ?? []), value],
        );
    };

    const onRemove = () => {
        SearchService.setFilter('regionAvailability', undefined);
    };

    if (!regionAvailability || regionAvailability?.length === 0) return null;

    const formattedValue = regionAvailability?.join(', ');

    return (
        <InlineFilters.Section
            label={
                <Flex mr={4} style={{ transform: 'translateY(3px)' }}>
                    <IoGlobeOutline />
                </Flex>
            }
            active={!!regionAvailability?.length}
            body={formattedValue}
            onRemove={onRemove}
            popoverContent={
                <>
                    <Menu.Label>Region Availability</Menu.Label>
                    {RegionAvailability.options.map(({ value, label }) => (
                        <Menu.Item
                            key={value}
                            onClick={() => onClick(value)}
                            leftSection={regionAvailability?.includes(value) ? <IoCheckmarkOutline /> : null}
                        >
                            {label}
                        </Menu.Item>
                    ))}
                </>
            }
        />
    );
};

export { InlineFiltersRegionAvailability };
