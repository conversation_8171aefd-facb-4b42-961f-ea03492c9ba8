import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { VoltageField } from 'components/component-fields/VoltageField';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { FormatHelpers } from 'helpers/formatters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { voltageConverter } from 'models';

const InlineFiltersVoltage: FC<{
    index: number;
}> = ({ index }) => {
    const name = `ports[${index}].voltage`;

    const { ports } = useComponentSearchFilters();
    const port = ports ? ports[index] : undefined;

    const label = FormatHelpers.formatMinNomMax({ ...port?.voltage, unit: 'V' }, voltageConverter);

    const { removePortFilter } = usePorts();

    return (
        <InlineFilters.Section
            body={
                <InlineFilters.MeasurementWrapper placeholder="Voltage" label={label}>
                    <VoltageField name={name} size="xs" fields={['nom']} hideIcons />
                </InlineFilters.MeasurementWrapper>
            }
            onRemove={() => removePortFilter(index, 'voltage')}
            active={!!port?.voltage}
        />
    );
};

export { InlineFiltersVoltage };
