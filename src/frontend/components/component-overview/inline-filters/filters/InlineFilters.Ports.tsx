import React from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';

const InlineFiltersPorts = () => {
    const { ports, removePort } = usePorts();

    return (
        <>
            {ports?.map((port, index) => (
                <InlineFilters.PortWrapper key={index} active>
                    <InlineFilters.Section
                        label={`Port ${index + 1}`}
                        body={<InlineFilters.VoltageType index={index} />}
                        onRemove={() => removePort(index)}
                        active={false}
                    />

                    {port.purpose !== undefined && <InlineFilters.Purpose index={index} />}
                    {port.voltage && <InlineFilters.Voltage index={index} />}
                    {port.current && <InlineFilters.Current index={index} />}
                    {port.power && <InlineFilters.Power index={index} />}
                    {port.powerFlowDirection && <InlineFilters.PowerFlowDirection index={index} />}
                    {port.isolated !== undefined && <InlineFilters.Isolated index={index} />}
                </InlineFilters.PortWrapper>
            ))}
        </>
    );
};

export { InlineFiltersPorts };
