import type { FC } from 'react';

import { Flex, Menu } from '@mantine/core';
import { IoCalendarOutline, IoCheckmarkOutline } from 'react-icons/io5';

import { Event } from 'models';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

import { useActiveEvents } from 'hooks/use-active-events';
import { useLocalEvent } from 'hooks/use-local-event';

const InlineFiltersEvent: FC<{
    search: () => Promise<void>;
}> = ({ search }) => {
    const { events } = useActiveEvents();
    const { localEvent, setLocalEvent } = useLocalEvent();

    const onClick = (id: Event['id']) => {
        setLocalEvent(id);
        search().then();
    };

    const onRemove = () => {
        setLocalEvent(null);
        search().then();
    };

    return events.length ? (
        <InlineFilters.Section
            label={
                localEvent ? (
                    <Flex mr={4} style={{ transform: 'translateY(4px)' }}>
                        <IoCalendarOutline />
                    </Flex>
                ) : (
                    <Flex align="center" gap={8}>
                        <IoCalendarOutline /> Event
                    </Flex>
                )
            }
            body={localEvent?.name}
            onRemove={localEvent ? onRemove : undefined}
            active={Boolean(localEvent)}
            popoverContent={
                <>
                    <Menu.Label>Events</Menu.Label>
                    {events.map(({ id, name }) => (
                        <Menu.Item
                            key={id}
                            onClick={() => onClick(id)}
                            leftSection={localEvent?.id === id ? <IoCheckmarkOutline /> : null}
                        >
                            {name}
                        </Menu.Item>
                    ))}
                </>
            }
        />
    ) : null;
};

export { InlineFiltersEvent };
