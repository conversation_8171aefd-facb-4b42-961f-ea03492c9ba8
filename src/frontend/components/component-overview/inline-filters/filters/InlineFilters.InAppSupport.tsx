import type { FC } from 'react';

import { Group } from '@mantine/core';
import { IoChatbubblesOutline } from 'react-icons/io5';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersInAppSupport: FC = () => {
    const { inAppSupport } = useComponentSearchFilters();

    const formattedValue = inAppSupport ? 'With support' : 'No support';

    const onRemove = () => {
        SearchService.setFilter('inAppSupport', undefined);
    };

    if (!inAppSupport) return null;

    return (
        <InlineFilters.Section
            label={
                <Group align="center">
                    <IoChatbubblesOutline />
                </Group>
            }
            body={formattedValue}
            onRemove={onRemove}
            active
        />
    );
};

export { InlineFiltersInAppSupport };
