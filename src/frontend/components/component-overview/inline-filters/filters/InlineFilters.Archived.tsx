import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersArchived: FC = () => {
    const { includeArchived } = useComponentSearchFilters();

    const formattedValue = includeArchived ? 'Yes' : 'No';

    const onRemove = () => {
        SearchService.setFilter('includeArchived', false);
    };

    if (!includeArchived) return null;

    return <InlineFilters.Section label="Inlcude archived" body={formattedValue} onRemove={onRemove} active />;
};

export { InlineFiltersArchived };
