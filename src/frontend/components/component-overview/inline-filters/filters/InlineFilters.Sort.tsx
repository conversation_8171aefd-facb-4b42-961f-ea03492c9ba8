import { useState } from 'react';

import { Menu } from '@mantine/core';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { ComponentQuery, Option, SortType } from 'models';

const InlineFiltersSort = () => {
    const { search, sort, type, manufacturer } = useComponentSearchFilters();
    const [previousSearch, setPreviousSearch] = useState(search);

    if (search !== previousSearch) {
        if (search) {
            SearchService.setFilter('sort', SortType.RELEVANCE);
        }

        setPreviousSearch(search);
        return null;
    }

    const sortOptions: Option<SortType>[] = [
        { label: 'Relevance', value: SortType.RELEVANCE },
        { label: 'Name', value: SortType.NAME },
    ];

    if (!type) {
        sortOptions.push({ label: 'Component Type', value: SortType.TYPE });
    }

    if (!manufacturer) {
        sortOptions.push({ label: 'Manufacturer', value: SortType.MANUFACTURER });
    }

    const selectedOption = sortOptions.find((option) => option.value === sort);

    return (
        <InlineFilters.Section
            label="Sort"
            body={selectedOption?.label || 'Choose'}
            popoverContent={sortOptions.map(({ value, label }) => (
                <Menu.Item key={value} onClick={() => SearchService.setFilter('sort', value as ComponentQuery['sort'])}>
                    {label}
                </Menu.Item>
            ))}
            active={!!sort}
        />
    );
};

export { InlineFiltersSort };
