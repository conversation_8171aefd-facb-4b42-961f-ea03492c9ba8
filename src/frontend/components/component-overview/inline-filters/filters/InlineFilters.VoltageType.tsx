import { FC } from 'react';

import { Text } from '@mantine/core';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { SearchService } from 'components/component-overview/services/SearchService';

const InlineFiltersVoltageType: FC<{
    index: number;
}> = ({ index }) => {
    const { ports } = useComponentSearchFilters();
    const voltageType = ports ? ports[index]?.voltageType : undefined;

    return (
        <InlineFilters.Switcher
            value={voltageType}
            placeholder="Voltage type"
            options={[
                {
                    value: 'AC',
                    label: 'AC',
                    render: (
                        <Text inherit c="var(--inline-filters-AC-color)">
                            AC
                        </Text>
                    ),
                },
                {
                    value: 'DC',
                    label: 'DC',
                    render: (
                        <Text inherit c="var(--inline-filters-DC-color)">
                            DC
                        </Text>
                    ),
                },
                {
                    value: undefined,
                    label: 'AC or DC',
                    render: (
                        <Text inherit>
                            <Text c="var(--inline-filters-AC-color)" span>
                                AC
                            </Text>{' '}
                            or{' '}
                            <Text c="var(--inline-filters-DC-color)" span>
                                DC
                            </Text>
                        </Text>
                    ),
                },
            ]}
            handleClick={(value) =>
                SearchService.setFilter(
                    'ports',
                    ports?.map((port, i) => (i === index ? { ...port, voltageType: value } : port)),
                )
            }
        />
    );
};

export { InlineFiltersVoltageType };
