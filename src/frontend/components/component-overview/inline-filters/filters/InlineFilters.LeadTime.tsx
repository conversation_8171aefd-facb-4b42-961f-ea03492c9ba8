import React from 'react';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { IoTimerOutline } from 'react-icons/io5';

const InlineFiltersLeadTime = () => {
    const { hasLeadTime } = useComponentSearchFilters();

    const onRemove = () => {
        SearchService.setFilter('hasLeadTime', undefined);
    };

    return (
        <InlineFilters.Section
            icon={<IoTimerOutline />}
            label={!hasLeadTime && 'Has lead time'}
            body={hasLeadTime && 'Has lead time'}
            wrapperProps={{
                onClick: () => {
                    SearchService.setFilter('hasLeadTime', hasLeadTime ? undefined : true);
                },
            }}
            onRemove={hasLeadTime ? onRemove : undefined}
            active={!!hasLeadTime}
        />
    );
};

export { InlineFiltersLeadTime };
