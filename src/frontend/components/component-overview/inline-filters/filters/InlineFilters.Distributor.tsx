import React, { FC, useEffect } from 'react';

import { Box, Group, Menu, Text } from '@mantine/core';
import { IoBriefcaseOutline } from 'react-icons/io5';

import { CompanyService } from 'models';

import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { CompanyLogo } from 'components/company-logo';
import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

type Props = {
    isViewOnly?: boolean;
};

const InlineFiltersDistributor: FC<Props> = ({ isViewOnly }) => {
    const { distributor: selectedDistributorId } = useComponentSearchFilters();

    const { companies: distributors, isLoading } = useCompanyProfiles({
        services: [CompanyService.DISTRIBUTION],
    });

    const selectedDistributor = selectedDistributorId
        ? distributors.find((distributor) => distributor.id === selectedDistributorId)
        : null;

    const onSelect = (id: string) => {
        SearchService.setFilter('distributor', id);
    };

    const onRemove = () => {
        SearchService.setFilter('distributor', undefined);
    };

    useEffect(() => {
        if (selectedDistributorId && !selectedDistributor) {
            SearchService.setFilter('distributor', undefined);
        }
    }, [selectedDistributor]);

    if (!selectedDistributorId && selectedDistributorId !== '') return null;
    if (!isLoading && !distributors.length) return null;

    return (
        <InlineFilters.Section
            isViewOnly={isViewOnly}
            active={!!selectedDistributor}
            label={
                selectedDistributor ? undefined : (
                    <Group gap={8}>
                        <IoBriefcaseOutline size={12} />
                        Distributor
                    </Group>
                )
            }
            body={
                selectedDistributor ? (
                    <Group gap={8}>
                        <CompanyLogo logos={selectedDistributor?.logos} width={20} />
                        {selectedDistributor?.name}
                    </Group>
                ) : null
            }
            onRemove={onRemove}
            popoverContent={
                isLoading ? (
                    <Menu.Item disabled>
                        <Text c="dimmed">Loading...</Text>
                    </Menu.Item>
                ) : (
                    <>
                        <Menu.Label>Distributor</Menu.Label>
                        {distributors.map((distributor) => (
                            <Menu.Item
                                key={distributor.id}
                                onClick={() => onSelect(distributor.id)}
                                leftSection={
                                    <CompanyLogo
                                        logos={distributor.logos}
                                        width={20}
                                        fallback={<Box w={20} h={20} bg="gray.1" />}
                                    />
                                }
                            >
                                {distributor.name}
                            </Menu.Item>
                        ))}
                    </>
                )
            }
        />
    );
};

export { InlineFiltersDistributor };
