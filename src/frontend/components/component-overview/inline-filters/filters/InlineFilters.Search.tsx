import React from 'react';

import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { InlineFiltersSearch as Component } from 'components/inline-filters/InlineFilters.Search';

const InlineFiltersSearch = () => {
    const { search } = useComponentSearchFilters();

    const handleRemove = () => {
        SearchService.setFilter('search', undefined);
    };

    const handleChange = (value: string) => {
        SearchService.setFilter('search', value);
    };

    return <Component search={search} onRemove={handleRemove} onChange={handleChange} />;
};
export { InlineFiltersSearch };
