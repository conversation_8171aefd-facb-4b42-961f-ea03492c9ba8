import React from 'react';

import { Menu } from '@mantine/core';

import { voltageConverter, VoltageType, voltageTypes, WireSize } from 'models';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { FormatHelpers } from 'helpers/formatters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { LinesField } from 'components/component-fields/LinesFields';
import { VoltageField } from 'components/component-fields/VoltageField';
import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersCable = () => {
    const { type } = useComponentSearchFilters();

    if (type !== 'cable') return null;

    return (
        <>
            <InlineFiltersWireSize />
            <InlineFiltersVoltage />
            <InlineFiltersLines />
        </>
    );
};

const InlineFiltersWireSize = () => {
    const { cable } = useComponentSearchFilters();

    const selectedWireSize = WireSize.options.find((option) => option.value === cable?.wireSize);

    const onSelect = (wireSize: WireSize) => {
        if (!cable) return;

        SearchService.setFilter('cable', {
            ...cable,
            wireSize,
        });
    };

    const onRemove = () => {
        if (!cable) return;

        SearchService.setFilter('cable', {
            ...cable,
            wireSize: undefined,
        });
    };

    return (
        <InlineFilters.Section
            label={selectedWireSize ? '' : 'Wire size'}
            body={selectedWireSize?.label}
            active={!!cable?.wireSize}
            popoverContent={
                <>
                    {WireSize.options.map((option) => (
                        <Menu.Item key={option.value} onClick={() => onSelect(option.value)}>
                            {option.label}
                        </Menu.Item>
                    ))}
                </>
            }
            onRemove={selectedWireSize ? onRemove : undefined}
        />
    );
};

const InlineFiltersVoltage = () => {
    const { cable } = useComponentSearchFilters();

    const label = FormatHelpers.formatMinNomMax({ ...cable?.voltage, unit: 'V' }, voltageConverter);

    const onRemove = () => {
        if (!cable) return;

        SearchService.setFilter('cable', {
            ...cable,
            voltage: undefined,
        });
    };

    return (
        <InlineFilters.Section
            body={
                <InlineFilters.MeasurementWrapper placeholder="Cable max. voltage" label={label}>
                    <VoltageField name="cable.voltage" size="xs" fields={['max']} hideIcons />
                </InlineFilters.MeasurementWrapper>
            }
            active={!!label}
            onRemove={label ? onRemove : undefined}
        />
    );
};

const InlineFiltersVoltageType = () => {
    const { cable } = useComponentSearchFilters();
    const voltageType = cable?.voltageType;

    const onSelect = (voltageType: VoltageType) => {
        if (!cable) return;

        SearchService.setFilter('cable', {
            ...cable,
            voltageType,
        });
    };

    return (
        <InlineFilters.Section
            popoverContent={
                <>
                    {voltageTypes.map((option) => (
                        <Menu.Item key={option} onClick={() => onSelect(option)}>
                            {option}
                        </Menu.Item>
                    ))}
                </>
            }
            body={voltageType}
            active={false}
        />
    );
};

const InlineFiltersLines = () => {
    const { cable } = useComponentSearchFilters();

    const voltageType = cable?.voltageType;

    const onRemove = () => {
        if (!cable) return;

        SearchService.setFilter('cable', {
            ...cable,
            lines: undefined,
        });
    };

    const checkedNbLines = voltageType
        ? Object.values(cable?.lines?.[voltageType] ?? {}).filter((value) => !!value).length
        : 0;

    return (
        <InlineFilters.PortWrapper active={true}>
            <InlineFiltersVoltageType />
            {voltageType && (
                <InlineFilters.Section
                    body={<LinesField name="cable.lines" voltageType={voltageType} />}
                    active={false}
                    onRemove={checkedNbLines > 0 ? onRemove : undefined}
                />
            )}
        </InlineFilters.PortWrapper>
    );
};

export { InlineFiltersCable };
