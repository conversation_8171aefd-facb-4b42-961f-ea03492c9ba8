import type { FC } from 'react';

import { Menu } from '@mantine/core';
import { IoCheckmark } from 'react-icons/io5';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { FormatHelpers } from 'helpers/formatters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersIsolated: FC<{
    index: number;
}> = ({ index }) => {
    const { ports } = useComponentSearchFilters();
    const isolated = ports ? ports[index]?.isolated : undefined;

    const { updatePort, removePortFilter } = usePorts();

    const setIsolated = (value: boolean) => {
        updatePort(index, { isolated: value });
    };

    return (
        <InlineFilters.Section
            body={FormatHelpers.formatIsolation(isolated)}
            active={!!isolated}
            onRemove={() => removePortFilter(index, 'isolated')}
            popoverContent={
                <>
                    <Menu.Item onClick={() => setIsolated(true)} rightSection={isolated && <IoCheckmark />}>
                        Is isolated
                    </Menu.Item>
                    <Menu.Item onClick={() => setIsolated(false)} rightSection={!isolated && <IoCheckmark />}>
                        Not isolated
                    </Menu.Item>
                </>
            }
        />
    );
};

export { InlineFiltersIsolated };
