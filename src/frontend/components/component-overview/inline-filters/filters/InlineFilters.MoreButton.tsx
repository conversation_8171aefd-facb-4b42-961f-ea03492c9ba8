import type { FC } from 'react';

import { Menu } from '@mantine/core';

import { SearchService } from 'components/component-overview/services/SearchService';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { FiltersDropdown } from 'components/component-overview/components/FiltersDropdown';
import { InlineFiltersMoreButton as Component } from 'components/inline-filters/InlineFilters.MoreButton';

const InlineFiltersMoreButton: FC = () => {
    const filters = useComponentSearchFilters();

    const showClearButton = !SearchService.isInitialSearch(filters);

    return (
        <Component
            showClearButton={showClearButton}
            onClear={SearchService.resetFilters}
            MoreFilters={
                <>
                    <Menu.Label>Filters</Menu.Label>
                    <FiltersDropdown />
                </>
            }
        />
    );
};

export { InlineFiltersMoreButton };
