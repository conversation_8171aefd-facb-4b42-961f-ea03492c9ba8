import type { FC } from 'react';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { InlineFiltersClearButton as Component } from 'components/inline-filters/InlineFilters.ClearButton';

const InlineFiltersClearButton: FC = () => {
    const filters = useComponentSearchFilters();

    const showClearButton = !SearchService.isInitialSearch(filters);

    return showClearButton ? <Component onClick={() => SearchService.resetFilters()} /> : null;
};

export { InlineFiltersClearButton };
