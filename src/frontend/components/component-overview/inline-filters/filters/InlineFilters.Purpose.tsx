import type { FC } from 'react';

import { Flex, Group, Menu } from '@mantine/core';

import { all, PortPurpose, PortPurposeOption } from 'models';

import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';
import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersPurpose: FC<{
    index: number;
}> = ({ index }) => {
    const { ports } = useComponentSearchFilters();
    const purpose = ports ? ports[index]?.purpose : undefined;

    const { removePortFilter, updatePort } = usePorts();

    const setPurpose = (purpose: PortPurposeOption) => {
        updatePort(index, { purpose });
    };

    const removePurpose = () => {
        removePortFilter(index, 'purpose');
    };

    const selectedOption = purpose ? all[purpose] : null;

    return (
        <InlineFilters.Section
            label={selectedOption ? null : 'Purpose'}
            body={
                selectedOption ? (
                    <Group gap={8}>
                        <Flex
                            align="center"
                            justify="center"
                            style={{ width: 16, color: 'var(--mantine-color-gray-6' }}
                        >
                            <ComponentIcon type={selectedOption.type} />
                        </Flex>
                        {selectedOption.name}
                    </Group>
                ) : null
            }
            active={!!purpose}
            onRemove={removePurpose}
            popoverContent={
                <>
                    <Menu.Label>Port purpose</Menu.Label>
                    {PortPurpose.options.map((value) => (
                        <Menu.Item
                            key={value}
                            onClick={() => setPurpose(value)}
                            leftSection={
                                <Flex
                                    align="center"
                                    justify="center"
                                    style={{ width: 16, color: 'var(--mantine-color-gray-7)' }}
                                >
                                    <ComponentIcon type={value} />
                                </Flex>
                            }
                        >
                            {all[value].name}
                        </Menu.Item>
                    ))}
                </>
            }
        />
    );
};

export { InlineFiltersPurpose };
