import type { FC } from 'react';

import { Flex, Menu } from '@mantine/core';
import { IoCheckmarkCircleOutline, IoCheckmarkOutline } from 'react-icons/io5';

import { Compliance, Compliances } from 'models';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersCompliances: FC = () => {
    const { compliances } = useComponentSearchFilters();

    const onClick = (value: Compliance) => {
        SearchService.setFilter(
            'compliances',
            compliances?.includes(value)
                ? compliances.filter((compliance) => compliance !== value)
                : [...(compliances ?? []), value],
        );
    };

    const onRemove = () => {
        SearchService.setFilter('compliances', undefined);
    };

    if (!compliances) return null;

    const formattedValue = Compliances.options
        .filter(({ value }) => compliances?.includes(value))
        .map(({ label }) => label)
        .join(', ');

    return (
        <InlineFilters.Section
            label={
                formattedValue ? (
                    <Flex mr={4} style={{ transform: 'translateY(4px)' }}>
                        <IoCheckmarkCircleOutline />
                    </Flex>
                ) : (
                    <Flex align="center" gap={8}>
                        <IoCheckmarkCircleOutline /> Compliance
                    </Flex>
                )
            }
            body={formattedValue}
            onRemove={onRemove}
            active={!!compliances?.length}
            popoverContent={
                <>
                    <Menu.Label>Compliance and organizations</Menu.Label>
                    {Compliances.options.map(({ value, label }) => (
                        <Menu.Item
                            key={value}
                            onClick={() => onClick(value)}
                            leftSection={compliances?.includes(value) ? <IoCheckmarkOutline /> : null}
                        >
                            {label}
                        </Menu.Item>
                    ))}
                </>
            }
        />
    );
};

export { InlineFiltersCompliances };
