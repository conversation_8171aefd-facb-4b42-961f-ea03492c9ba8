import type { FC } from 'react';

import { Menu } from '@mantine/core';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { IoCheckmarkOutline } from 'react-icons/io5';
import { ComponentApplication, ComponentApplicationOptions } from 'models';

const InlineFiltersApplication: FC = () => {
    const { application } = useComponentSearchFilters();

    const onClick = (value: ComponentApplication) => {
        SearchService.setFilter(
            'application',
            application?.includes(value)
                ? application.filter((_application) => _application !== value)
                : [...(application ?? []), value],
        );
    };

    const formattedValue = ComponentApplicationOptions.filter(({ value }) => application?.includes(value))
        .map(({ label }) => label)
        .join(', ');

    return (
        <InlineFilters.Section
            label={!application?.length ? 'Application' : ''}
            body={formattedValue}
            active={!!application?.length}
            popoverContent={
                <>
                    <Menu.Label>Application</Menu.Label>
                    {ComponentApplicationOptions.map(({ value, label }) => (
                        <Menu.Item
                            key={value}
                            onClick={() => onClick(value)}
                            leftSection={application?.includes(value) ? <IoCheckmarkOutline /> : null}
                        >
                            {label}
                        </Menu.Item>
                    ))}
                </>
            }
            onRemove={application?.length ? () => SearchService.setFilter('application', []) : undefined}
        />
    );
};

export { InlineFiltersApplication };
