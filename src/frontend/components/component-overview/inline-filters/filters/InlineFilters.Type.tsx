import React, { FC, useEffect } from 'react';

import { Box, Group, Menu } from '@mantine/core';

import { ComponentCount, all } from 'models';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { ComponentIcon } from 'components/component-icons/ComponentIcon';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import cx from './InlineFilters.Type.module.scss';

const InlineFiltersType: FC<{ componentCount?: ComponentCount; isViewOnly?: boolean }> = ({
    componentCount,
    isViewOnly,
}) => {
    const { type } = useComponentSearchFilters();

    const enabledComponents = Object.keys(componentCount || {})
        // @ts-ignore
        .filter((key) => componentCount?.[key] > 0)
        .filter(Boolean);

    const data = Object.values(all)
        .filter((component) => !enabledComponents.length || enabledComponents.includes(component.type))
        .map((component) => ({
            value: component.type,
            label: component.name,
        }));

    const selectedOption = data.find((option) => {
        return option.value === type;
    });

    useEffect(() => {
        if (type && !selectedOption) {
            SearchService.setFilter('type', undefined);
        }
    }, [selectedOption]);

    const onRemove = () => {
        SearchService.setFilter('type', undefined);
    };

    if (!data.length) return null;

    return (
        <InlineFilters.Section
            isViewOnly={isViewOnly}
            active={!!type}
            label={selectedOption ? undefined : 'Component Type'}
            body={
                selectedOption ? (
                    <Group gap={8} className={cx.root}>
                        <Box style={{ width: 16, height: 16, color: 'var(--mantine-color-gray-6' }}>
                            <ComponentIcon type={selectedOption.value} />
                        </Box>
                        {selectedOption.label}
                    </Group>
                ) : null
            }
            onRemove={selectedOption ? onRemove : undefined}
            popoverContent={
                <>
                    <Menu.Label>Component Type</Menu.Label>
                    {data.map(({ value, label }) => (
                        <Menu.Item
                            key={value}
                            onClick={() => SearchService.setFilter('type', value)}
                            leftSection={
                                <Box
                                    className={cx.root}
                                    style={{
                                        width: 16,
                                        height: 16,
                                        color: 'var(--mantine-color-gray-7)',
                                        overflow: 'hidden',
                                    }}
                                >
                                    <ComponentIcon type={value} />
                                </Box>
                            }
                        >
                            {label}
                        </Menu.Item>
                    ))}
                </>
            }
        />
    );
};

export { InlineFiltersType };
