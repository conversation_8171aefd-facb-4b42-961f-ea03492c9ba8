import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { PowerField } from 'components/component-fields/PowerField';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { FormatHelpers } from 'helpers/formatters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { powerConverter } from 'models';

const InlineFiltersPower: FC<{
    index: number;
}> = ({ index }) => {
    const name = `ports[${index}].power`;

    const { ports } = useComponentSearchFilters();
    const power = ports ? ports[index]?.power : undefined;

    const label = FormatHelpers.formatMinNomMax({ ...power, unit: 'W' }, powerConverter);

    const { removePortFilter } = usePorts();

    return (
        <InlineFilters.Section
            body={
                <InlineFilters.MeasurementWrapper label={label} placeholder="Power">
                    <PowerField name={name} size="xs" hideIcons fields={['nom']} />
                </InlineFilters.MeasurementWrapper>
            }
            onRemove={() => removePortFilter(index, 'power')}
            active={!!power}
        />
    );
};

export { InlineFiltersPower };
