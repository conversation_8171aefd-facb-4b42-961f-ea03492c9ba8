import React from 'react';

import { Menu } from '@mantine/core';
import { IoCheckmarkOutline } from 'react-icons/io5';
import { BsPlug } from 'react-icons/bs';

import { ChargerConnector, ChargerConnectorType, ComponentQuery } from 'models';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

const InlineFiltersCharging = () => {
    const { type, charging } = useComponentSearchFilters();

    const selectedChargingTypes = ChargerConnector.options
        .filter((option) => charging?.connectors?.includes(option.value as ChargerConnectorType))
        .map((option) => option.label)
        .join(', ');

    const toggleChargingType = (type: ChargerConnectorType) => {
        SearchService.setFilter('charging', {
            ...charging,
            connectors: charging?.connectors?.includes(type)
                ? charging.connectors.filter((connector) => connector !== type)
                : [...(charging?.connectors || []), type],
        });
    };

    const removeChargingFilter = (filter: keyof NonNullable<ComponentQuery['charging']>) => {
        const clonedCharging = charging ? { ...charging } : {};

        delete clonedCharging[filter];

        SearchService.setFilter('charging', clonedCharging);
    };

    if (type !== 'charger') return null;

    const filterIsActive = Boolean(
        charging?.connectors?.length || charging?.power?.value || charging?.numberOfConnectors,
    );

    return (
        <InlineFilters.PortWrapper active={filterIsActive}>
            <InlineFilters.Section
                icon={<BsPlug />}
                label={!filterIsActive && 'Charging'}
                body={filterIsActive && 'Charging'}
                active={filterIsActive}
            />
            <InlineFilters.Section
                label={selectedChargingTypes ? '' : 'Connector Type'}
                body={selectedChargingTypes}
                onRemove={selectedChargingTypes ? () => removeChargingFilter('connectors') : undefined}
                popoverContent={
                    <>
                        <Menu.Label>Connector Type</Menu.Label>
                        {ChargerConnector.options.map(({ value, label }) => (
                            <Menu.Item
                                key={value}
                                onClick={() => toggleChargingType(value as ChargerConnectorType)}
                                leftSection={
                                    charging?.connectors?.includes(value as ChargerConnectorType) ? (
                                        <IoCheckmarkOutline />
                                    ) : null
                                }
                            >
                                {label}
                            </Menu.Item>
                        ))}
                    </>
                }
                active={filterIsActive}
            />

            <InlineFilters.MeasurementValue
                type="power"
                name="charging.power"
                value={charging?.power?.value}
                placeholder="Power"
                onRemove={charging?.power?.value ? () => removeChargingFilter('power') : undefined}
                active={filterIsActive}
            />

            <InlineFilters.Number
                name="charging.numberOfConnectors"
                value={charging?.numberOfConnectors}
                formattedValue={charging?.numberOfConnectors ? charging?.numberOfConnectors + ' connectors' : undefined}
                label="# Connectors"
                onRemove={charging?.numberOfConnectors ? () => removeChargingFilter('numberOfConnectors') : undefined}
                active={filterIsActive}
            />
        </InlineFilters.PortWrapper>
    );
};

export { InlineFiltersCharging };
