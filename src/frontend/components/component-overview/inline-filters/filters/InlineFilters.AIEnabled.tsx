import type { FC } from 'react';

import { Flex } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersAIEnabled: FC = () => {
    const { aiEnabled } = useComponentSearchFilters();

    const formattedValue = aiEnabled ? 'AI enabled' : 'AI not enabled';

    const onRemove = () => {
        SearchService.setFilter('aiEnabled', false);
    };

    if (!aiEnabled) return null;

    return (
        <InlineFilters.Section
            label={
                <Flex align="center">
                    <BsRobot />
                </Flex>
            }
            body={formattedValue}
            onRemove={onRemove}
            active
        />
    );
};

export { InlineFiltersAIEnabled };
