import type { FC } from 'react';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { usePorts } from 'components/component-overview/inline-filters/hooks/usePorts';
import { FormatHelpers } from 'helpers/formatters';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

const InlineFiltersPowerFlowDirection: FC<{
    index: number;
}> = ({ index }) => {
    const { ports } = useComponentSearchFilters();
    const powerFlowDirection = ports ? ports[index]?.powerFlowDirection : undefined;

    const { removePortFilter } = usePorts();

    if (!powerFlowDirection) return null;

    return (
        <InlineFilters.Section
            body={FormatHelpers.formatPowerFlowDirection(powerFlowDirection)}
            onRemove={() => removePortFilter(index, 'powerFlowDirection')}
            active={!!powerFlowDirection}
        />
    );
};

export { InlineFiltersPowerFlowDirection };
