import { range } from 'radash';

import { ComponentDefinition, getComponentDefinition, getComponentPayloadValidator, PortQuery } from 'models';

import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

type PortFilterType = Omit<PortQuery, 'voltageType'>;

const usePorts = () => {
    const { ports, type } = useComponentSearchFilters();

    const definition: ComponentDefinition | undefined = type ? getComponentDefinition(type) : undefined;

    const definitionMaxNbPorts = definition?.ports?.max ?? Infinity;
    const maxNbPorts = Math.min(definitionMaxNbPorts, 3);
    const hasMaxNbPorts = ports?.length === maxNbPorts;

    const addPort = (filters?: PortFilterType) => {
        if (hasMaxNbPorts) return;

        SearchService.setFilter('ports', [...(ports || []), { voltageType: 'DC', ...filters }]);
    };

    const updatePort = (index: number, filters?: PortFilterType) => {
        if (!ports) return;
        if (!ports[index]) return;

        const updatedPort = { ...ports[index], ...filters };

        // if every filter is undefined, remove the port
        if (Object.values(updatedPort).every((filter) => filter === undefined)) {
            removePort(index);
            return;
        }

        SearchService.setFilter(
            'ports',
            ports.map((port, _index) => (_index === index ? updatedPort : port)),
        );
    };

    const removePort = (index: number) => {
        if (!ports) return;
        if (ports?.length === 0) return;

        SearchService.setFilter('ports', [...ports.slice(0, index), ...ports.slice(index + 1)]);
    };

    const addPortFilter = (index: number, filter: keyof PortFilterType, defaultValue: any = {}) => {
        const isExistingIndex = ports?.[index];

        if (isExistingIndex) {
            updatePort(index, { [filter]: defaultValue });
            return;
        }

        addPort({ [filter]: defaultValue });
    };

    const removePortFilter = (index: number, filter: keyof PortFilterType) => {
        const isExistingIndex = ports?.[index];

        if (!isExistingIndex) return;

        updatePort(index, { [filter]: undefined });
    };

    const getPortAddFilters = (index: number) => {
        const currentPort = ports?.[index];

        let showPowerFlowDirectionFilter = false;

        if (type) {
            const definition = getComponentDefinition(type);

            if (definition.ports.powerFlowDirections.includes('bidirectional')) {
                showPowerFlowDirectionFilter = true;
            }
        }

        let showPurposeFilter = false;

        if (type) {
            const componentValidator = getComponentPayloadValidator(type);
            const port = componentValidator.parse({ name: '' }).electrical.ports[0];

            if ('purpose' in port) {
                showPurposeFilter = true;
            }
        }

        const addFilters: {
            type: keyof PortFilterType;
            label: string;
            defaultValue?: any;
            addLabel?: string;
        }[] = [
            { type: 'voltage', label: 'voltage' },
            { type: 'current', label: 'current' },
            { type: 'power', label: 'power' },
            ...(showPurposeFilter
                ? [
                      {
                          type: 'purpose' as keyof PortFilterType,
                          label: 'purpose',
                          defaultValue: null,
                      },
                  ]
                : []),
            ...(showPowerFlowDirectionFilter
                ? [
                      {
                          type: 'powerFlowDirection' as keyof PortFilterType,
                          label: 'power flow direction',
                          defaultValue: 'bidirectional',
                          addLabel: 'Is bidirectional',
                      },
                  ]
                : []),
            {
                type: 'isolated',
                label: 'isolation',
                defaultValue: true,
                addLabel: 'Is isolated',
            },
        ];

        if (!currentPort) return addFilters;

        // if filter key exists in currentPort, remove from possibleFilters
        Object.keys(currentPort).forEach((key) => {
            const filterKey = key as keyof PortFilterType;

            const filterIndex = addFilters.findIndex(({ type }) => type === filterKey);

            if (currentPort[filterKey] && filterIndex >= 0) {
                addFilters.splice(filterIndex, 1);
            }
        });

        return addFilters;
    };

    const nbUnfilteredPorts = [...range(maxNbPorts - 1)].filter((index) => getPortAddFilters(index).length > 0).length;

    return {
        ports,
        maxNbPorts,
        nbUnfilteredPorts,
        addPort,
        removePort,
        updatePort,
        addPortFilter,
        removePortFilter,
        getPortAddFilters,
    };
};

export { usePorts };
