import React from 'react';

import { useCaseStudySearch } from 'components/case-study-search/hooks/use-case-study-search';

import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';

import { InlineFiltersSearch as Component } from 'components/inline-filters/InlineFilters.Search';

const InlineFiltersSearch = () => {
    const {
        filters: { search },
    } = useCaseStudySearch();

    const handleRemove = () => {
        CaseStudySearchService.setFilter('search', undefined);
    };

    const handleChange = (value: string) => {
        CaseStudySearchService.setFilter('search', value);
    };

    return <Component search={search} onRemove={handleRemove} onChange={handleChange} />;
};

export { InlineFiltersSearch };
