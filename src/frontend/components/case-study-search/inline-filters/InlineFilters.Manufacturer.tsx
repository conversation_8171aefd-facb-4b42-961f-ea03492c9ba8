import React from 'react';

import { IoBriefcaseOutline } from 'react-icons/io5';

import { PublishedStatus } from 'models';

import { CaseStudySearchService } from '../services/CaseStudySearchService';

import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { useCaseStudySearch } from '../hooks/use-case-study-search';

import { InlineFilters } from './InlineFilters';

const InlineFiltersManufacturer = () => {
    const {
        filters: { manufacturer },
    } = useCaseStudySearch();

    const { companies } = useCompanyProfiles({ status: PublishedStatus.PUBLISHED });

    const selectedManufacturers = companies.filter(({ id }) =>
        typeof manufacturer === 'string' ? id === manufacturer : manufacturer?.includes(id),
    );

    if (!selectedManufacturers.length) return null;

    return (
        <InlineFilters.SectionWithIcon
            icon={<IoBriefcaseOutline />}
            onRemove={() => CaseStudySearchService.setFilter('manufacturer', undefined)}
            body={selectedManufacturers ? selectedManufacturers.map(({ name }) => name).join(', ') : undefined}
            active
        />
    );
};
export { InlineFiltersManufacturer };
