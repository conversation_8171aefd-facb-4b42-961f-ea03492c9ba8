import type { FC } from 'react';

import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';

import { useCaseStudySearch } from 'components/case-study-search/hooks/use-case-study-search';

import { InlineFiltersMoreButton as Component } from 'components/inline-filters/InlineFilters.MoreButton';

const InlineFiltersMoreButton: FC = () => {
    const { filters } = useCaseStudySearch();

    const showClearButton = !CaseStudySearchService.isInitialSearch(filters);

    if (!showClearButton) return null;

    return <Component showClearButton onClear={CaseStudySearchService.resetFilters} />;
};

export { InlineFiltersMoreButton };
