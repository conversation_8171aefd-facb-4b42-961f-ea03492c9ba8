import React, { FC } from 'react';

import { Form } from 'components/forms/Form';

import { InlineFiltersWrapper, InlineFiltersWrapperProps } from 'components/inline-filters/InlineFilters.Wrapper';

import { InlineFiltersSearch } from './InlineFilters.Search';
import { InlineFiltersMoreButton } from './InlineFilters.MoreButton';
import { InlineFiltersManufacturer } from './InlineFilters.Manufacturer';
import { InlineFiltersSectionWithIcon } from 'components/inline-filters/InlineFilters.SectionWithIcon';

const InlineFilters: FC<InlineFiltersWrapperProps> & {
    Search: typeof InlineFiltersSearch;
    More: typeof InlineFiltersMoreButton;
    SectionWithIcon: typeof InlineFiltersSectionWithIcon;
    Manufacturer: typeof InlineFiltersManufacturer;
} = (props) => {
    return (
        <Form onSubmit={() => {}}>
            <InlineFiltersWrapper variant="dark" {...props}>
                <InlineFilters.Search />
                <InlineFiltersManufacturer />
                <InlineFilters.More />
            </InlineFiltersWrapper>
        </Form>
    );
};

InlineFilters.Search = InlineFiltersSearch;
InlineFilters.More = InlineFiltersMoreButton;
InlineFilters.SectionWithIcon = InlineFiltersSectionWithIcon;
InlineFilters.Manufacturer = InlineFiltersManufacturer;

export { InlineFilters };
