import { proxy, subscribe } from 'valtio';

import { deepClone } from 'helpers/deep-clone';

import { CaseStudySearch } from 'components/case-study-search/types';
import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';

export const DEFAULT_CASE_STUDY_SEARCH: CaseStudySearch = {
    caseStudies: [],
    page: 0,
    totalPages: 1,
    totalResults: 0,
    isLoading: false,
    error: false,
    filters: {
        search: undefined,
        manufacturer: undefined,
    },
};

const caseStudySearch = proxy<CaseStudySearch>(deepClone(DEFAULT_CASE_STUDY_SEARCH));

subscribe(caseStudySearch, (changes) => {
    changes.forEach((change) => {
        const [type, path] = change;

        if (type === 'set' && path.includes('filters')) {
            caseStudySearch.page = 0;
            CaseStudySearchService.debouncedSearch();
        }

        if (type === 'set' && path.join('.') === 'page') {
            CaseStudySearchService.debouncedSearch();
        }
    });
});

export { caseStudySearch };
