import React from 'react';

import { Article } from 'models';

import { Loader } from 'components/search/components/Loader';
import { GridSection } from 'components/section/GridSection';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { CaseStudyHit } from 'components/case-study-search/components/CaseStudyHit';

const CaseStudyHits = ({ caseStudies = [], isLoading }: { caseStudies: Article[]; isLoading?: boolean }) => {
    if (isLoading) {
        return <Loader />;
    }

    return caseStudies.length === 0 ? (
        <EmptyMessage>No case studies found matching your criteria</EmptyMessage>
    ) : (
        <GridSection nbCols={4}>
            {caseStudies.map((caseStudy: any, index) => (
                <CaseStudyHit caseStudy={caseStudy} position={index + 1} key={caseStudy.id} />
            ))}
        </GridSection>
    );
};

export { CaseStudyHits };
