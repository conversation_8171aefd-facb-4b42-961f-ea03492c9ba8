import React, { FC } from 'react';

import { Anchor, Box, Card, Stack, Title } from '@mantine/core';
import { IoImageOutline } from 'react-icons/io5';

import { Article, Stub as StubType } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { IKImage } from 'components/ik-image/IKImage';
import { RTEContent } from 'components/rte-content/RTEContent';
import { ImpressionTracker } from 'components/component-overview/components/ImpressionTracker';

import cx from 'components/company-profile/components/Stub.module.scss';

const CaseStudyHit: FC<{
    caseStudy: Article;
    position?: number;
    isExternalLink?: boolean;
    withBorder?: boolean;
}> = ({ caseStudy, position, isExternalLink, withBorder }) => {
    const renderStub = (
        <Stub
            name={caseStudy.name}
            description={caseStudy.teaser?.description}
            image={caseStudy.teaser?.image}
            url={
                caseStudy.company &&
                CompanyProfileHelpers.urls.view(caseStudy.company) + `?article=${caseStudy.id}#caseStudies`
            }
            isExternalLink={isExternalLink}
            withBorder={withBorder}
        />
    );

    if (position === undefined) {
        return renderStub;
    }

    return (
        <ImpressionTracker
            namespace="caseStudy.search"
            data={{
                articleId: caseStudy.id,
                position,
            }}
            key={caseStudy.id}
        >
            {renderStub}
        </ImpressionTracker>
    );
};

const Stub: FC<
    StubType & {
        url?: string;
        isExternalLink?: boolean;
        withBorder?: boolean;
    }
> = ({ url, name, description, image, isExternalLink, withBorder = true }) => {
    return (
        <Card withBorder={withBorder} className={cx.root} radius="sm">
            {image && (
                <Box
                    component="a"
                    className={cx.imageWrapper}
                    href={url}
                    target={isExternalLink ? '_blank' : undefined}
                >
                    <IKImage fileOrId={image as any} width={600} height={340} alt={name || ''} />
                </Box>
            )}

            {!image && (
                <Box
                    component="a"
                    className={`${cx.imageWrapper} ${cx.imagePreview}`}
                    href={url}
                    target={isExternalLink ? '_blank' : undefined}
                >
                    <IoImageOutline size={20} />
                </Box>
            )}

            <Stack className={cx.content} gap="sm">
                {name && (
                    <Anchor href={url} c="brand" target={isExternalLink ? '_blank' : undefined}>
                        <Title order={3} fz="lg" fw={700} c="brand">
                            {name}
                        </Title>
                    </Anchor>
                )}

                {description && (
                    <Box mah={101} style={{ overflow: 'hidden' }}>
                        <RTEContent content={description} />
                    </Box>
                )}
            </Stack>
        </Card>
    );
};

export { CaseStudyHit };
