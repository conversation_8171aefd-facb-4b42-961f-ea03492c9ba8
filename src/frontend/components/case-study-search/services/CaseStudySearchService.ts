import { publicConfig } from '@public-config';
import { debounce, isEqual } from 'radash';

import { Article, CaseStudyQuery, removeUndefined } from 'models';

import { ApiService } from 'services/ApiService';

import { caseStudySearch, DEFAULT_CASE_STUDY_SEARCH } from 'components/case-study-search/state/case-study-search';
import { CaseStudySearch } from 'components/case-study-search/types';

const CaseStudySearchService = {
    controller: null as AbortController | null,

    debouncedSearch: debounce({ delay: 500 }, () => {
        CaseStudySearchService.search().then();
    }),

    search: async () => {
        const { page, filters } = caseStudySearch;

        // cancel previous search calls
        if (caseStudySearch.isLoading) {
            CaseStudySearchService.controller?.abort();
        }

        caseStudySearch.isLoading = true;

        try {
            const controller = new AbortController();
            const { signal } = controller;
            CaseStudySearchService.controller = controller;

            const limit = 48;
            const sort = 'relevance';
            const query = {
                search: filters.search,
                manufacturer: filters.manufacturer,
                type: 'caseStudy',
            };

            const params = new URLSearchParams({
                query: JSON.stringify(query),
                sort,
                page: page.toString(),
            });

            if (limit) {
                params.set('limit', limit.toString());
            }

            const result = await ApiService.get(`${publicConfig.urls.api}/articles/search?${params.toString()}`, {
                signal,
            });

            caseStudySearch.caseStudies = result.docs.filter((caseStudy: Article) => caseStudy.company) ?? [];
            caseStudySearch.totalPages = result.totalPages;
            if (!CaseStudySearchService.isInitialSearch(filters)) {
                caseStudySearch.totalResults = result.totalResults;
            }
            caseStudySearch.isLoading = false;

            return result;
        } catch (error: any) {
            if (error?.name === 'AbortError') {
                return;
            }

            caseStudySearch.caseStudies = [];
            caseStudySearch.error = true;
            caseStudySearch.isLoading = false;
        }
    },

    setPage: (page: number) => {
        caseStudySearch.page = page;
    },

    setFilter: <FilterKey extends keyof CaseStudySearch['filters']>(
        key: FilterKey,
        value: CaseStudySearch['filters'][FilterKey],
    ) => {
        caseStudySearch.filters[key] = value;
    },

    mergeFilters(filters: CaseStudyQuery) {
        Object.entries(filters).forEach(([key, value]) => {
            CaseStudySearchService.setFilter(key as keyof CaseStudyQuery, value);
        });
    },

    resetFilters: () => {
        CaseStudySearchService.mergeFilters(DEFAULT_CASE_STUDY_SEARCH.filters);
    },

    isInitialSearch: (filters: CaseStudySearch['filters']) => {
        return isEqual(removeUndefined(filters), removeUndefined(DEFAULT_CASE_STUDY_SEARCH.filters));
    },
};

export { CaseStudySearchService };
