import { useEffect } from 'react';

import { Pagination } from '@mantine/core';

import { HorizontalTabType } from 'components/horizontal-tabs';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { InlineFilters } from 'components/design-library/inline-filters/InlineFilters';
import { DesignLibraryHits } from 'components/design-library/components/DesignLibraryHits';

import { SearchTab } from 'components/search/components/SearchTab';
import { StickyFilters } from 'components/search/components/StickyFilters';
import { SearchTabName } from 'components/search/components/SearchTabName';
import { isEqual } from 'radash';
import { DEFAULT_DESIGN_LIBRARY_SEARCH } from 'components/design-library/state/design-library-search';

const DesignsLabel = () => {
    const { totalDesigns, isLoading, filters } = useDesignLibrarySearch();

    const hasSearched = !isEqual(filters, DEFAULT_DESIGN_LIBRARY_SEARCH.filters);

    const label = isLoading || !hasSearched ? undefined : totalDesigns;

    return <SearchTabName label={label}>Designs</SearchTabName>;
};

const Filters = () => {
    return (
        <>
            <InlineFilters variant="light" />
            <StickyFilters filters={<InlineFilters variant="dark" size="md" />} />
        </>
    );
};

const Designs = () => {
    const { docs, isLoading, page, totalPages } = useDesignLibrarySearch();
    const showPagination = !isLoading && totalPages > 1;

    // initial search
    useEffect(() => {
        DesignLibraryService.debouncedSearch();
    }, []);

    return (
        <SearchTab
            filters={<Filters />}
            body={<DesignLibraryHits designs={docs} isLoading={isLoading} />}
            pagination={({ scrollToTop }) =>
                showPagination ? (
                    <Pagination
                        value={page + 1}
                        onChange={(newPage) => {
                            DesignLibraryService.setPage(newPage - 1);
                            scrollToTop();
                        }}
                        total={totalPages}
                    />
                ) : null
            }
        />
    );
};

export const designs: HorizontalTabType = {
    value: 'designs',
    label: <DesignsLabel />,
    content: <Designs />,
};
