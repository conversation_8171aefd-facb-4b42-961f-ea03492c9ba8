import { HorizontalTabType } from 'components/horizontal-tabs';

import { SearchTab } from 'components/search/components/SearchTab';
import { StickyFilters } from 'components/search/components/StickyFilters';
import { SearchTabName } from 'components/search/components/SearchTabName';
import { IoHome } from 'react-icons/io5';

const OverviewLabel = () => {
    return (
        <SearchTabName>
            <IoHome />
        </SearchTabName>
    );
};

const Filters = () => {
    return <StickyFilters />;
};

const Overview = ({ initialContent }: { initialContent: React.ReactNode }) => {
    return <SearchTab filters={<Filters />} body={initialContent} />;
};

export const overview = ({ initialContent }: { initialContent: React.ReactNode }): HorizontalTabType => ({
    value: 'overview',
    label: <OverviewLabel />,
    content: <Overview initialContent={initialContent} />,
});
