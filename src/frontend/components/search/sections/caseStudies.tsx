import { useEffect } from 'react';

import { Pagination } from '@mantine/core';

import { HorizontalTabType } from 'components/horizontal-tabs';

import { useCaseStudySearch } from 'components/case-study-search/hooks/use-case-study-search';

import { CaseStudySearchService } from 'components/case-study-search/services/CaseStudySearchService';

import { SearchTab } from 'components/search/components/SearchTab';
import { StickyFilters } from 'components/search/components/StickyFilters';
import { SearchTabName } from 'components/search/components/SearchTabName';
import { CaseStudyHits } from 'components/case-study-search/components/CaseStudyHits';
import { InlineFilters } from 'components/case-study-search/inline-filters/InlineFilters';
import { isEqual } from 'radash';
import { DEFAULT_CASE_STUDY_SEARCH } from 'components/case-study-search/state/case-study-search';

const CaseStudiesLabel = () => {
    const { totalResults, isLoading, filters } = useCaseStudySearch();

    const hasSearched = !isEqual(filters, DEFAULT_CASE_STUDY_SEARCH.filters);

    const label = isLoading || !hasSearched ? undefined : totalResults;

    return <SearchTabName label={label}>Case Studies</SearchTabName>;
};

const Filters = () => {
    return (
        <>
            <InlineFilters variant="light" />
            <StickyFilters filters={<InlineFilters variant="dark" size="md" />} />
        </>
    );
};

const CaseStudies = () => {
    const { caseStudies, isLoading, page, totalPages } = useCaseStudySearch();
    const showPagination = !isLoading && totalPages > 1;

    // initial search
    useEffect(() => {
        CaseStudySearchService.debouncedSearch();
    }, []);

    return (
        <SearchTab
            filters={
                <>
                    <Filters />
                </>
            }
            body={<CaseStudyHits caseStudies={caseStudies} isLoading={isLoading} />}
            pagination={({ scrollToTop }) =>
                showPagination ? (
                    <Pagination
                        value={page + 1}
                        onChange={(newPage) => {
                            CaseStudySearchService.setPage(newPage - 1);
                            scrollToTop();
                        }}
                        total={totalPages}
                    />
                ) : null
            }
        />
    );
};

export const caseStudies: HorizontalTabType = {
    value: 'caseStudies',
    label: <CaseStudiesLabel />,
    content: <CaseStudies />,
};
