import { useEffect } from 'react';

import { Pagination } from '@mantine/core';

import { HorizontalTabType } from 'components/horizontal-tabs';

import { useComponentSearchData } from 'components/component-overview/hooks/use-component-search-data';
import { useComponentSearchDefaultValues } from 'components/component-overview/hooks/use-component-search-default-values';

import { SearchService } from 'components/component-overview/services/SearchService';

import { ComponentSearchForm } from 'components/component-overview/ComponentSearchForm';
import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { ComponentOverviewHits } from 'components/component-overview/ComponentOverviewHits';

import { SearchTab } from 'components/search/components/SearchTab';
import { StickyFilters } from 'components/search/components/StickyFilters';
import { SearchTabName } from 'components/search/components/SearchTabName';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { useLocalEvent } from 'hooks/use-local-event';

const ProductsLabel = () => {
    const { totalComponents, isLoading } = useComponentSearchData();
    const filters = useComponentSearchFilters();

    const hasSearched = !SearchService.isInitialSearch(filters);

    return <SearchTabName label={isLoading || !hasSearched ? undefined : totalComponents}>Products</SearchTabName>;
};

const Filters = () => {
    return (
        <ComponentSearchForm>
            <InlineFilters variant="light" />
            <StickyFilters filters={<InlineFilters variant="dark" />} />
        </ComponentSearchForm>
    );
};

const Products = () => {
    const { localEvent } = useLocalEvent();
    const { docs: components, page, totalPages, isLoading } = useComponentSearchData();

    const showPagination = !isLoading && totalPages > 1;

    // search by URL query params
    useComponentSearchDefaultValues();

    // initial search
    useEffect(() => {
        SearchService.debouncedSearch();
    }, [localEvent?.id]);

    return (
        <SearchTab
            filters={<Filters />}
            body={<ComponentOverviewHits hits={components} isLoading={isLoading} />}
            pagination={({ scrollToTop }) =>
                showPagination ? (
                    <Pagination
                        value={page + 1}
                        onChange={(newPage) => {
                            SearchService.setPage(newPage - 1);
                            scrollToTop();
                        }}
                        total={totalPages}
                    />
                ) : null
            }
        />
    );
};

export const products: HorizontalTabType = {
    value: 'products',
    label: <ProductsLabel />,
    content: <Products />,
};
