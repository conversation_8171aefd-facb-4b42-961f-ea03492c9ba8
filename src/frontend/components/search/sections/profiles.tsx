import { useEffect } from 'react';

import { HorizontalTabType } from 'components/horizontal-tabs';

import { useProfileOverviewSearch } from 'components/profile-overview/hooks/use-profile-overview-search';

import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { InlineFilters } from 'components/profile-overview/inline-filters/InlineFilters';
import { ProfileOverviewHits } from 'components/profile-overview/components/ProfileOverviewHits';

import { SearchTab } from 'components/search/components/SearchTab';
import { StickyFilters } from 'components/search/components/StickyFilters';
import { SearchTabName } from 'components/search/components/SearchTabName';
import { Pagination } from '@mantine/core';
import { useLocalEvent } from 'hooks/use-local-event';

const ProfilesLabel = () => {
    const { totalProfiles, isLoading, filters } = useProfileOverviewSearch();

    const hasSearched = !ProfileOverviewService.isInitialSearch(filters);

    const label = isLoading || !hasSearched ? undefined : totalProfiles;

    return <SearchTabName label={label}>Profiles</SearchTabName>;
};

const Filters = () => {
    return (
        <>
            <InlineFilters variant="light" />
            <StickyFilters filters={<InlineFilters variant="dark" size="md" />} />
        </>
    );
};

const Profiles = () => {
    const { localEvent } = useLocalEvent();
    const { profiles, isLoading, page, totalPages } = useProfileOverviewSearch();

    const showPagination = !isLoading && totalPages > 1;

    // initial search
    useEffect(() => {
        ProfileOverviewService.debouncedSearch();
    }, [localEvent?.id]);

    return (
        <SearchTab
            filters={<Filters />}
            body={<ProfileOverviewHits profiles={profiles} isLoading={isLoading} />}
            pagination={({ scrollToTop }) =>
                showPagination ? (
                    <Pagination
                        value={page + 1}
                        onChange={(newPage) => {
                            ProfileOverviewService.setPage(newPage - 1);
                            scrollToTop();
                        }}
                        total={totalPages}
                    />
                ) : null
            }
        />
    );
};

export const profiles: HorizontalTabType = {
    value: 'profiles',
    label: <ProfilesLabel />,
    content: <Profiles />,
};
