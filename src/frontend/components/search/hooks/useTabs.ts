import { HorizontalTabType } from 'components/horizontal-tabs';

import { overview } from '../sections/overview';
import { products } from '../sections/products';
import { profiles } from '../sections/profiles';
import { designs } from '../sections/designs';
import { caseStudies } from '../sections/caseStudies';
import { dcMicrogrids } from '../sections/dcMicrogrids';

const useTabs = (initialContent: React.ReactNode) => {
    const tabs: HorizontalTabType[] = [products, profiles, designs, caseStudies, dcMicrogrids];

    if (initialContent) {
        tabs.unshift(overview({ initialContent }));
    }

    return tabs;
};

export { useTabs };
