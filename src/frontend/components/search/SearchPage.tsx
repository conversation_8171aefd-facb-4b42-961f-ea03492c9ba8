import Link from 'next/link';

import { But<PERSON>, Stack } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { Page } from 'components/page/Page';
import { Tabs } from 'components/search/components/Tabs';
import { Title } from 'components/search/components/Title';
import { SearchBar } from 'components/component-overview/components/SearchBar';
import { TabContent } from 'components/search/components/TabContent';

type Props = {
    title?: string;
    initialContent?: React.ReactNode;
};

export const SearchPage = ({ title = 'Search', initialContent }: Props) => {
    const user = useCurrentUser();

    return (
        <Page
            title={title}
            showBackground
            showScrollToTop
            breadcrumbs={{
                type: 'floating.fullWidth',
                isSticky: false,
                showToggle: true,
                rightSection: !user?.hasCompany && (
                    <Button
                        variant="filled"
                        size="xs"
                        component={Link}
                        href={CompanyProfileHelpers.urls.create()}
                        leftSection={<IoAdd />}
                    >
                        Create Company Profile
                    </Button>
                ),
            }}
        >
            <Page.Hero pb={0}>
                <Stack gap="xl">
                    <Title />
                    <SearchBar showAIButton />
                    <Tabs initialContent={initialContent} />
                </Stack>
            </Page.Hero>

            <Page.WideContent id="search-content" pt="lg">
                <TabContent initialContent={initialContent} />
            </Page.WideContent>
        </Page>
    );
};
