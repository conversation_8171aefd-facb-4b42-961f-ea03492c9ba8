import { Badge, Group, Title, Transition } from '@mantine/core';

export const SearchTabName = ({ children, label }: { children: React.ReactNode; label?: React.ReactNode }) => {
    const showLabel = !!label;

    return (
        <Group gap={8} wrap="nowrap">
            <Title order={2} size="h3" fw={700} hiddenFrom="sm">
                {children}
            </Title>
            <Title order={2} visibleFrom="sm">
                {children}
            </Title>

            <Transition transition="fade" duration={200} mounted={showLabel}>
                {(style) => (
                    <Badge size="xs" variant="light" color="brand" style={{ flexShrink: 0, ...style }}>
                        {label}
                    </Badge>
                )}
            </Transition>
        </Group>
    );
};
