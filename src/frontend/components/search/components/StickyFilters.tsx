import React, { useEffect, useState, FC } from 'react';

import { Box, Stack, Transition } from '@mantine/core';
import { useHeadroom, useWindowScroll } from '@mantine/hooks';

import { useSidebarNav } from 'hooks/use-sidebar-nav';

import { SearchBar } from 'components/component-overview/components/SearchBar';

import cx from './StickyFilters.module.scss';

const StickyFilters: FC<{
    filters?: React.ReactNode;
}> = ({ filters }) => {
    const { isOpen } = useSidebarNav();

    const scrollUp = useHeadroom();
    const [scroll] = useWindowScroll();

    const [offset, setOffset] = useState(200);

    useEffect(() => {
        const hero = document.querySelector('[data-hero]');

        if (hero) {
            setOffset(hero.getBoundingClientRect().height + 20);
        }
    }, []);

    const showSearchBar = scrollUp && scroll.y > offset;

    return (
        <Transition transition="slide-down" mounted={showSearchBar}>
            {(transitionStyles) => (
                <Stack data-sidebar-open={isOpen} style={transitionStyles} className={cx.root}>
                    <Box className={cx.inner}>
                        <SearchBar size="md" />
                    </Box>

                    {filters && <Box className={cx.filters}>{filters}</Box>}
                </Stack>
            )}
        </Transition>
    );
};

export { StickyFilters };
