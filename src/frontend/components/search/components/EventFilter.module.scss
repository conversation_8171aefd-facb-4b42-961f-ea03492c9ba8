.eventClear {
    display: inline-flex;
    align-items: center;

    opacity: 0.5;
    line-height: 1.2;
    font-size: var(--mantine-font-size-lg);
}

.eventDate {
    opacity: 0.5;

    @media (max-width: $mantine-breakpoint-sm) {
        display: none;
    }
}

.eventItem {
    --menu-item-hover: white;

    gap: 40px;
}

.eventDropdown {
    backdrop-filter: blur(5px);

    background-color: rgba(255, 255, 255, 0.8);
}

.eventBadge {
    border-radius: var(--mantine-radius-xs);
    font-size: 9px;
}
