import { Box } from '@mantine/core';

import { HorizontalTabs } from 'components/horizontal-tabs';

import { useTabs } from 'components/search/hooks/useTabs';
import { useActiveTab } from 'components/horizontal-tabs/hooks/useActiveTab';

import cx from './Tabs.module.scss';

type Props = {
    initialContent?: React.ReactNode;
};

const Tabs = ({ initialContent }: Props) => {
    const tabs = useTabs(initialContent);

    const { activeTab } = useActiveTab(tabs);

    if (!activeTab) {
        return null;
    }

    return (
        <Box className={cx.root}>
            <HorizontalTabs.Tabs tabs={tabs} value={activeTab} />
        </Box>
    );
};

export { Tabs };
