import { Badge, Group, Menu, Text, UnstyledButton } from '@mantine/core';
import { IoCalendarOutline, IoClose } from 'react-icons/io5';

import { DateService } from 'services/DateService';

import { useLocalEvent } from 'hooks/use-local-event';
import { useActiveEvents } from 'hooks/use-active-events';

import cxTitle from './Title.module.scss';
import cx from './EventFilter.module.scss';

const EventFilter = () => {
    const { localEvent, setLocalEvent } = useLocalEvent();
    const { events } = useActiveEvents();

    if (!events.length) {
        return null;
    }

    let eventDate = '';

    if (localEvent) {
        eventDate = DateService.format(localEvent.start, 'MMM D') + ' - ' + DateService.format(localEvent.end, 'MMM D');
    }

    if (!localEvent && events.length === 1) {
        return (
            <UnstyledButton className={cxTitle.linkWrapper} onClick={() => setLocalEvent(events[0].id)}>
                <IoCalendarOutline />
                <Text span inherit className={cxTitle.link}>
                    Are you attending {events[0].name}?
                </Text>
            </UnstyledButton>
        );
    }

    return (
        <Menu
            disabled={events.length < 2}
            trigger="click-hover"
            position="bottom-start"
            width="target"
            shadow="xl"
            classNames={{
                item: cx.eventItem,
            }}
        >
            <Menu.Target>
                <UnstyledButton className={cxTitle.linkWrapper} data-active={Boolean(localEvent)}>
                    <IoCalendarOutline />
                    <Text span inherit className={cxTitle.link}>
                        {localEvent ? localEvent.name : 'What event are you attending?'}
                    </Text>

                    {eventDate && (
                        <Text span inherit className={cx.eventDate}>
                            ({eventDate})
                        </Text>
                    )}

                    {localEvent && (
                        <UnstyledButton className={cx.eventClear} onClick={() => setLocalEvent(null)}>
                            <IoClose />
                        </UnstyledButton>
                    )}
                </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown className={cx.eventDropdown}>
                {events.map((event) => (
                    <Menu.Item
                        key={event.id}
                        onClick={() => setLocalEvent(event.id)}
                        rightSection={
                            localEvent?.id === event.id && (
                                <Badge className={cx.eventBadge} color="brand">
                                    Active
                                </Badge>
                            )
                        }
                    >
                        <Text fw={700}>{event.name}</Text>
                        <Group gap={6} c="dimmed">
                            <IoCalendarOutline />
                            {DateService.format(event.start, 'MMM D') + ' - ' + DateService.format(event.end, 'MMM D')}
                        </Group>
                    </Menu.Item>
                ))}
            </Menu.Dropdown>
        </Menu>
    );
};

export { EventFilter };
