import { Flex, Group, Stack, Text, UnstyledButton } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoInformationCircleOutline } from 'react-icons/io5';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { Page } from 'components/page/Page';

import { ReplusLogo } from 'components/logo/ReplusLogo';
import { SEIALogo } from 'components/logo/SEIALogo';
import { SEPALogo } from 'components/logo/SEPALogo';
import { ReplusModal } from 'components/replus-modal/ReplusModal';
import { EventFilter } from 'components/search/components/EventFilter';

import Link from 'next/link';

import cx from './Title.module.scss';

export const Title = () => {
    return (
        <Flex className={cx.root}>
            <Flex className={cx.logo} component={Link} href={RouterHelpers.urls.searchTab('overview')}>
                <ReplusLogo isWhite />
            </Flex>
            <Stack className={cx.titleWrapper} gap="xs">
                <Flex className={cx.title} visibleFrom="sm">
                    <Page.Title opacity={0.9}>Discover. Design. Deploy.</Page.Title>
                    <Text fz="lg" fw={600} opacity={0.8} ta="left">
                        Bringing Power Systems to Life
                    </Text>
                </Flex>
                <Flex className={cx.title} hiddenFrom="sm">
                    <Page.Title opacity={0.9}>Discover. Design. Deploy.</Page.Title>
                    <Text fz="lg" fw={600} opacity={0.8} ta="left">
                        Bringing Power Systems to Life
                    </Text>
                </Flex>
                <Group>
                    <EventFilter />
                    <MoreInfo />
                </Group>
            </Stack>

            <Flex className={cx.right}>
                <Flex fw={500} opacity={0.6} fz="xs" ta="right" pt={-6}>
                    Powered by
                </Flex>
                <Flex
                    component={Link}
                    href="https://seia.org/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cx.rightlogo}
                >
                    <SEIALogo isWhite />
                </Flex>
                <Flex
                    component={Link}
                    href="https://sepapower.org/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cx.rightlogo}
                >
                    <SEPALogo isWhite />
                </Flex>
            </Flex>
        </Flex>
    );
};

const MoreInfo = () => {
    const [opened, { open, close }] = useDisclosure(false);

    return (
        <>
            <UnstyledButton className={cx.link} onClick={open}>
                <IoInformationCircleOutline />
                What is RE+Source Pro?
            </UnstyledButton>
            <ReplusModal opened={opened} handleClose={close} />
        </>
    );
};
