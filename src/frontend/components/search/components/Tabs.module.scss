.root {
    width: 100%;

    margin-top: var(--mantine-spacing-md);

    margin: 0 auto;

    @media (min-width: $mantine-breakpoint-sm) {
        margin-top: calc(var(--mantine-spacing-xl) * 2);
    }

    :global(.mantine-Tabs-list) {
        justify-content: left;

        flex-wrap: nowrap;

        margin-bottom: 0;

        &::before {
            content: none;
        }
    }

    :global(.mantine-Tabs-tab) {
        --tabs-color: var(--mantine-color-primary-4);
        --tab-border-color: rgba(255, 255, 255, 0.4);

        @media (min-width: $mantine-breakpoint-sm) {
            border-bottom-width: 4px;
            padding-left: var(--mantine-spacing-xl);
            padding-right: var(--mantine-spacing-xl);

            h2 {
                font-size: var(--mantine-font-size-xl) !important;
            }
        }
    }

    :global(.mantine-Badge-root) {
        --badge-bg: var(--mantine-color-primary-0) !important;
        --badge-color: var(--mantine-color-primary-6) !important;
    }
}
