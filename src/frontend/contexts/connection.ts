import { createContext, useContext } from 'react';
import { DiagramConnection, DiagramComponentInstance } from 'models';

export const ConnectionContext = createContext<{
    connection: DiagramConnection;
    fromComponentInstance: DiagramComponentInstance;
    toComponentInstance: DiagramComponentInstance;
} | null>(null);

export const useConnectionContext = () => {
    return useContext(ConnectionContext)!;
};
