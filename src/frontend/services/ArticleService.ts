import { Article } from 'models';

import { mutate } from 'swr';

import { ApiService } from 'services/ApiService';
import { ArticleHelpers } from 'helpers/ArticleHelpers';

import { getId } from 'helpers/getId';

import { publicConfig } from '@public-config';

import { IncomingMessage } from 'http';

const ArticleService = {
    mutate: {
        list: async (company: string, type: string) => {
            const companyId = getId(company)!;
            await mutate(ArticleHelpers.swr.list(companyId, type));
        },
    },

    list: async (company: string, type: string) => {
        const { docs } = await ApiService.get<{
            docs: Article[];
        }>(`${publicConfig.urls.api}/articles?where[company][equals]=${company}&where[type][equals]=${type}&limit=0`);

        return docs;
    },

    getMultiple: async (articleIds: string[]) => {
        const params = articleIds.map((id) => `where[id][in]=${id}`).join('&');

        return ApiService.get<{ docs: Article[] }>(`${publicConfig.urls.api}/articles?${params}`);
    },

    get: async (id: Article['id'], req?: IncomingMessage) => {
        const article = await ApiService.get<Article>(`${publicConfig.urls.api}/articles/${id}`, { req });

        return article;
    },

    create: async (data: Article) => {
        const { doc: article } = await ApiService.post<{
            doc: Article;
        }>(`${publicConfig.urls.api}/articles`, data);

        return article;
    },

    update: async (id: Article['id'], data: Partial<Article>) => {
        const { doc: article } = await ApiService.patch<{
            doc: Article;
        }>(`${publicConfig.urls.api}/articles/${id}`, {
            name: data.name,
            file: data.file,
            teaser: data.teaser,
        });

        return article;
    },

    delete: async (id: Article['id']) => {
        await ApiService.delete(`${publicConfig.urls.api}/articles/${id}`);
    },

    search: async (query?: string) => {
        const params = new URLSearchParams({
            query: JSON.stringify({ search: query, type: 'caseStudy' }),
            page: '0',
            sort: 'relevance',
        });

        return ApiService.get(`${publicConfig.urls.api}/articles/search?${params.toString()}`);
    },
};

export { ArticleService };
