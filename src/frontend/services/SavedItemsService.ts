import { ApiService } from './ApiService';

import { SavedItemPayload, SavedItemType } from 'models';

import { publicConfig } from '@public-config';

import { ServiceHelpers } from 'helpers/ServiceHelpers';
import { SavedItemsHelpers } from 'helpers/SavedItemsHelpers';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { savedItemsState } from 'state/current-saved-items';

const SavedItemsService = {
    navigate: ServiceHelpers.createNavigator(SavedItemsHelpers.urls),

    list: async ({
        page = 1,
        limit = 999,
        userId,
        type,
    }: {
        page?: number;
        limit?: number;
        userId: string;
        type?: SavedItemType;
    }) => {
        const searchParams = new URLSearchParams({
            'depth': '0',
            'page': page.toString(),
            'limit': limit.toString(),
            'where[user][equals]': userId,
        });

        if (type) {
            searchParams.set(`where[item.relationTo][equals]`, type);
        }

        return await ApiService.get(`${publicConfig.urls.api}/savedItems?${searchParams.toString()}`);
    },

    count: async (id: string) => {
        const { count } = await ApiService.get<{
            count: number;
        }>(`${publicConfig.urls.api}/savedItems/count?id=${id}`);

        return count;
    },

    create: async (payload: SavedItemPayload) => {
        InternalTrackingService.track('savedItems.create', payload);

        const result = await ApiService.post(`${publicConfig.urls.api}/savedItems?depth=0`, payload);

        if (result?.doc) {
            savedItemsState.savedItems.push(result.doc);
        }

        return result;
    },

    delete: async (savedItemId: string, payload: SavedItemPayload) => {
        InternalTrackingService.track('savedItems.delete', payload);

        savedItemsState.savedItems = savedItemsState.savedItems.filter((savedItem) => savedItem.id !== savedItemId);

        return await ApiService.delete(`${publicConfig.urls.api}/savedItems/${savedItemId}`);
    },
};

export { SavedItemsService };
