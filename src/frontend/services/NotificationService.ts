import { Notification } from 'models';

import { mutate } from 'swr';

import { ApiService } from './ApiService';
import { InternalTrackingService } from './InternalTrackingService';
import { LocalNotificationService } from './LocalNotificationService';

import { state as inboxState } from 'state/inbox';

import { publicConfig } from '@public-config';

const NotificationService = {
    inbox: async () => {
        return await ApiService.get<Notification[]>(`${publicConfig.urls.api}/notifications/inbox`);
    },

    refreshInbox: async () => {
        await mutate('/inbox');
    },

    receiveNotification: (notification: Notification) => {
        const title = {
            'diagram.comment.create': 'New diagram comment',
            'diagram.comment.reply.user': 'Comment reply',
            'diagram.comment.reply.ai': 'Comment reply',
            'diagram.comment.mention.user': 'Comment mention',
            'diagram.comment.mention.manufacturer': 'Comment mention',
            'diagram.invite': 'Diagram invite',
            'diagram.requestAccess': 'Diagram access request',
            'diagram.chat.channel': 'New chat channel created',
            'diagram.chat.message': 'New chat message',
            'intercom.message': 'New support message',
            'order.event': '',
            'order.message': '',
            'design.duplicate': '',
            'company.requestAccess': 'Company access request',
            'company.grantAccess': 'Company access granted',
            'company.denyAccess': 'Company access denied',
            'user.teamInvite': 'Team invite',
            'company.addedAsPartner': 'Company added as partner',
            'team.requestAccess': 'Team access request',
            'team.grantAccess': 'Team access granted',
            'team.denyAccess': 'Team access denied',
            'company.newExhibitorMatchLead': 'New exhibitor match lead',
        }[notification.type];

        LocalNotificationService.showNotification({
            title,
            // @ts-ignore
            description: notification.app.text,
            // @ts-ignore
            url: notification.app.url,
        });
    },

    openInbox: () => {
        InternalTrackingService.track('notifications.open-inbox');
        inboxState.opened = true;
    },

    closeInbox: () => {
        inboxState.opened = false;
    },

    toggleReadFlag: () => {
        inboxState.flags.read = !inboxState.flags.read;
    },

    read: async (notification: Notification) => {
        InternalTrackingService.track('notifications.read', {
            notification,
        });

        await ApiService.post(`${publicConfig.urls.api}/notifications/${notification.id}/read`);
    },

    readAll: async () => {
        InternalTrackingService.track('notifications.read-all');

        await ApiService.post(`${publicConfig.urls.api}/notifications/read-all`);
    },

    handle: async (notification: Notification) => {
        InternalTrackingService.track('notifications.handled', {
            notification,
        });

        await ApiService.post(`${publicConfig.urls.api}/notifications/${notification.id}/handle`);
    },
};

export { NotificationService };
