import { User } from 'models';
import * as Ably from 'ably';
import { RealtimeClient } from 'ably';
import Spaces from '@ably/spaces';

import { publicConfig } from '@public-config';

const RealtimeService = {
    initialized: false,
    initializedAsClientId: null as string | null,

    clients: {
        ably: null as any as RealtimeClient,
        spaces: null as any as Spaces,
    },

    init: (user: User | null) => {
        const clientId = user?.id || null;

        if (RealtimeService.initialized && RealtimeService.initializedAsClientId === clientId) {
            return RealtimeService.clients;
        }

        const ably = new Ably.Realtime(
            clientId
                ? {
                      key: publicConfig.ablyPublicKey,
                      clientId,
                      echoMessages: false,
                      logLevel: 1,
                  }
                : {
                      key: publicConfig.ablyPublicKey,
                      echoMessages: false,
                      logLevel: 1,
                  },
        );

        RealtimeService.clients.ably = ably;
        RealtimeService.clients.spaces = new Spaces(ably);
        RealtimeService.initialized = true;
        RealtimeService.initializedAsClientId = clientId;

        return RealtimeService.clients;
    },
};

export { RealtimeService };
