import { dash, first, last, uid } from 'radash';

import { ApiService } from './ApiService';

import { publicConfig } from '@public-config';
import { FilePayload } from 'models';

const FILE_SIZE_LIMIT = 1000 * 1000 * 4.5;

export class FileSizeLimitExceededError extends Error {
    title = 'File size limit exceeded';
    message = 'The file size limit is 4.5MB';
}

class FileService {
    static list = async () => {
        return await ApiService.get(`${publicConfig.urls.api}/files`);
    };

    static create = async (payload: { file: File; name?: string; group: string }) => {
        const { file, name, group } = payload;

        if (file.size > FILE_SIZE_LIMIT) {
            throw new FileSizeLimitExceededError();
        }

        const headers = {};

        const body = new FormData();
        const parts = file.name.split('.');
        const cleanName = name || first(parts)!;
        const extension = last(parts);

        body.append(
            'file',
            new File([file], `${dash(cleanName)}-${uid(6)}.${extension}`, {
                type: file.type,
            }),
        );

        body.append('_payload', JSON.stringify({ name: cleanName, group }));

        const response = await fetch(`${publicConfig.urls.api}/files`, {
            method: 'POST',
            headers,
            body,
            credentials: 'include',
        });
        const json = await response.json();

        return json.doc;
    };

    static createFromUrl = async (url: string, name: string, group: string) => {
        const params = [`url=${encodeURIComponent(url)}`, `name=${name}`, `group=${group}`].join('&');

        return await ApiService.get<{
            success: boolean;
            id: string;
        }>(`${publicConfig.urls.api}/files/create-from-url?${params}`);
    };

    static get = async (fileId: string) => {
        return ApiService.get(`${publicConfig.urls.api}/files/${fileId}`);
    };

    static update = async (fileId: string, payload: any) => {
        return await ApiService.patch(`${publicConfig.urls.api}/files/${fileId}`, payload);
    };

    static delete = async (fileId: string) => {
        return await ApiService.delete(`${publicConfig.urls.api}/files/${fileId}`);
    };

    static addFile = async ({
        file,
        name,
        group,
        ...payload
    }: { file: File; name?: string; group: string } & Omit<FilePayload, 'file' | 'url'>) => {
        const newFile = await FileService.create({ file, name, group });

        const updatedCollection = await ApiService.post(`${publicConfig.urls.api}/add-file`, {
            file: newFile.id,
            ...payload,
        });

        return { newFile, updatedCollection };
    };

    static addUrl = async (payload: Omit<FilePayload, 'file'> & { url: string }) => {
        return await ApiService.post(`${publicConfig.urls.api}/add-file`, payload);
    };

    static deleteFile = async (payload: Omit<FilePayload, 'type'>) => {
        return await ApiService.post(`${publicConfig.urls.api}/delete-file`, payload);
    };

    static getNameAndExtension = (file: File) => {
        const parts = file.name.split('.');
        const extension = parts.pop();
        const name = parts.join('.');

        return { name, extension };
    };
}

export { FileService };
