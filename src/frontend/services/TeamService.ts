import React from 'react';

import { Invoice, PermissionTeam, SubscriptionBillingCycle, Team, Subscription } from 'models';
import { snapshot } from 'valtio';

import { openContextModal } from '@mantine/modals';

import { ApiService } from './ApiService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { UserService } from 'services/UserService';

import { state as currentTeamState } from 'state/current-team';

import { publicConfig } from '@public-config';
import { mutate } from 'swr';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { IncomingMessage } from 'http';

const TeamService = {
    getCurrentTeam: async (req?: IncomingMessage) => {
        return ApiService.get(`${publicConfig.urls.api}/teams/current`, { req });
    },

    create: async (name: string): Promise<any> => {
        InternalTrackingService.track('team.create', {
            name,
        });

        const result = await ApiService.post(`${publicConfig.urls.api}/teams`, { name });

        return result;
    },

    get: async (teamId: string, req?: IncomingMessage): Promise<Team> => {
        if (currentTeamState.team?.id === teamId) {
            return snapshot(currentTeamState.team) as Team;
        }

        return ApiService.get(`${publicConfig.urls.api}/teams/${teamId}?depth=0`, { req });
    },

    refresh: async (teamId: string, data?: Team): Promise<Team> => {
        const updatedTeam = data ?? (await ApiService.get(`${publicConfig.urls.api}/teams/${teamId}?depth=0`))!;

        mutate(`/api/teams/${teamId}`, updatedTeam, { revalidate: false });

        if (teamId === currentTeamState.team?.id) {
            currentTeamState.team = updatedTeam;
        }

        return updatedTeam;
    },

    update: async (teamId: string, data: any): Promise<any> => {
        InternalTrackingService.track('team.update', {
            data,
        });

        const result = await ApiService.patch(`${publicConfig.urls.api}/teams/${teamId}`, data);

        await TeamService.refresh(teamId, result.doc);

        return result;
    },

    inviteUser: async (
        teamId: string,
        email: string,
        role: PermissionTeam = PermissionTeam.USER,
        data: Record<string, any> = {},
    ) => {
        InternalTrackingService.track('team.inviteUser', {
            email,
            role,
        });

        const currentUrl = location.toString();
        const team = await ApiService.post(
            `${publicConfig.urls.api}/teams/${teamId}/users/invite`,
            {
                teamId,
                email,
                role: role,
                source: currentUrl,
                origin: `${location.origin}/token`,
                ...data,
            },
            {
                throwError: true,
            },
        );

        await TeamService.refresh(teamId, team);

        await UserService.refreshProgress();

        return team;
    },

    updateUserRole: async (teamId: string, user: string, role: PermissionTeam) => {
        InternalTrackingService.track('team.updateUserRole', {
            user,
            role,
        });

        const team = await ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/users/edit`, {
            user,
            role,
        });

        await TeamService.refresh(teamId, team);

        return team;
    },

    deleteUser: async ({ teamId, userId }: { teamId: string; userId: string }) => {
        InternalTrackingService.track('team.removeUser', {
            user: userId,
        });

        const team = await ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/users/delete`, {
            userId,
        });

        await TeamService.refresh(teamId, team);

        return team;
    },

    activateUser: async ({ teamId, userId, activate }: { teamId: string; userId: string; activate?: boolean }) => {
        try {
            const team = await ApiService.post(
                `${publicConfig.urls.api}/teams/${teamId}/users/activate`,
                {
                    userId,
                    activate: activate ?? true,
                },
                {
                    throwError: true,
                },
            );

            await TeamService.refresh(teamId, team);
        } catch (error: any) {
            LocalNotificationService.showError({
                message: error.message,
            });

            console.error(error);
        }
    },

    deactivateUser: async ({ teamId, userId }: { teamId: string; userId: string }) => {
        return TeamService.activateUser({ teamId, userId, activate: false });
    },

    subscriptionRefresh: async (teamId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/subscription-refresh`);
    },

    subscriptionSignUp: async (
        teamId: string,
        subscription: Subscription,
        billingCycle: SubscriptionBillingCycle,
        numberOfSeats: number,
        additionalSubscriptions: Subscription[],
        interestedIn: string[],
    ) => {
        return await ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/subscription-sign-up`, {
            subscription,
            billingCycle,
            numberOfSeats,
            additionalSubscriptions,
            interestedIn,
        });
    },

    subscriptionRequestDowngrade: async (subscription: Subscription) => {
        return await ApiService.post(`${publicConfig.urls.api}/teams/subscription-request-downgrade`, {
            subscription,
        });
    },

    getStripeInvoices: async (teamId: Team['id']): Promise<Invoice[]> => {
        return await ApiService.get(`${publicConfig.urls.api}/teams/${teamId}/invoices`);
    },

    getStripePortalUrl: async (teamId: Team['id']): Promise<string> => {
        const response = await ApiService.get(`${publicConfig.urls.api}/teams/${teamId}/portal-url`);

        return response.url;
    },

    sendSubscriptionFeedback: async (feedback: string) => {
        return ApiService.post(`${publicConfig.urls.api}/teams/subscription-feedback`, {
            feedback,
        });
    },

    setShowDesignEditor: async (teamId: string, showDesignEditor: boolean = true) => {
        return TeamService.update(teamId, {
            showDesignEditor,
        });
    },

    openCreateTeam: () => {
        openContextModal({
            modal: 'createTeam',
            innerProps: {},
            centered: true,
            withCloseButton: false,
        });
    },

    openSwitchTeamModal: ({
        create,
        switchy,
        force = false,
    }: {
        create: {
            title: string;
            description: React.ReactNode;
        };
        switchy: {
            title: string;
            description: React.ReactNode;
        };
        force?: boolean;
    }) => {
        const options = force
            ? {
                  closeOnClickOutside: false,
                  closeOnEscape: false,
              }
            : {};

        openContextModal({
            modal: 'genericSwitchTeam',
            innerProps: {
                force,
                create,
                switchy,
            },
            centered: true,
            withCloseButton: false,
            ...options,
        });
    },

    postCreateSubscription: (customerId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/teams/post-subscribe/${customerId}`, {});
    },

    requestAccess: async (teamId: string) => {
        InternalTrackingService.track('team.requestAccess', {
            team: teamId,
        });

        return ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/request-access`);
    },

    denyAccess: async (teamId: string, userId: string, message: string) => {
        InternalTrackingService.track('team.denyAccess', {
            teamId,
            userId,
            message,
        });

        return ApiService.post(`${publicConfig.urls.api}/teams/${teamId}/deny-access`, {
            userId,
            message,
        });
    },
};

export { TeamService };
