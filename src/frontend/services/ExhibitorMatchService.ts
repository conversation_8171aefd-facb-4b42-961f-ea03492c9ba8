import { ExhibitorMatch, ExhibitorMatchLead } from 'models';
import { publicConfig } from '@public-config';

import { ApiService } from 'services/ApiService';

import { mutate } from 'swr';
import { UserHelpers } from 'helpers/UserHelpers';

const ExhibitorMatchService = {
    swr: {
        exhibitorMatch: (exhibitorMatchId: string) => `exhibitorMatch/${exhibitorMatchId}`,
    },

    getExhibitorMatch: async (exhibitorMatchId: string) => {
        return await ApiService.get<ExhibitorMatch>(`${publicConfig.urls.api}/exhibitorMatches/${exhibitorMatchId}`);
    },

    createExhibitorMatchLead: async (data: Partial<ExhibitorMatchLead>) => {
        const result = await ApiService.post<{ doc: ExhibitorMatchLead }>(
            `${publicConfig.urls.api}/exhibitorMatchLeads`,
            data,
        );

        if (data.exhibitorMatch) {
            await mutate(ExhibitorMatchService.swr.exhibitorMatch(data.exhibitorMatch));
        }

        return result.doc;
    },

    getLeadsForCompany: async (companyId: string) => {
        return await ApiService.get<{ docs: ExhibitorMatchLead[] }>(
            `${publicConfig.urls.api}/exhibitorMatchLeads?where[company][equals]=${companyId}&pagination=false`,
        );
    },

    inviteUser: async ({
        companyId,
        exhibitorMatchLeadId,
        email,
        message,
    }: {
        companyId: string;
        exhibitorMatchLeadId: string;
        email: string;
        message: any;
    }) => {
        const result = await ApiService.post(`${publicConfig.urls.api}/userInvitations`, {
            references: {
                company: companyId,
                exhibitorMatchLead: exhibitorMatchLeadId,
            },
            email,
            message,
        });

        await mutate(UserHelpers.swr.userInvitations({ email, exhibitorMatchLeadId }));

        return result;
    },
};

export { ExhibitorMatchService };
