import { mobileNavState } from 'hooks/use-mobile-nav';
import { sidebarNavState } from 'state/sidebar-nav';

const NavigationService = {
    disable: () => {
        sidebarNavState.isDisabled = true;
        sidebarNavState.isOpen = false;
        sidebarNavState.isMobileOpen = false;

        mobileNavState.isOpen = false;
    },

    enable: () => {
        sidebarNavState.isDisabled = false;
        sidebarNavState.isOpen = true;
        sidebarNavState.isMobileOpen = false;

        mobileNavState.isOpen = true;
    },

    toggleMobile: () => {
        sidebarNavState.isMobileOpen = !sidebarNavState.isMobileOpen;
    },
};

export { NavigationService };
