import { SubscribableType } from 'models';
import { ApiService } from './ApiService';
import { publicConfig } from '@public-config';

class SubscriptionService {
    static list = async () => {
        return await ApiService.get(`${publicConfig.urls.api}/subscriptions?depth=2`);
    };

    static subscribe = async ({ type, id, userId }: { type: SubscribableType; id: string; userId: string }) => {
        return await ApiService.post(`${publicConfig.urls.api}/${type}s/${id}/subscribe`, {
            userId,
        });
    };

    static unsubscribe = async ({ type, id, userId }: { type: SubscribableType; id: string; userId: string }) => {
        return await ApiService.post(`${publicConfig.urls.api}/${type}s/${id}/unsubscribe`, {
            userId,
        });
    };
}

export { SubscriptionService };
