import { ApiService } from './ApiService';

import { Order, OrderComponentUpdate, OrderStatus } from 'models';

import { publicConfig } from '@public-config';
import { IncomingMessage } from 'http';

type OrderSearchQuery = {
    name?: string;
    statuses: string[];
};

class OrderService {
    static list = async ({ depth, limit, req }: { depth: number; limit: number; req?: IncomingMessage }) => {
        return await ApiService.get(`${publicConfig.urls.api}/orders?depth=${depth}&limit=${limit}`, { req });
    };

    static getByDesign = async (designId?: string) => {
        return await ApiService.get(`${publicConfig.urls.api}/orders/get-by-design?designId=${designId}`);
    };

    static search = async (query?: OrderSearchQuery, page: number = 1) => {
        const cleanQuery = [];

        if (query?.name) {
            cleanQuery.push(`&where[name][contains]=${query.name}`);
        }

        return await ApiService.get(
            `${publicConfig.urls.api}/orders?depth=1&limit=8&page=${page}${cleanQuery.join('')}`,
        );
    };

    static create = async (
        payload: Partial<Pick<Order, 'name' | 'components' | 'design' | 'reviewers' | 'status'>>,
    ): Promise<
        | {
              doc: Order;
              errors: undefined;
          }
        | {
              doc: undefined;
              errors: {
                  message: string;
              }[];
          }
    > => {
        return ApiService.post(`${publicConfig.urls.api}/orders`, payload);
    };

    static get = async (orderId: string, depth = 0, req?: IncomingMessage) => {
        return ApiService.get(`${publicConfig.urls.api}/orders/${orderId}?depth=${depth}`, { req });
    };

    static update = async (orderId: string, payload: Partial<Order>) => {
        return await ApiService.patch(`${publicConfig.urls.api}/orders/${orderId}`, payload);
    };

    static addComponent = async (orderId: string, componentId: string, quantity?: number) => {
        return await ApiService.post(`${publicConfig.urls.api}/orders/${orderId}/components/add`, {
            componentId,
            quantity,
        });
    };

    static removeComponent = async (orderId: string, componentId: string) => {
        return await ApiService.post(`${publicConfig.urls.api}/orders/${orderId}/components/remove`, {
            componentId,
        });
    };

    static updateComponent = async (orderId: string, componentId: string, update: OrderComponentUpdate) => {
        return await ApiService.post(`${publicConfig.urls.api}/orders/${orderId}/components/update`, {
            componentId,
            ...update,
        });
    };

    static addEvent = async (orderId: string, type: OrderStatus) => {
        return await ApiService.post(`${publicConfig.urls.api}/orders/${orderId}/event`, { type });
    };

    static addMessage = async (orderId: string, message: string) => {
        return await ApiService.post(`${publicConfig.urls.api}/orders/${orderId}/messages/add`, { message });
    };

    static deleteFile = async (orderId: string, fileId: string) => {
        return await ApiService.delete(`${publicConfig.urls.api}/orders/${orderId}/files/${fileId}`);
    };
}

export { OrderService };
