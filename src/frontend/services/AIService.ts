import { publicConfig } from '@public-config';

import { ApiService } from 'services/ApiService';

import {
    Component,
    ComponentChatMessage,
    ComponentChatMessagePayload,
    ComponentVisibility,
    FeatureLimit,
} from 'models';

import { AIServiceUtils } from 'helpers/AIServiceUtils';
import { PermissionServiceSubscription } from 'services/PermissionServiceSubscription';

const AIService = {
    fetchSearchFilters: async (query: string | null) => {
        const controller = new AbortController();
        const timeout = setTimeout(() => {
            controller.abort();
        }, publicConfig.aiFilterTimeout);

        try {
            const urlEncodedQuery = encodeURIComponent(query || '');
            return ApiService.get(`${publicConfig.urls.ai}/search-filters?query=${urlEncodedQuery}`, {
                signal: controller.signal,
            });
        } catch (error: any) {
            if (error?.name === 'AbortError') {
                throw new Error(`Request timed out after ${publicConfig.aiFilterTimeout} seconds`);
            }
            throw error;
        } finally {
            clearTimeout(timeout);
        }
    },

    getAssistantFact: async (
        componentId: string,
        productIdentifier: string,
        componentType: string,
        tag: string,
        assistantConfig: string,
    ) => {
        return ApiService.post(`${publicConfig.urls.ai}/assistant/fact`, {
            component_id: componentId,
            product_identifier: productIdentifier,
            component_type: componentType,
            tag: tag,
            config: assistantConfig,
        });
    },

    getAiSuggestions: async () => {
        return await ApiService.get(`${publicConfig.urls.api}/aiSuggestions`);
    },

    getAnswer: async ({
        componentId,
        projectId,
        question,
        includePrivateFiles,
        history,
    }: {
        componentId?: string;
        projectId?: string;
        question: string;
        includePrivateFiles?: boolean;
        history?: ComponentChatMessage[];
    }) => {
        const conversationMessages = AIServiceUtils.convertAIMessagesToAIConversationMessages(history);

        return ApiService.post(
            `${publicConfig.urls.ai}/qna${includePrivateFiles ? '' : `?${ComponentVisibility.PUBLIC}`}`,
            {
                component_id: componentId,
                project_id: projectId,
                question,
                history: conversationMessages,
            },
            { headers: { Accept: 'text/event-stream' } },
        );
    },

    leaveFeedback: async (message_id: string, rating: NonNullable<ComponentChatMessage['rating']>) => {
        ApiService.post(`${publicConfig.urls.ai}/qna/feedback`, {
            message_id,
            rating,
        });
    },

    getComponentMessages: async (componentId: string) => {
        return ApiService.get(
            `${publicConfig.urls.api}/aiMessages?where[component][equals]=${componentId}&sort=createdAt`,
        );
    },

    getProjectMessages: async (projectId: string) => {
        return ApiService.get(`${publicConfig.urls.api}/aiMessages?where[project][equals]=${projectId}&sort=createdAt`);
    },

    saveComponentMessage: async (message: ComponentChatMessagePayload) => {
        const result = ApiService.post(`${publicConfig.urls.api}/aiMessages`, message);

        await PermissionServiceSubscription.mutateFeatureLimit(FeatureLimit.AI_REQUESTS);

        return result;
    },

    updateMessageRating: async (messageId: string, rating: ComponentChatMessage['rating']) => {
        return ApiService.patch(`${publicConfig.urls.api}/aiMessages/${messageId}`, { rating });
    },

    deleteMessage: async (messageId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/aiMessages/${messageId}/delete`);
    },

    deleteAllMessages: async () => {
        return ApiService.post(`${publicConfig.urls.api}/aiMessages/delete-all`);
    },

    populateDatasheet: async ({
        componentId,
        componentType,
        fileId,
        controller,
    }: {
        componentId: string;
        fileId: string;
        componentType: string;
        controller?: AbortController;
    }): Promise<Partial<Component>[]> => {
        return await ApiService.post(
            `${publicConfig.urls.ai}/datasheet/populate`,
            {
                component_id: componentId,
                document_id: fileId,
                component_type: componentType,
            },
            { signal: controller?.signal },
        );
    },
};

export { AIService };
