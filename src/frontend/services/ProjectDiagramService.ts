import { ApiService } from './ApiService';

import { Diagram } from 'models';

import { publicConfig } from '@public-config';

import { Pointer, Transaction } from 'sync-engine';

class ProjectDiagramService {
    static list = async () => {
        return await ApiService.get(`${publicConfig.urls.api}/projectDesignDiagrams`);
    };

    static create = async (payload: any) => {
        const response = await ApiService.post(`${publicConfig.urls.api}/projectDesignDiagrams`, payload);

        return response?.doc;
    };

    static get = async (diagramId: string) => {
        return ApiService.get(`${publicConfig.urls.api}/projectDesignDiagrams/${diagramId}?depth=0`);
    };

    static getByProjectId = async (projectId: string): Promise<Diagram[]> => {
        const { docs: diagrams } = await ApiService.get(
            `${publicConfig.urls.api}/projectDesignDiagrams?where[project][equals]=${projectId}`,
        );

        return diagrams;
    };

    static update = async (diagramId: string, payload: Diagram) => {
        return await ApiService.patch(`${publicConfig.urls.api}/projectDesignDiagrams/${diagramId}`, payload);
    };

    static delete = async (diagramId: string) => {
        return await ApiService.delete(`${publicConfig.urls.api}/projectDesignDiagrams/${diagramId}`);
    };

    static restore = async (diagramId: string) => {
        return await ApiService.post(`${publicConfig.urls.api}/projectDesignDiagrams/${diagramId}/restore`);
    };

    static sync = async (diagramId: string, transactions: Transaction[], pointer: Pointer) => {
        const response = await ApiService.post(`${publicConfig.urls.api}/projectDesignDiagrams/${diagramId}/sync`, {
            transactions,
            pointer,
        });

        if (!response?.success) {
            throw new Error(`ProjectDiagramService.sync failed: ${response?.error}`);
        }

        const result: {
            transactions: Transaction[];
        } = {
            transactions: response.transactions,
        };

        return result;
    };
}

export { ProjectDiagramService };
