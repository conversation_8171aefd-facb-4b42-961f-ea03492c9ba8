import { ApiService } from 'services/ApiService';

import { publicConfig } from 'config/public-config';

export type Reaction = {
    reaction: string;
    createdBy: string;
    createdAt: string;
};

const ReactionService = {
    react: async (collection: string, id: string, reaction: string) => {
        await ApiService.post(`${publicConfig.urls.backend}/api/${collection}/${id}/reactions`, {
            reaction,
        });
    },
};

export { ReactionService };
