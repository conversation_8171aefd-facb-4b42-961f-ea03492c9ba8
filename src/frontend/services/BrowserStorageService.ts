type BrowserStorageType = 'local' | 'session';

const getStorage = (storage: BrowserStorageType) => {
    if (storage === 'local') {
        return localStorage;
    }

    if (storage === 'session') {
        return sessionStorage;
    }

    return null;
};

const BrowserStorageService = (storage: BrowserStorageType) => ({
    store: (key: string, object: any) => {
        try {
            getStorage(storage)?.setItem(key, JSON.stringify(object));
        } catch {
            /* empty */
        }
    },

    get: <T = any>(key: string): T | null | undefined => {
        try {
            const object = getStorage(storage)?.getItem(key);

            return object ? JSON.parse(object) : null;
        } catch {
            /* empty */
        }
    },

    clear: (key: string) => {
        try {
            getStorage(storage)?.removeItem(key);
        } catch {
            /* empty */
        }
    },
});

export { BrowserStorageService };
