import { mutate } from 'swr';
import DayJS from 'dayjs';

import { readLocalStorageValue } from '@mantine/hooks';

import { ComponentChatRating, SearchAgentMessage, SearchThread } from 'models';

import { ApiService } from 'services/ApiService';
import { UserHelpers } from 'helpers/UserHelpers';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { AIServiceHelpers } from 'helpers/AIServiceHelpers';

import { publicConfig } from '@public-config';

import { state as currentUserState } from 'state/current-user';

const ACTIVE_THREAD_KEY = 'ai-search-agent-active-thread';
const RATING_KEY = 'ai-search-agent-rating';

export type SearchThreadListProps = {
    page?: number;
    depth?: number;
    limit?: number;
    withExhibitorMatch?: boolean;
    withoutInternal?: boolean;
    removeEmptyThreads?: boolean;
    onlyWithEmail?: boolean;
    fromDate?: number;
    toDate?: number;
    eventId?: string | null;
};

const SearchAgentService = {
    swr: {
        searchThread: (searchThreadId: string) => `searchThreads/${searchThreadId}`,
        searchThreads: (params: URLSearchParams) => `searchThreads/${params.toString()}`,
        searchThreadsForCurrentUser: () => `searchThreads/user`,
    },

    activeThread: {
        key: ACTIVE_THREAD_KEY,
    },

    ratings: {
        key: RATING_KEY,

        // we are using useLocalStorage to rate to give instant feedback to the user

        track: (message: SearchAgentMessage, rating: ComponentChatRating) => {
            InternalTrackingService.track('ai.plan.rate', {
                ...message,
                rating,
            });
        },
    },

    ask: (question: string, searchThreadId: string) => {
        const event = readLocalStorageValue({ key: UserHelpers.localStorageKey.event });

        InternalTrackingService.track('ai.plan.ask', {
            question,
        });

        return AIServiceHelpers.handleStream2(
            (params) =>
                ApiService.post(`${publicConfig.urls.api}/search-agent2`, params, {
                    headers: { Accept: 'text/event-stream' },
                }),
            {
                question,
                id: searchThreadId,
                event: event || undefined,
            },
        );
    },

    getListParams: ({
        depth = 0,
        limit = 24,
        page = 1,
        withExhibitorMatch,
        withoutInternal,
        removeEmptyThreads,
        onlyWithEmail,
        fromDate,
        toDate,
        eventId,
    }: SearchThreadListProps) => {
        const params = new URLSearchParams();

        params.set('depth', depth.toString());
        params.set('limit', limit.toString());
        params.set('page', page.toString());

        if (withExhibitorMatch) {
            params.set('where[exhibitorMatch][exists]', 'true');
        }

        if (withoutInternal) {
            params.set('where[internal][not_equals]', 'true');

            params.set('where[or][0][createdBy][exists]', 'false');
            params.set('where[or][1][createdBy.internal][not_equals]', 'true');
        }

        if (removeEmptyThreads) {
            params.set('where[thread.input][exists]', 'true');
        }

        if (onlyWithEmail) {
            params.set('where[email][exists]', 'true');
        }

        if (fromDate) {
            params.set('where[createdAt][greater_than_equal]', DayJS(fromDate).startOf('day').toDate().toISOString());
        }

        if (toDate) {
            params.set('where[createdAt][less_than_equal]', DayJS(toDate).endOf('day').toDate().toISOString());
        }

        if (eventId) {
            params.set('where[event][equals]', eventId);
        }

        return params;
    },

    list: async (props: SearchThreadListProps) => {
        const params = SearchAgentService.getListParams(props);

        return await ApiService.get<{ docs: SearchThread[]; totalPages: number; totalDocs: number }>(
            `${publicConfig.urls.api}/searchThreads?${params.toString()}`,
        );
    },

    get: async (searchThreadId: string) => {
        return await ApiService.get<SearchThread>(`${publicConfig.urls.api}/searchThreads/${searchThreadId}`);
    },

    create: async (data?: Partial<SearchThread>) => {
        const response = await ApiService.post<{ doc: SearchThread }>(`${publicConfig.urls.api}/searchThreads`, data);

        const searchThreadId = response.doc.id;

        return searchThreadId;
    },

    update: async (searchThreadId: string, data: Partial<SearchThread>) => {
        await ApiService.patch(`${publicConfig.urls.api}/searchThreads/${searchThreadId}`, data);
    },

    updateCreatedBy: async (searchThreadId: string) => {
        if (!currentUserState.user) return;

        await ApiService.patch(`${publicConfig.urls.api}/searchThreads/${searchThreadId}`, {
            createdBy: currentUserState.user.id,
        });
    },

    listForCurrentUser: async () => {
        if (!currentUserState.user) return { docs: [] };

        return await ApiService.get<{ docs: SearchThread[] }>(
            `${publicConfig.urls.api}/searchThreads?where[createdBy][equals]=${currentUserState.user.id}&pagination=false`,
        );
    },

    refresh: async (searchThreadId: string) => {
        await mutate(SearchAgentService.swr.searchThread(searchThreadId), undefined, { revalidate: true });
    },

    refreshHistory: async () => {
        await mutate(SearchAgentService.swr.searchThreadsForCurrentUser());
    },

    delete: async (searchThreadId: string) => {
        await ApiService.post(`${publicConfig.urls.api}/searchThreads/${searchThreadId}/delete`);
        await mutate(SearchAgentService.swr.searchThread(searchThreadId));
        await mutate(SearchAgentService.swr.searchThreadsForCurrentUser());
    },
};

export { SearchAgentService };
