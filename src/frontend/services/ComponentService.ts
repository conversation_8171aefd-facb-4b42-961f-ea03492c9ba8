import {
    Component,
    ComponentQuery,
    ComponentSearchScore,
    ComponentType,
    ComponentVisibility,
    SortType,
    Team,
} from 'models';

import { ApiService } from './ApiService';
import { UserService } from 'services/UserService';
import { ServiceHelpers } from 'helpers/ServiceHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { publicConfig } from '@public-config';
import { CryptoHelpers } from 'helpers/CryptoHelpers';
import { IncomingMessage } from 'http';

export type ComponentListType = {
    depth?: number;
    page?: number;
    limit?: number;
    sort?: string;
    manufacturer?: string;
    noManufacturer?: boolean;
    team?: string;
    visibility?: ComponentVisibility;
    showDeleted?: boolean;
    showArchived?: boolean;
    showPrivate?: boolean;
    type?: ComponentType;
    req?: IncomingMessage;
};

// safe to use depth=1 b/c we set maxDepth on relationship fields
class ComponentService {
    static controller: AbortController | null = null;

    static navigate = ServiceHelpers.createNavigator(ComponentHelpers.urls);

    static list = async ({
        depth = 1,
        page = 1,
        limit = 99,
        sort = '-createdAt',
        manufacturer,
        noManufacturer,
        team,
        visibility = ComponentVisibility.PUBLIC,
        showArchived,
        showDeleted,
        showPrivate,
        type,
        req,
    }: ComponentListType = {}) => {
        const searchParams = new URLSearchParams({
            depth: depth.toString(),
            page: page.toString(),
            limit: limit.toString(),
            sort,
        });

        if (manufacturer) {
            searchParams.set(`where[or][0][manufacturer][equals]`, manufacturer);
            searchParams.set(`where[or][1][distributors][equals]`, manufacturer);
        }

        if (noManufacturer) {
            searchParams.set(`where[manufacturer][equals]`, 'null');
        }

        if (team) {
            searchParams.set(`where[team][equals]`, team);
        }

        if (visibility) {
            searchParams.set(`where[visibility][equals]`, visibility);
        }

        if (showPrivate && visibility === ComponentVisibility.PUBLIC) {
            searchParams.delete(`where[visibility][equals]`);
        }

        if (!showArchived) {
            searchParams.set(`where[archivedAt][equals]`, 'null');
        }

        if (!showDeleted) {
            searchParams.set(`where[deletedAt][equals]`, 'null');
        }

        if (type) {
            searchParams.set(`where[type][equals]`, type);
        }

        return await ApiService.get<{ docs: Component[] }>(
            `${publicConfig.urls.api}/components?${searchParams.toString()}`,
            { req },
        );
    };

    static getRecentlyAdded = async (limit = 8, req?: IncomingMessage) => {
        return await ComponentService.list({ sort: '-createdAt', limit, visibility: ComponentVisibility.PUBLIC, req });
    };

    static search = async (
        {
            sort = SortType.RELEVANCE,
            limit,
            customWeights,
            ...query
        }: Partial<ComponentQuery> & {
            limit?: number;
            customWeights?: Partial<Record<ComponentSearchScore, number>>;
        } = {},
        page = 0,
    ) => {
        console.log(`${publicConfig.urls.api}/components/search`);
        const urlAsObject = new URL(`${publicConfig.urls.api}/components/search`);

        const params: Record<string, string> = {
            query: JSON.stringify(query),
            page: page.toString(),
            sort: sort,
        };

        if (limit) {
            params.limit = limit.toString();
        }

        if (customWeights) {
            params.customWeights = JSON.stringify(customWeights);
        }

        urlAsObject.search = new URLSearchParams(params).toString();

        const result = await ApiService.get(urlAsObject.toString());

        const { docs = [] } = result;

        const combinedDocs = docs.map((doc: any, index: number) => ({
            ...doc,
            __position: 48 * page + (index + 1),
        }));

        return {
            ...result,
            docs: combinedDocs,
        };
    };

    static create = async (payload: any, depth: number = 1) => {
        const response = await ApiService.post(`${publicConfig.urls.api}/components?depth=${depth}`, payload);

        await UserService.refreshProgress();

        return response?.doc;
    };

    static get = async (
        componentId: string,
        {
            depth = 1,
            skipAccessCheck = false,
            req,
        }: { depth?: number; skipAccessCheck?: boolean; req?: IncomingMessage } = {},
    ) => {
        const hash = skipAccessCheck ? CryptoHelpers.unsafeSimpleHash(componentId) : '';

        return ApiService.get(`${publicConfig.urls.api}/components/${componentId}?depth=${depth}&hash=${hash}`, {
            req,
        });
    };

    static getMultiple = async (componentIds: string[]) => {
        const params = componentIds.map((id) => `where[id][in]=${id}`).join('&');

        return ApiService.get<{
            docs: Component[];
        }>(`${publicConfig.urls.api}/components?${params}`);
    };

    static delete = async (componentId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/components/${componentId}/delete`);
    };

    static getExisting = async ({
        type,
        productIdentifier,
        manufacturer,
        depth = 1,
    }: {
        type: string;
        productIdentifier: string;
        manufacturer: string;
        depth?: number;
    }) => {
        const query = [
            `where[type][equals]=${type}`,
            `where[productIdentifier][equals]=${productIdentifier}`,
            `where[manufacturer][equals]=${manufacturer}`,
        ];
        const response = await ApiService.get(
            `${publicConfig.urls.api}/components?${query.join('&')}&depth=${depth}&limit=1`,
        );

        return response?.docs[0];
    };

    static getFiles = async (componentId: string, query?: ComponentVisibility) => {
        const queryString = query ? `?${query}` : '';
        return ApiService.get(`${publicConfig.urls.api}/components/${componentId}/files${queryString}`);
    };

    static update = async (componentId: string, payload: any, depth: number = 1) => {
        return await ApiService.patch(`${publicConfig.urls.api}/components/${componentId}?depth=${depth}`, payload);
    };

    static getCount = async (teamComponents?: boolean, req?: IncomingMessage) => {
        return ApiService.get(
            `${publicConfig.urls.api}/components/count${teamComponents ? `?teamComponents=true` : ''}`,
            { req },
        );
    };

    static addDistributor = async (componentId: string, payload: { distributorId: string }) => {
        return ApiService.post(`${publicConfig.urls.api}/components/${componentId}/add-distributor`, payload);
    };

    static getProductSeries = async () => {
        return ApiService.get(`${publicConfig.urls.api}/components/product-series`);
    };

    static getProductSeriesComponents = async ({
        manufacturerId,
        productSeries,
    }: {
        manufacturerId: string;
        productSeries: string;
    }) => {
        const encodedProductSeries = encodeURIComponent(productSeries.replace('/', '++--++'));

        return ApiService.get(
            `${publicConfig.urls.api}/components/product-series/${manufacturerId}/${encodedProductSeries}`,
        );
    };

    static updateMultipleComponents = async ({
        componentIds,
        update,
        newFiles,
    }: {
        componentIds: string[];
        update: Component;
        newFiles: Component['files'];
    }) => {
        return ApiService.post(`${publicConfig.urls.api}/components/bulk-edit`, { componentIds, update, newFiles });
    };

    static isTeamComponent = (component?: Component) => {
        return component && !component.manufacturer && component.visibility === ComponentVisibility.PRIVATE;
    };

    static getFeaturedProducts = async (req?: IncomingMessage): Promise<Component[]> =>
        ApiService.get(`${publicConfig.urls.api}/components/featured-products`, { req });

    static getAll = async (teamId: Team['id']) =>
        ApiService.get(`${publicConfig.urls.api}/components?where[team][equals]=${teamId}`);
}

export { ComponentService };
