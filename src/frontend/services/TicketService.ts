import { ApiService } from './ApiService';
import { publicConfig } from '@public-config';

class TicketService {
    static createTicket = ({
        subject,
        userMessage,
        throwError = false,
    }: {
        subject: string;
        userMessage: string;
        throwError?: boolean;
    }): Promise<void> =>
        ApiService.post(`${publicConfig.urls.api}/create-ticket`, { subject, userMessage }, { throwError });
}

export { TicketService };
