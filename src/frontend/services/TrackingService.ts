import TagManager from 'react-gtm-module';

const GA4_CUSTOM_EVENT = 'user_interaction';

class TrackingService {
    static trackGA4Event = (data: { eventName: string; eventData?: string }) => {
        if (typeof window === 'undefined') {
            return;
        }

        TagManager.dataLayer({
            dataLayer: {
                event: GA4_CUSTOM_EVENT,
                ...data,
            },
        });
    };

    static trackUseComponent = (component: string) => {
        TrackingService.trackGA4Event({
            eventName: 'use_component',
            eventData: component,
        });
    };

    static trackDownloadDiagram = (file: 'png' | 'pdf') => {
        TrackingService.trackGA4Event({
            eventName: 'download_diagram',
            eventData: file,
        });
    };

    static trackShareDiagram = (action: 'invite_user' | 'public_link') => {
        TrackingService.trackGA4Event({
            eventName: 'invite_user',
            eventData: action,
        });
    };

    static trackBillOfMaterials = () => {
        TrackingService.trackGA4Event({
            eventName: 'bill_of_materials',
        });
    };

    static trackComponentChat = (
        source: 'datasheet_top' | 'datasheet_files' | 'floating_button' | 'diagram_component',
    ) => {
        TrackingService.trackGA4Event({
            eventName: 'component_chat',
            eventData: source,
        });
    };

    static trackDiagramComment = (action: 'comment' | 'reply') => {
        TrackingService.trackGA4Event({
            eventName: 'diagram_commment',
            eventData: action,
        });
    };
}

export { TrackingService };
