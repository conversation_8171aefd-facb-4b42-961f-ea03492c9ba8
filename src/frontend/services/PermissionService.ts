import { snapshot } from 'valtio';
import { Permission, PermissionComponent, PermissionDiagram, PermissionProject } from 'models';
import { permissionsState } from 'state/current-project-permissions';

class PermissionService {
    static canPermissions = (toCheckPermissions: Permission | Permission[], givenPermissions: Permission[]) => {
        if (Array.isArray(toCheckPermissions)) {
            return toCheckPermissions.some((permission) => givenPermissions.includes(permission));
        }

        if (toCheckPermissions.includes('project') && givenPermissions.includes(PermissionProject.ALL)) return true;
        if (toCheckPermissions.includes('project') && givenPermissions.includes(PermissionProject.ADMIN)) return true;

        if (toCheckPermissions.includes('component') && givenPermissions.includes(PermissionComponent.ALL)) return true;

        if (givenPermissions.includes(toCheckPermissions)) return true;

        return false;
    };

    static can = (toCheck: PermissionDiagram): boolean => {
        const permissions = snapshot(permissionsState).permissions;

        return permissions[toCheck] ?? false;
    };
}

export { PermissionService };
