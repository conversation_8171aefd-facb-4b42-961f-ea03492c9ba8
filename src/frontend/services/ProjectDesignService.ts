import { CollectionFile, CollectionFileWithFile, ProjectDesign } from 'models';
import { snapshot } from 'valtio';

import { ApiService } from './ApiService';

import { state as currentProjectDesignState } from 'state/current-project-design';

import { FileService } from 'components/diagram/services/FileService';

import { publicConfig } from '@public-config';
import { getId } from 'helpers/getId';

class ProjectDesignService {
    static get = async (projectDesignId: string, depth: number = 0): Promise<ProjectDesign> => {
        if (currentProjectDesignState.projectDesign?.id === projectDesignId) {
            return snapshot(currentProjectDesignState.projectDesign) as ProjectDesign;
        }

        return ApiService.get(`${publicConfig.urls.api}/projectDesigns/${projectDesignId}?depth=${depth}`);
    };

    static updateFile = async ({
        designId,
        designFileId,
        data,
        designFiles,
    }: {
        designId: string;
        designFileId: string;
        data: Partial<CollectionFile>;
        designFiles: CollectionFileWithFile[];
    }): Promise<ProjectDesign> => {
        const files = designFiles?.map((file) =>
            file.id === designFileId
                ? {
                      ...file,
                      file: getId(file.file),
                      ...data,
                  }
                : { ...file, file: getId(file.file) },
        );

        const result = await ApiService.patch(`${publicConfig.urls.api}/projectDesigns/${designId}`, { files });

        await FileService.mutateDesignFiles(designId);

        return result;
    };

    static getFiles = async (designId: string) => {
        return await ApiService.get(`${publicConfig.urls.api}/projectDesigns/${designId}/files`);
    };
}

export { ProjectDesignService };
