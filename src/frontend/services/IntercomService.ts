import { IntercomChannel, IntercomChannelListParams, IntercomMessage } from 'models';

import { mutate } from 'swr';

import { ApiService } from 'services/ApiService';
import { LocalStorageService } from 'services/LocalStorageService';

import { IntercomHelpers } from 'helpers/IntercomHelpers';

import { intercomState } from 'components/intercom/state/intercom';

import { publicConfig } from '@public-config';
import { InternalTrackingService } from 'services/InternalTrackingService';

const IntercomService = {
    open: (channelId: string = '') => {
        intercomState.opened = true;

        InternalTrackingService.track('intercom.open', {
            channelId: channelId || intercomState.activeChannel,
        });

        if (channelId) {
            intercomState.activeChannel = channelId;
        }
    },

    close: () => {
        intercomState.opened = false;

        InternalTrackingService.track('intercom.close', {
            channelId: intercomState.activeChannel,
        });
    },

    toggle: () => {
        if (intercomState.opened) {
            InternalTrackingService.track('intercom.close', {
                channelId: intercomState.activeChannel,
            });
        } else {
            InternalTrackingService.track('intercom.open', {
                channelId: intercomState.activeChannel,
            });
        }

        intercomState.opened = !intercomState.opened;
    },

    getChannels: async (type: string, id: string) => {
        const params = [`where[type][equals]=${type}`, `where[${type}][equals]=${id}`, 'sort=name', 'limit=999'].join(
            '&',
        );

        const response = await ApiService.get<{
            docs: IntercomChannel[];
        }>(`${publicConfig.urls.api}/intercomChannels?${params}`);

        return {
            ...response,
            channels: response.docs,
        };
    },

    getChannelMessages: async (channelId: string) => {
        const params = [`where[channel][equals]=${channelId}`, 'sort=createdAt', 'limit=999'].join('&');
        const response = await ApiService.get<{
            docs: IntercomMessage[];
        }>(`${publicConfig.urls.api}/intercomMessages?${params}`);

        return {
            ...response,
            messages: response.docs,
        };
    },

    getChannelForTeam: async (type: string, id: string, teamId: string) => {
        const params = [
            `where[type][equals]=${type}`,
            `where[${type}][equals]=${id}`,
            `where[team][equals]=${teamId}`,
            'sort=-createdAt',
        ].join('&');

        const response = await ApiService.get<{
            docs: IntercomChannel[];
        }>(`${publicConfig.urls.api}/intercomChannels?${params}`);

        return {
            ...response,
            channel: response.docs[0],
        };
    },

    adminList: async (data: Partial<IntercomChannelListParams>) => {
        const response = await ApiService.get<{
            docs: IntercomChannel[];
            totalPages: number;
            totalDocs: number;
        }>(`${publicConfig.urls.api}/intercomChannels/admin-list?query=${JSON.stringify(data)}`);

        return response;
    },

    getTooltipVisibility: (type: string) => {
        const now = new Date();
        const key = `intercom-tooltip-${now.getUTCFullYear()}-${now.getUTCMonth()}-${type}`;

        return LocalStorageService.get(key) as undefined | boolean;
    },

    setTooltipVisibility: (type: string, value: boolean) => {
        const now = new Date();
        const key = `intercom-tooltip-${now.getUTCFullYear()}-${now.getUTCMonth()}-${type}`;

        LocalStorageService.store(key, value);
    },

    refreshProjectChannels: async (projectId: string) => {
        await mutate(IntercomHelpers.swr.channels.project(projectId));
    },

    refreshCompanyChannels: async (companyId: string) => {
        await mutate(IntercomHelpers.swr.channels.company(companyId));
    },

    refreshComponentChannels: async (componentId: string) => {
        await mutate(IntercomHelpers.swr.channels.component(componentId));
    },

    refreshChannelMessages: async (channelId: string) => {
        await mutate(IntercomHelpers.swr.messages(channelId));
    },

    setOptimisticChannels: (
        type: 'project' | 'company' | 'component',
        id: string,
        updater: (channels: IntercomChannel[]) => IntercomChannel[],
    ) => {
        mutate<{
            docs: IntercomChannel[];
            channels: IntercomChannel[];
        }>(
            IntercomHelpers.swr.channels[type](id),
            async (data) => {
                if (data) {
                    data.docs = updater(data.docs);
                    data.channels = updater(data.channels);
                }

                return { ...data } as any;
            },
            {
                revalidate: false,
            },
        ).then();
    },

    setOptimisticChannelMessages: (channelId: string, updater: (messages: IntercomMessage[]) => IntercomMessage[]) => {
        mutate<{
            docs: IntercomMessage[];
            messages: IntercomMessage[];
        }>(
            IntercomHelpers.swr.messages(channelId),
            (data) => {
                if (data) {
                    data.docs = updater(data.docs);
                    data.messages = updater(data.messages);
                }

                return { ...data } as any;
            },
            {
                revalidate: false,
            },
        ).then();
    },

    createChannel: async ({
        type,
        companyId,
        componentId,
        projectId,
        content,
        files,
        userId,
        userPartOfCompany,
    }: {
        type: 'component' | 'company' | 'project';
        companyId: string;
        componentId?: string;
        projectId?: string;
        content: any;
        files: any;
        userId?: string;
        userPartOfCompany: boolean;
    }) => {
        const { doc: channel } = await ApiService.post<{
            doc: IntercomChannel;
        }>(`${publicConfig.urls.api}/intercomChannels`, {
            type,
            project: projectId,
            component: componentId,
            company: companyId,
            createdBy: userId,
            access: {
                company: companyId,
            },
        });

        IntercomService.setOptimisticChannels(type, projectId ?? componentId ?? companyId, (channels) => {
            return [...channels, channel];
        });

        await IntercomService.reply({
            channelId: channel.id,
            content,
            files,
            type,
            userPartOfCompany,
        });

        InternalTrackingService.track('intercom.createChannel', {
            type,
            channelId: channel.id,
        });

        return channel;
    },

    viewChannel: async (channelId: IntercomChannel['id']) => {
        await ApiService.post(`${publicConfig.urls.api}/intercomChannels/${channelId}/view`);
    },

    reply: async ({
        channelId,
        content,
        files,
        type,
        userPartOfCompany,
    }: {
        channelId: string;
        content: any;
        files: any;
        type: 'component' | 'company' | 'project';
        userPartOfCompany: boolean;
    }) => {
        const { doc: message } = await ApiService.post<{
            doc: IntercomMessage;
        }>(`${publicConfig.urls.api}/intercomMessages`, {
            channel: channelId,
            content: [
                {
                    blockType: 'message',
                    content: JSON.stringify(content),
                    files: files,
                },
            ],
        });

        IntercomService.setOptimisticChannelMessages(channelId, (messages) => {
            const exists = messages.some((needle) => needle.id === message.id);

            return exists ? messages : [...messages, message];
        });

        InternalTrackingService.track('intercom.message', {
            type,
            from: userPartOfCompany ? 'company' : 'user',
            channelId,
        });
    },

    unlinkCompany: async (messageId: string) => {
        await ApiService.patch<{
            doc: IntercomMessage;
        }>(`${publicConfig.urls.api}/intercomMessages/${messageId}`, {
            createdByCompany: null,
        });
    },
};

export { IntercomService };
