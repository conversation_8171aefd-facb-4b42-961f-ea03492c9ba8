import { IntercomChannelWithLatestMessage } from 'models';

import { ApiService } from 'services/ApiService';

import { publicConfig } from '@public-config';

import { CompanyProfile } from 'models';

export type SupportChannel = {
    company: CompanyProfile | string;
    chat: IntercomChannelWithLatestMessage[];
    intercom: {
        project: IntercomChannelWithLatestMessage[];
        component: IntercomChannelWithLatestMessage[];
        company: IntercomChannelWithLatestMessage[];
    };
};

const SupportCenterService = {
    get: async (company?: string) => {
        const endpoint = company ? `/support-center/${company}` : '/support-center';

        return ApiService.get<SupportChannel[]>(`${publicConfig.urls.api}${endpoint}`);
    },
};

export { SupportCenterService };
