import { ModalProps } from '@mantine/core';
import { modals, closeModal, openContextModal } from '@mantine/modals';

import { LoginModalProps } from 'components/modals/LoginModal';
import { LocalNotificationService } from 'services/LocalNotificationService';

type LocalModalProps = Pick<
    ModalProps,
    'title' | 'zIndex' | 'withCloseButton' | 'closeOnClickOutside' | 'closeOnEscape'
> & { modalId?: string };

type LocalLoginModalProps = Omit<LoginModalProps, 'closeModal'>;

const ModalService = {
    open: modals.open,

    openContextModal: openContextModal,

    openLoginModal: (props?: LocalLoginModalProps & LocalModalProps) => {
        const {
            zIndex = 999,
            withCloseButton = false,
            closeOnClickOutside = true,
            closeOnEscape = true,
            modalId = 'login',
            ...innerProps
        } = props || {};

        openContextModal({
            modal: 'login',
            modalId,
            zIndex,
            withCloseButton,
            closeOnClickOutside,
            closeOnEscape,
            innerProps: {
                closeModal: () => closeModal(modalId),
                afterSubmit: () => {
                    LocalNotificationService.showSuccess({
                        message: 'We have sent you a login link!',
                    });
                },
                ...innerProps,
            },
        });
    },

    closeLoginModal: () => {
        closeModal('login');
    },

    closeAll: modals.closeAll,
};

export { ModalService };
