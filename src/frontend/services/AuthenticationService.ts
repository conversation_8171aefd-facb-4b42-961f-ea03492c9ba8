import Router from 'next/router';

import { ApiService } from './ApiService';

import { TokenMeta } from 'models';
import { publicConfig } from '@public-config';

const AuthenticationService = {
    logout: async (reason?: 'expired') => {
        await ApiService.post(`${publicConfig.urls.api}/users/magic-logout`);

        if (reason === 'expired') {
            window.location.href = `/login?reason=expired&redirect=${window.location.pathname}`;

            return;
        }

        window.location.href = '/login';
    },

    login: async (token: string) => {
        return await ApiService.post(`${publicConfig.urls.api}/users/magic-login-verify`, { token });
    },

    sendMagicTokenLogin: (email: string, otherData = {} as TokenMeta, redirect?: string) => {
        const currentUrl = location.toString();
        const incomingRedirect = Router.query.redirect || redirect;

        const url = incomingRedirect
            ? `${location.origin}${incomingRedirect}`
            : currentUrl.includes('/login')
              ? location.origin
              : currentUrl;

        return ApiService.post(`${publicConfig.urls.api}/users/magic-login`, {
            email,
            source: url,
            origin: `${location.origin}/token`,
            redirect: url,
            ...otherData,
        });
    },

    getMagicLoginToken: (email: string, code: string) =>
        ApiService.post(
            `${publicConfig.urls.api}/users/magic-login-get-token`,
            {
                email,
                code,
            },
            { throwError: true, hideConsoleError: true },
        ),
};

export { AuthenticationService };
