import { notifications } from '@mantine/notifications';
import { toast } from 'sonner';

import { ActionIcon, Button, Flex, Group, Paper, Text, Tooltip } from '@mantine/core';
import { IoBugSharp } from 'react-icons/io5';

import { RouterService } from 'services/RouterService';

import type { NotificationProps, NotificationId } from './LocalNotificationService.types';

const LocalNotificationService = {
    showNotification: (args: { title?: string; description: string; url?: string }) => {
        const { title, description, url } = args;

        toast.custom(
            (id) => (
                <Paper
                    w="320"
                    p="xs"
                    onClick={() => {
                        if (url) {
                            RouterService.push(url).then();
                        }

                        toast.dismiss(id);
                    }}
                    withBorder
                    style={{
                        borderColor: 'var(--mantine-color-gray-2)',
                    }}
                >
                    {title && (
                        <Text fz={12} fw={600}>
                            {title}
                        </Text>
                    )}
                    <Text fz={12} dangerouslySetInnerHTML={{ __html: description }} />
                </Paper>
            ),
            {
                duration: 5000,
            },
        );
    },

    show: (data: NotificationProps): NotificationId => {
        const { actions = [], message, ..._data } = data;

        const notificationId = notifications.show({
            ..._data,
            message:
                actions.length > 0 ? (
                    <Flex justify="space-between" gap={'xs'} align="center">
                        <span>{message}</span>
                        <Group>
                            {actions.map(({ icon, onClick, tooltip, label }, index) => {
                                if (tooltip && icon) {
                                    return (
                                        <Tooltip key={index} label={tooltip}>
                                            <ActionIcon
                                                key={index}
                                                radius="md"
                                                variant="outline"
                                                color="white"
                                                onClick={() => {
                                                    onClick();
                                                    LocalNotificationService.hide(notificationId);
                                                }}
                                            >
                                                {icon}
                                            </ActionIcon>
                                        </Tooltip>
                                    );
                                }

                                if (label) {
                                    return (
                                        <Button
                                            key={index}
                                            size="compact-xs"
                                            variant="outline"
                                            color="white"
                                            onClick={() => {
                                                onClick();
                                                LocalNotificationService.hide(notificationId);
                                            }}
                                        >
                                            {label}
                                        </Button>
                                    );
                                }

                                return null;
                            })}
                        </Group>
                    </Flex>
                ) : (
                    message
                ),
        });

        return notificationId;
    },

    showInfo: (data: NotificationProps): NotificationId => {
        return LocalNotificationService.show({
            ...data,
            color: 'transparent',
        });
    },

    showSuccess: (data: NotificationProps): NotificationId => {
        return LocalNotificationService.show({
            ...data,
            color: 'transparent',
        });
    },

    showError: (data: NotificationProps): NotificationId => {
        return LocalNotificationService.show({
            ...data,
            color: 'transparent',
        });
    },

    showPersistantBug: (data: NotificationProps): NotificationId => {
        return LocalNotificationService.show({
            autoClose: false,
            color: 'red',
            style: {
                borderRadius: 'var(--mantine-radius-sm)',
            },
            icon: <IoBugSharp />,
            ...data,
        });
    },

    hide: (id: NotificationId | undefined) => {
        if (!id) return;

        notifications.hide(id);
    },

    clean: notifications.clean,
};

export { LocalNotificationService };
