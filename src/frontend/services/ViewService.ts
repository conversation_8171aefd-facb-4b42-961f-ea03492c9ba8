import { ApiService } from 'services/ApiService';
import { publicConfig } from '@public-config';

const ViewService = {
    getRecentlyViewedComponents: async () => {
        const components = await ApiService.get<
            {
                id: string;
                page: {
                    relationTo: 'components';
                    value: string;
                };
            }[]
        >(`${publicConfig.urls.api}/views/components`);

        return {
            components: components.map((component) => ({
                ...component,
                component: component.page.value,
            })),
        };
    },
};

export { ViewService };
