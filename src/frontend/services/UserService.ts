import { mutate } from 'swr';
import { snapshot } from 'valtio';
import Router from 'next/router';
import { IncomingMessage } from 'http';

import { ApiService } from 'services/ApiService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { TeamInfo, User, UserInvitation, UserProgressItem } from 'models';

import { UserHelpers } from 'helpers/UserHelpers';

import { state as currentUserState } from 'state/current-user';

import { publicConfig } from '@public-config';

export type UserInvitationProps = {
    email?: string;
    userId?: string;
    exhibitorMatchLeadId?: string;
    companyId?: string;
};

const UserService = {
    getCurrentUser: async (): Promise<User | null> => {
        const response = await ApiService.get(`${publicConfig.urls.api}/users/me`);

        return response?.user ?? null;
    },

    refreshCurrentUser: async () => {
        currentUserState.user = await UserService.getCurrentUser();
    },

    get: async (userId: string, req?: IncomingMessage): Promise<User> => {
        if (currentUserState.user?.id === userId) {
            return snapshot(currentUserState.user) as User;
        }

        return ApiService.get(`${publicConfig.urls.api}/users/${userId}?depth=0`, { req });
    },

    getMultiple: async (userIds: string[]) => {
        if (userIds.length === 0) {
            return {
                docs: [],
                users: [],
            };
        }

        const response = await ApiService.get<{
            docs: User[];
        }>(`${publicConfig.urls.api}/users/get-by-ids?${userIds.map((id) => `ids=${id}`).join('&')}`);

        return {
            ...response,
            users: response.docs,
        };
    },

    update: async (userId: string, payload: Partial<User>) => {
        await ApiService.patch(`${publicConfig.urls.api}/users/${userId}`, payload);
        await UserService.refreshCurrentUser();
    },

    getTeams: async (req?: IncomingMessage): Promise<TeamInfo[]> => {
        return ApiService.get(`${publicConfig.urls.api}/users/get-teams`, { req });
    },

    switchTeam: async (teamId: string) => {
        InternalTrackingService.track('team.switch', {
            team: teamId,
        });

        return ApiService.post(`${publicConfig.urls.api}/users/switch-team`, { teamId });
    },

    heartbeat: async () => {
        return ApiService.get<{
            authenticated: boolean;
        }>(`${publicConfig.urls.api}/users/heartbeat`);
    },

    ping: async () => {
        if (window.location.pathname === '/sorry') {
            return;
        }

        return ApiService.post(`${publicConfig.urls.api}/users/ping`, Router.query, {
            keepalive: true,
        });
    },

    pingProject: (projectId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/users/ping-project`, {
            projectId,
        });
    },

    pingProduct: (componentId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/users/ping-product`, {
            componentId,
        });
    },

    sendNewUserInvites: async (emails: string[], teamId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/users/send-new-user-invites`, {
            emails,
            teamId,
            source: location.toString(),
            origin: `${location.origin}/token`,
        });
    },

    getProgress: async () => {
        return ApiService.get(`${publicConfig.urls.api}/users/me/get-progress`);
    },

    refreshProgress: async () => {
        await mutate(UserHelpers.swr.progress(), undefined, {
            revalidate: true,
        });
    },

    completeProgressItem: async (item: UserProgressItem) => {
        await ApiService.post(`${publicConfig.urls.api}/users/me/progress/${item}/complete`);
    },

    finishSignup: async () => {
        await ApiService.post(`${publicConfig.urls.api}/users/finish-signup`, {}, { throwError: true });
    },

    dismissFeatureSuggestion: async (user: User, feature: string) => {
        InternalTrackingService.track('user.dismissFeatureSuggestion', {
            feature: feature,
        });

        const dismissedFeatureSuggestions = JSON.parse(JSON.stringify(user.dismissedFeatureSuggestions || []));

        dismissedFeatureSuggestions.push({
            feature: feature,
            data: new Date().toISOString(),
        });

        await UserService.update(user.id, {
            dismissedFeatureSuggestions,
        });
    },

    dismissCompanySuggestions: async (userId: string) => {
        InternalTrackingService.track('user.dismissCompanySuggestions');

        await UserService.update(userId, {
            companySuggestionsDismissed: true,
        });
    },

    dismissTeamSuggestions: async (userId: string) => {
        InternalTrackingService.track('user.dismissTeamSuggestions');

        await UserService.update(userId, {
            teamSuggestionsDismissed: true,
        });
    },

    getUserInvitation: async ({ email, userId, exhibitorMatchLeadId, companyId }: UserInvitationProps) => {
        const params = new URLSearchParams();

        if (email) {
            params.set('where[email][equals]', email);
        }

        if (userId) {
            params.set('where[user][equals]', userId);
        }

        if (exhibitorMatchLeadId) {
            params.set('where[references.exhibitorMatchLead][equals]', exhibitorMatchLeadId);
        }

        if (companyId) {
            params.set('where[references.company][equals]', companyId);
        }

        return ApiService.get<{ docs: UserInvitation[] }>(
            `${publicConfig.urls.api}/userInvitations?${params.toString()}`,
        );
    },
};

export { UserService };
