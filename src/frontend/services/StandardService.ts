import { ApiService } from './ApiService';

import { publicConfig } from '@public-config';

class StandardService {
    static list = async () => {
        return await ApiService.get(`${publicConfig.urls.api}/standards?limit=9999&sort=name`);
    };

    static create = async (payload: any) => {
        return await ApiService.post(`${publicConfig.urls.api}/standards`, payload);
    };

    static get = async (standardId: string) => {
        return ApiService.get(`${publicConfig.urls.api}/standards/${standardId}`);
    };

    static update = async (standardId: string, payload: any) => {
        return await ApiService.patch(`${publicConfig.urls.api}/standards/${standardId}`, payload);
    };
}

export { StandardService };
