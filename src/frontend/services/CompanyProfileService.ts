import { ApiService } from './ApiService';
import { IncomingMessage } from 'http';

import { publicConfig } from '@public-config';

import {
    CompanyProfile,
    CompanyProfileStubFields,
    CompanyService,
    ManufacturerProfile,
    PublishedStatus,
    StubWithLocalFiles,
} from 'models';

import { currentProfileState } from 'components/company-profile/state/current-profile';

import { UserService } from 'services/UserService';

import { ServiceHelpers } from 'helpers/ServiceHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { LocalStorageService } from 'services/LocalStorageService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { mutate } from 'swr';

export type CompanyProfileListProps = {
    page?: number;
    depth?: number;
    query?: string;
    services?: CompanyService[];
    status?: CompanyProfile['status'];
    compliance?: CompanyProfile['compliance'];
    subscription?: CompanyProfile['subscription'];
    sort?: string;
    ids?: string[];
    internal?: boolean;
    limit?: number;
};

// safe to use depth=1 b/c we set maxDepth on relationship fields
const CompanyProfileService = {
    navigate: ServiceHelpers.createNavigator(CompanyProfileHelpers.urls),

    getSearchParams: (props?: CompanyProfileListProps) => {
        const {
            page,
            depth = 1,
            query,
            services = [],
            compliance = {},
            status,
            ids = [],
            sort = 'name',
            subscription,
            internal,
            limit,
        } = props ?? {};

        const searchParams = new URLSearchParams({
            depth: depth.toString(),
            page: page ? page.toString() : '1',
            limit: page ? '24' : '9999',
            sort,
        });

        if (query) {
            searchParams.set('where[name][like]', query);
        }

        if (status) {
            searchParams.set('where[status][equals]', status);
        }

        Object.entries(compliance).forEach(([key, value], index) => {
            searchParams.set(`where[or][${index}][compliance.${key}][equals]`, (value as boolean).toString());
        });

        if (services.length) {
            searchParams.set('where[services][in]', services.join(','));
        }

        if (subscription) {
            searchParams.set('where[subscription][equals]', subscription);
        }

        if (ids.length) {
            searchParams.set('where[id][in]', ids.join(','));
        }

        if (internal) {
            searchParams.set('where[team.createdBy.internal][equals]', 'true');
        }

        if (internal === false) {
            searchParams.set('where[team.createdBy.internal][not_equals]', 'true');
        }

        if (limit) {
            searchParams.set('limit', limit.toString());
        }

        return searchParams;
    },

    list: async (props?: CompanyProfileListProps, req?: IncomingMessage) => {
        const searchParams = CompanyProfileService.getSearchParams(props);

        return await ApiService.get(`${publicConfig.urls.api}/manufacturers?${searchParams.toString()}`, {
            req,
        });
    },

    getRecentlyJoined: async (limit = 8, req?: IncomingMessage) =>
        await CompanyProfileService.list({ sort: '-createdAt', limit, status: PublishedStatus.PUBLISHED }, req),

    refresh: async (profileId: string, data?: CompanyProfile): Promise<CompanyProfile> => {
        const updatedProfile =
            data ?? (await ApiService.get(`${publicConfig.urls.api}/manufacturers/${profileId}?depth=0`))!;

        await mutate(CompanyProfileHelpers.swr.profile(profileId), updatedProfile, { revalidate: false });

        if (profileId === currentProfileState.profile?.id) {
            currentProfileState.profile = updatedProfile;
        }

        return updatedProfile;
    },

    create: async (payload: any, depth: number = 1) => {
        InternalTrackingService.track('profile.create', {
            data: payload,
        });

        const result = await ApiService.post<{
            doc: CompanyProfile;
        }>(`${publicConfig.urls.api}/manufacturers?depth=${depth}`, payload, {
            throwError: true,
        });

        await UserService.refreshProgress();

        return result;
    },

    publish: async (id: string) => {
        InternalTrackingService.track('profile.publish', {
            profile: id,
        });

        return await ApiService.post(`${publicConfig.urls.api}/manufacturers/${id}/publish`);
    },

    get: async (manufacturerId: string, depth: number = 1, req?: IncomingMessage) => {
        const result = await ApiService.get(`${publicConfig.urls.api}/manufacturers/${manufacturerId}?depth=${depth}`, {
            req,
        });

        if (result) {
            CompanyProfileService.refresh(result.id, result);
        }

        return result;
    },

    getByTeam: async (teamId: string, depth: number = 1, req?: IncomingMessage) => {
        return ApiService.get<{ docs: ManufacturerProfile[] }>(
            `${publicConfig.urls.api}/manufacturers?limit=99&where[team][equals]=${teamId}&depth=${depth}`,
            { req },
        );
    },

    getBySlug: async (slug: string, depth: number = 1, req?: IncomingMessage) => {
        const matchId = `where[or][0][id][equals]=${slug}`;
        const matchSlug = `where[or][1][slug][equals]=${slug}`;

        return ApiService.get(`${publicConfig.urls.api}/manufacturers?${matchId}&${matchSlug}&depth=${depth}`, {
            req,
        });
    },

    searchPartnerProfiles: async ({
        query,
        excludeIds,
        limit = 99,
    }: {
        query: string;
        excludeIds?: string[];
        limit?: number;
    }) => {
        const params = new URLSearchParams({
            'where[or][0][name][like]': query,
            'where[or][1][services][like]': query,
            'where[or][2][serviceTags][like]': query,
            'where[or][3][applicationTags][like]': query,
            'where[status][equals]': PublishedStatus.PUBLISHED,
            'limit': limit.toString(),
            'sort': '-completeness',
        });

        if (excludeIds?.length) {
            params.set('where[id][not_in]', excludeIds.join(','));
        }

        return ApiService.get<{
            docs: CompanyProfile[];
        }>(`${publicConfig.urls.api}/manufacturers?${params.toString()}`);
    },

    getMultiple: async (manufacturerIds: string[]) => {
        const params = manufacturerIds.map((id) => `where[id][in]=${id}`).join('&');

        if (!manufacturerIds.length) {
            return { docs: [] };
        }

        return await ApiService.get(`${publicConfig.urls.api}/manufacturers?limit=99&${params}`);
    },

    getServiceTags: () => {
        return ApiService.get(`${publicConfig.urls.api}/manufacturers/service-tags`);
    },

    getApplicationTags: () => {
        return ApiService.get(`${publicConfig.urls.api}/manufacturers/application-tags`);
    },

    update: async (manufacturerId: string, payload: any, depth: number = 1) => {
        InternalTrackingService.track('profile.update', {
            data: payload,
        });

        const result = await ApiService.patch(
            `${publicConfig.urls.api}/manufacturers/${manufacturerId}?depth=${depth}`,
            payload,
        );

        if (result?.doc) {
            await CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    addSupportUser: async (manufacturerId: string, payload: any) => {
        InternalTrackingService.track('profile.update.addSupportUser', {
            profile: manufacturerId,
            data: payload,
        });

        return await ApiService.post(
            `${publicConfig.urls.api}/manufacturers/${manufacturerId}/add-support-user`,
            payload,
            {
                throwError: true,
            },
        );
    },

    requestAccess: async (manufacturerId: string) => {
        InternalTrackingService.track('profile.requestAccess', {
            profile: manufacturerId,
        });

        return await ApiService.post(`${publicConfig.urls.api}/manufacturers/${manufacturerId}/request-access`);
    },

    moveTeam: async (
        manufacturerId: string,
        payload: { teamId: string; userId: string; companyId: string; previewSecret?: string },
    ) => {
        InternalTrackingService.track('profile.moveTeam', {
            profile: manufacturerId,
            data: payload,
        });

        return await ApiService.post(`${publicConfig.urls.api}/manufacturers/${manufacturerId}/move-team`, payload);
    },

    denyAccess: async (manufacturerId: string, payload: { message: string; userId: string; userTeamId: string }) => {
        InternalTrackingService.track('profile.denyAccess', {
            profile: manufacturerId,
            data: payload,
        });

        return await ApiService.post(`${publicConfig.urls.api}/manufacturers/${manufacturerId}/deny-access`, payload);
    },

    addStub: async ({
        companyId,
        data,
        field,
    }: {
        companyId: string;
        data: StubWithLocalFiles;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.post(`${publicConfig.urls.api}/manufacturers/${companyId}/stub/${field}`, data);

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    editStub: async ({
        companyId,
        data,
        stubId,
        field,
    }: {
        companyId: string;
        stubId: string;
        data: Partial<StubWithLocalFiles>;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.patch(
            `${publicConfig.urls.api}/manufacturers/${companyId}/stub/${field}/${stubId}`,
            data,
        );

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    deleteStub: async ({
        companyId,
        stubId,
        field,
    }: {
        companyId: string;
        stubId: string;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.delete(
            `${publicConfig.urls.api}/manufacturers/${companyId}/stub/${field}/${stubId}`,
        );

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    search: async (query: string) => {
        const params = JSON.stringify({
            search: query,
        });
        return ApiService.get<{ docs: CompanyProfile[] }>(
            `${publicConfig.urls.api}/manufacturers/search?query=${params}&sort=relevance&page=0`,
        );
    },

    getLocalSignupUrl: (teamId: string) => {
        return LocalStorageService.get(`${teamId}.localCompanySignup`);
    },

    setLocalSignup: (teamId: string, companyId?: CompanyProfile['id'], step?: number) => {
        LocalStorageService.store(
            `${teamId}.localCompanySignup`,
            CompanyProfileHelpers.urls.create({ id: companyId, step }),
        );
    },

    finishLocalSignup: (teamId: string) => {
        LocalStorageService.clear(`${teamId}.localCompanySignup`);
    },

    addPartner: async (
        company: CompanyProfile,
        partnerId: string,
        options: Partial<CompanyProfile['partners'][number]> = {},
    ) => {
        const response = await CompanyProfileService.update(company.id, {
            partners: [...(company.partners ?? []), { company: partnerId, ...options }],
        });

        return response;
    },
};

export { CompanyProfileService };
