import { mutate } from 'swr';

import { FeatureLimit, PermissionTeam, Team, User } from 'models';

import { publicConfig } from '@public-config';

import { getFeatureLimitSWRKey } from 'hooks/use-check-feature-limit';

import { ApiService } from './ApiService';

export type FeatureCountAndLimit = { count: number; limit: number };
export type FeatureLimitCheckFunction<Return> = {
    (limit: Exclude<FeatureLimit, FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS>): Return;
    (limit: FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS, diagramId: string): Return;
};

class PermissionServiceSubscription {
    static canPurchaseSubscription = (user?: User | null, team?: Team | null) =>
        user &&
        team &&
        team.users.find(({ user: userId }) => user.id === userId)?.newPermissions.includes(PermissionTeam.OWNER);

    static checkFeatureLimit: FeatureLimitCheckFunction<Promise<FeatureCountAndLimit>> = (limit, diagramId?: string) =>
        ApiService.get(
            `${publicConfig.urls.api}/check-feature-limit?limit=${limit}${diagramId ? `&diagramId=${diagramId}` : ''}`,
        );

    static mutateFeatureLimit: FeatureLimitCheckFunction<Promise<void>> = async (
        limit: FeatureLimit,
        diagramId?: string,
    ) => {
        // need { revalidate: true } to trigger revalidation
        // even if the component is not present on page
        await mutate(getFeatureLimitSWRKey(limit, diagramId), undefined, {
            revalidate: true,
        });
    };
}

export { PermissionServiceSubscription };
