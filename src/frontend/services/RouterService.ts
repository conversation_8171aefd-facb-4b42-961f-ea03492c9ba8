import Router from 'next/router';

type RouterOptions = {
    shallow?: boolean;
    locale?: string | false;
    scroll?: boolean;
};

const RouterService = {
    push: async (url: string, as?: string, options?: RouterOptions) => {
        await Router.push(url, as, options);
    },

    replace: async (url: string, as?: string, options?: RouterOptions) => {
        await Router.replace(url, as, options);
    },

    refresh: async () => {
        await Router.replace(Router.asPath);
    },

    reload: () => {
        Router.reload();
    },

    back: () => {
        Router.back();
    },

    setQuery: async (name: string, value: string, action: 'replace' | 'push' = 'push') => {
        const search = new URLSearchParams(window.location.search);
        search.set(name, value);

        let url = window.location.pathname;

        if (search.toString()) {
            url += `?${search.toString()}`;
        }

        if (window.location.hash) {
            url += window.location.hash;
        }

        await RouterService[action](url, undefined, { shallow: true });
    },

    removeQuery: async (name: string, action: 'replace' | 'push' = 'replace') => {
        const search = new URLSearchParams(window.location.search);
        search.delete(name);

        let url = window.location.pathname;

        if (search.toString()) {
            url += `?${search.toString()}`;
        }

        if (window.location.hash) {
            url += window.location.hash;
        }

        await RouterService[action](url, undefined, { shallow: true });
    },
};

export { RouterService };
