/** biome-ignore-all lint/correctness/noUnusedVariables: this is a definition file? */
import { NotificationData } from '@mantine/notifications';

type NotificationAction = {
    tooltip?: string;
    icon?: React.ReactNode;
    label?: React.ReactNode;
    onClick: () => void;
};

type NotificationProps = NotificationData & {
    actions?: NotificationAction[];
};

type NotificationId = NotificationData['id'];
