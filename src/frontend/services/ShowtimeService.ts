import { QrScan, QrScanAISummary, QrScanPayload, QrScanSummaryType } from 'models';
import { publicConfig } from '@public-config';

import { AIServiceHelpers } from 'helpers/AIServiceHelpers';
import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';

import { ApiService } from 'services/ApiService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { mutate } from 'swr';

const ShowtimeService = {
    createQrScan: async (payload: Partial<QrScan>) => {
        InternalTrackingService.track('qrScan.create', {
            data: payload,
        });

        const result = await ApiService.post<{
            doc: QrScanPayload;
        }>(`${publicConfig.urls.api}/qrScans`, payload);

        await mutate(ShowtimeHelpers.swr.qrScans, (data: QrScanPayload[] | undefined) => {
            return [result.doc, ...(data || [])];
        });

        return result.doc;
    },

    editQrScan: async (id: string, payload: Partial<QrScan>) => {
        InternalTrackingService.track('qrScan.edit', {
            id,
            data: payload,
        });

        const result = await ApiService.patch<{
            doc: QrScanPayload;
        }>(`${publicConfig.urls.api}/qrScans/${id}`, payload);

        await mutate(ShowtimeHelpers.swr.qrScans);

        return result.doc;
    },

    deleteQrScan: async (id: string) => {
        InternalTrackingService.track('qrScan.delete', {
            id,
        });

        await ApiService.patch(`${publicConfig.urls.api}/qrScans/${id}`, {
            deletedAt: new Date().toISOString(),
        });

        await mutate(ShowtimeHelpers.swr.qrScans);
    },

    getQrScansByEmail: async (email: string) => {
        const result = await ApiService.get<{ docs: QrScanPayload[] }>(
            `${publicConfig.urls.api}/qrScans?where[userInfo.email][equals]=${encodeURIComponent(email)}&pagination=false`,
        );

        return result.docs;
    },

    getQrScansByUser: async (userId: string) => {
        const result = await ApiService.get<{ docs: QrScanPayload[] }>(
            `${publicConfig.urls.api}/qrScans?where[user][equals]=${userId}&pagination=false`,
        );

        return result.docs;
    },

    getQrScansByCompany: async ({ companyId, eventId }: { companyId: string; eventId?: string }) => {
        const result = await ApiService.get<{ docs: QrScanPayload[] }>(
            `${publicConfig.urls.api}/qrScans?where[company][equals]=${companyId}${eventId ? `&where[event][equals]=${eventId}` : ''}&pagination=false`,
        );

        return result.docs;
    },

    summarizeVisitorNotes: async ({ userId, email }: { userId?: string; email?: string }) => {
        InternalTrackingService.track('qrScan.summarizeVisitorNotes', {
            userId,
            email,
        });

        return AIServiceHelpers.handleStream(
            (params) =>
                ApiService.post(`${publicConfig.urls.api}/qrScans/summarizeVisitorNotes`, params, {
                    headers: { Accept: 'text/event-stream' },
                }),
            {
                userId,
                email,
            },
        );
    },

    summarizeQrScans: async ({ companyId, type }: { companyId: string; type: QrScanSummaryType }) => {
        InternalTrackingService.track('qrScan.summarizeQrScans', {
            companyId,
            type,
        });

        const result = await ApiService.post<QrScanAISummary>(`${publicConfig.urls.api}/qrScans/summarize`, {
            companyId,
            type,
        });

        return result;
    },
};

export { ShowtimeService };
