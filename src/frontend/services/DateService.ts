import DayJS from 'dayjs';

import IsSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import LocalizedFormat from 'dayjs/plugin/localizedFormat';
import RelativeTime from 'dayjs/plugin/relativeTime';

DayJS.extend(IsSameOrBefore);
DayJS.extend(LocalizedFormat);
DayJS.extend(RelativeTime);

const DateService = {
    format: (date: Date | string, formatString = 'L LT') => {
        try {
            return DayJS(date).format(formatString);
        } catch {
            return '';
        }
    },

    formatToDate: (date: Date | string) => {
        return DateService.format(date, 'L');
    },

    formatDistanceToNow: (date: Date | string, addSuffix: boolean = true) => {
        try {
            return DayJS(date).fromNow(!addSuffix);
        } catch {
            return '';
        }
    },

    interval(start: Date | null, end: Date | null, interval: 'day' = 'day') {
        const dates: Date[] = [];

        if (!start || !end) {
            return dates;
        }

        let current = DayJS(start);

        while (current.isSameOrBefore(DayJS(end).endOf(interval))) {
            dates.push(current.toDate());
            current = current.add(1, interval);
        }

        return dates;
    },

    formatNumberOfDays: (days: number) => {
        const years = Math.round(days / 365);
        const months = Math.round(days / 30);
        const weeks = Math.round(days / 7);

        if (years > 1.25) {
            return 'more than a year';
        }

        if (years > 1) {
            return '1 year';
        }

        if (months > 1) {
            return `${months} ${months > 1 ? 'months' : 'month'}`;
        }

        if (weeks > 1) {
            return `${weeks} ${weeks > 1 ? 'weeks' : 'week'}`;
        }

        return `${days} ${days > 1 ? 'days' : 'day'}`;
    },
};

export { DateService };
