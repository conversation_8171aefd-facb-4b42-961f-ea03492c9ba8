import { Geocoder } from '@mapbox/search-js-react';
import { publicConfig } from '@public-config';

type SuggestType = NonNullable<Parameters<typeof Geocoder>[0]['onSuggest']>;
type GeocodingResponse = Parameters<SuggestType>[0];
export type GeocodingFeature = GeocodingResponse['features'][number];

export type Geo = GeocodingFeature['properties'];

const GeoService = {
    geocode: async (query: string) => {
        const params = new URLSearchParams({
            access_token: publicConfig.mapboxPublicKey ?? '',
            q: query,
        });

        const response = await fetch(`https://api.mapbox.com/search/geocode/v6/forward?${params.toString()}`);
        const json = await response.json();

        return ((json.features || [])[0]?.properties || null) as Geo | null;
    },
};

export { GeoService };
