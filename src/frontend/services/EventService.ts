import { Event } from 'models';
import { publicConfig } from '@public-config';

import { mutate } from 'swr';

import { ApiService } from 'services/ApiService';

const EventService = {
    get: async (eventId: string) => {
        return await ApiService.get<Event>(`${publicConfig.urls.api}/events/${eventId}`);
    },

    list: async ({ depth = 0 }: { depth?: number }) => {
        const result = await ApiService.get<{
            docs: Event[];
        }>(`${publicConfig.urls.api}/events?[sort]=-createdBy&limit=9999&depth=${depth}`);

        return {
            ...result,
            events: result.docs,
        };
    },

    getActiveEvents: async () => {
        const result = await ApiService.get<{
            docs: Event[];
        }>(`${publicConfig.urls.api}/events?where[isActive][equals]=true&depth=0`);

        return {
            ...result,
            events: result.docs,
        };
    },

    update: async (eventId: string, payload: Partial<Event>) => {
        await ApiService.patch(`${publicConfig.urls.api}/events/${eventId}`, payload);
        await mutate('/api/events');
    },

    countProducts: async (eventId: string) => {
        return await ApiService.get<{
            total: number;
            counts: { manufacturer: string; productCount: number }[];
        }>(`${publicConfig.urls.api}/events/${eventId}/count-products`);
    },
};

export { EventService };
