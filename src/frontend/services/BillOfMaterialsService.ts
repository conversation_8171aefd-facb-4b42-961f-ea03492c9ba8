import { unique } from 'radash';

import { ApiService } from './ApiService';

import {
    BillOfMaterials,
    BillOfMaterialsComponent,
    BillOfMaterialsComponentInstance,
    BillOfMaterialsPayload,
    CompanyProfile,
    Order,
} from 'models';

import { getId } from 'helpers/getId';
import { NumberHelpers } from 'helpers/NumberHelpers';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { GroupService } from 'components/diagram/services/GroupService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { billOfMaterialsState, BillOfMaterialsView } from 'components/diagram/state/bill-of-materials';

import { BOMComponentsPerGroup } from 'components/diagram/hooks/use-bill-of-materials-grouped';

import { publicConfig } from '@public-config';

export type OrderComponent = Pick<NonNullable<Order['components']>[number], 'price' | 'quantity'> & {
    order: Order;
};

export type BomCSVData = {
    designator: string;
    group?: string;
    componentType?: string;
    quantity?: number;
    price?: string;
    partNumber?: string;
    manufacturer?: string;
    furnishedBy?: string;
    installedBy?: string;
    notes?: string;
    link?: string;
};

// always use depth=1 for BOM, to fetch all the products
const BillOfMaterialsService = {
    getByDesign: async (designId: string) => {
        return await ApiService.post(`${publicConfig.urls.api}/billOfMaterials/get-by-design/${designId}`);
    },

    update: async (billOfMaterialsId: string, data: Partial<BillOfMaterialsPayload>) => {
        return await ApiService.patch(`${publicConfig.urls.api}/billOfMaterials/${billOfMaterialsId}?depth=1`, data);
    },

    getOrderPrices: ({ orders, billOfMaterials }: { billOfMaterials: BillOfMaterials; orders: Order[] }) => {
        if (!billOfMaterials?.components) return null;

        let minPrice = 0;
        let maxPrice = 0;

        const orderComponents: { component: string; data: OrderComponent[] }[] = billOfMaterials.components.map(
            ({ component, quantity }) => {
                const orderPrices: number[] = [];

                const ordersData: OrderComponent[] = orders.map((order) => {
                    const findComponent = order.components?.find((cc) => getId(cc.component) === component.id);

                    orderPrices.push(findComponent?.price ?? 0);

                    return {
                        order,
                        price: findComponent?.price ?? 0,
                        quantity: findComponent?.quantity ?? 0,
                    };
                });

                minPrice += orderPrices.length ? quantity * Math.min(...orderPrices.filter(Boolean)) : 0; // filter to remove 0s
                maxPrice += orderPrices.length ? quantity * Math.max(...orderPrices.filter(Boolean)) : 0;

                return { component: component.id, data: ordersData };
            },
        );

        return {
            totalPrice: { min: NumberHelpers.getCleanNumber(minPrice), max: NumberHelpers.getCleanNumber(maxPrice) },
            orderComponents,
        };
    },

    generateCSVData: ({
        billOfMaterials,
        manufacturers,
    }: {
        billOfMaterials: BillOfMaterials;
        manufacturers: CompanyProfile[];
    }) => {
        const { components = [], componentInstances = [] } = billOfMaterials;
        const { componentInstances: diagramComponentInstances, groups } = DiagramSyncService.get();

        const data: BomCSVData[] = [];

        const groupedComponentInstances = groups.map((group) => {
            const groupComponentIntances = GroupService.getComponentInstancesInsideGroup(group.id);
            return {
                group,
                componentIntances: groupComponentIntances.map((componentInstance) => componentInstance.id),
            };
        });

        const getComponentInstanceGroups = (componentInstance: string) =>
            groupedComponentInstances
                .filter((group) => group.componentIntances.includes(componentInstance))
                .map((group) => group.group.label);

        components.forEach((component) => {
            const designators = component.componentInstances.map(
                (id) => diagramComponentInstances[id]?.designator ?? id,
            );
            const groups = component.componentInstances.map((instanceId) => getComponentInstanceGroups(instanceId));

            const csvData = BillOfMaterialsService.createComponentCSVData({
                component,
                designator: designators.join(', '),
                group: unique(groups).join(', '),
                manufacturers,
            });

            if (csvData) {
                data.push(csvData);
            }
        });

        componentInstances.forEach((instance) => {
            const diagramComponentInstance = diagramComponentInstances[instance.componentInstance];
            if (!diagramComponentInstance) return;

            const csvData = BillOfMaterialsService.createComponentInstanceCSVData({
                instance,
                designator: diagramComponentInstance.designator,
                group: getComponentInstanceGroups(instance.componentInstance).join(', '),
                componentType: diagramComponentInstance.componentType,
                partNumber: diagramComponentInstance.partNumber,
                manufacturer: diagramComponentInstance.manufacturer,
            });

            if (csvData) {
                data.push(csvData);
            }
        });

        return data;
    },

    generateCSVDataByGroup: ({
        componentsPerGroup,
        manufacturers,
        bomGroups = {},
    }: {
        manufacturers: CompanyProfile[];
        componentsPerGroup: BOMComponentsPerGroup;
        bomGroups?: BillOfMaterials['groups'];
    }) => {
        const { componentInstances: diagramComponentInstances } = DiagramSyncService.get();
        const data: BomCSVData[] = [];

        componentsPerGroup.forEach(({ group, components, componentInstances }) => {
            const groupLabel = group.label || '[unnamed group]';

            const bomGroup = Object.entries(bomGroups).find(([id]) => id === group.id)?.[1];

            if (bomGroup?.showGroupFields) {
                data.push({
                    group: groupLabel,
                    designator: 'GROUP',
                    quantity: bomGroup.quantity,
                    price: bomGroup.price ? NumberHelpers.formatPrice(bomGroup.price) : '',
                    furnishedBy: bomGroup.furnishedBy ?? '',
                    installedBy: bomGroup.installedBy ?? '',
                    notes: bomGroup.notes ?? '',
                    link: bomGroup.link ?? '',
                });
            }

            components.forEach((component) => {
                const designator = component.componentInstances
                    .map((id) => diagramComponentInstances[id]?.designator)
                    .filter(Boolean)
                    .join(', ');

                const csvData = BillOfMaterialsService.createComponentCSVData({
                    component,
                    designator,
                    group: groupLabel,
                    manufacturers,
                    componentType: component.component.type,
                    partNumber: component.component.productIdentifier,
                });

                if (csvData) {
                    data.push(csvData);
                }
            });

            componentInstances.forEach((instance) => {
                const diagramComponentInstance = diagramComponentInstances[instance.componentInstance];
                if (!diagramComponentInstance) return;

                const csvData = BillOfMaterialsService.createComponentInstanceCSVData({
                    instance,
                    designator: diagramComponentInstance.designator,
                    group: groupLabel,
                    componentType: diagramComponentInstance.componentType,
                    partNumber: diagramComponentInstance.partNumber,
                    manufacturer: diagramComponentInstance.manufacturer,
                });

                if (csvData) {
                    data.push(csvData);
                }
            });

            if (components.length || componentInstances.length) {
                data.push({
                    designator: '',
                });
            }
        });

        return data;
    },

    createComponentCSVData: ({
        component,
        designator,
        group,
        manufacturers,
        componentType,
        partNumber,
        manufacturerId,
    }: {
        component: BillOfMaterialsComponent;
        designator: string;
        group: string;
        manufacturers: CompanyProfile[];
        componentType?: string | null;
        partNumber?: string | null;
        manufacturerId?: string | null;
    }): BomCSVData | null => {
        const manufacturer = manufacturers.find(
            (m) => m.id === getId(manufacturerId || component.component.manufacturer),
        );

        return {
            designator,
            group,
            componentType: componentType || component.component.type,
            quantity: component.quantity,
            price: component.price ? NumberHelpers.formatPrice(component.price) : '',
            partNumber: partNumber || component.component.productIdentifier,
            manufacturer: manufacturer?.name,
            installedBy: component.installedBy ?? '',
            furnishedBy: component.furnishedBy ?? '',
            notes: component.notes ?? '',
            link: component.link ?? `${window.location.origin}/${ComponentHelpers.urls.view(component.component.id)}`,
            ...(component.customFields || {}),
        };
    },

    createComponentInstanceCSVData: ({
        instance,
        designator,
        group,
        componentType,
        partNumber,
        manufacturer,
    }: {
        instance: BillOfMaterialsComponentInstance;
        designator: string;
        group: string;
        componentType?: string | null;
        partNumber?: string | null;
        manufacturer?: string | null;
    }): BomCSVData | null => {
        return {
            designator,
            group,
            componentType: componentType ?? '',
            quantity: instance.quantity,
            price: instance.price ? NumberHelpers.formatPrice(instance.price) : '',
            partNumber: partNumber ?? '',
            manufacturer: manufacturer ?? '',
            installedBy: instance.installedBy ?? '',
            furnishedBy: instance.furnishedBy ?? '',
            notes: instance.notes ?? '',
            link:
                instance.link ??
                (instance.id ? `${window.location.origin}/${ComponentHelpers.urls.view(instance.id)}` : ''),
            ...(instance.customFields || {}),
        };
    },

    setShowView: (view: BillOfMaterialsView) => {
        billOfMaterialsState.currentView = view;
    },

    setShowAddFieldModal: (showAddFieldModal: boolean) => {
        billOfMaterialsState.showAddFieldModal = showAddFieldModal;
    },

    setShowOpenAllGroups: (showOpenAllGroups: boolean) => {
        billOfMaterialsState.openAllGroups = showOpenAllGroups;
    },
};

export { BillOfMaterialsService };
