import { MeasurementSystem } from 'models';

import { state } from 'state/default-measurement-system';

const MeasurementSystemService = {
    toggle: () => {
        state.measurementSystem = {
            [MeasurementSystem.METRIC]: MeasurementSystem.IMPERIAL,
            [MeasurementSystem.IMPERIAL]: MeasurementSystem.METRIC,
        }[state.measurementSystem || MeasurementSystem.IMPERIAL];
    },

    set: (measurementSystem: MeasurementSystem) => {
        state.measurementSystem = measurementSystem;
    },
};

export { MeasurementSystemService };
