import { debounce, isEqual } from 'radash';

import { ApiService } from './ApiService';

import { publicConfig } from '@public-config';

import {
    DEFAULT_DESIGN_LIBRARY_SEARCH,
    designLibrarySearch,
} from 'components/design-library/state/design-library-search';

import { DesignLibraryHelpers } from 'helpers/DesignLibraryHelpers';
import { ServiceHelpers } from 'helpers/ServiceHelpers';

import { DesignLibrarySearch } from 'components/design-library/types';
import { ReferenceDesignQuery, removeUndefined } from 'models';

const DesignLibraryService = {
    navigate: ServiceHelpers.createNavigator(DesignLibraryHelpers.urls),

    controller: null as AbortController | null,

    debouncedSearch: debounce({ delay: 500 }, () => {
        DesignLibraryService.search().then();
    }),

    getTags: () => {
        return ApiService.get(`${publicConfig.urls.api}/projects/marketplace-tags`);
    },

    getManufacturers: () => {
        return ApiService.get(`${publicConfig.urls.api}/projects/marketplace-manufacturers`);
    },

    search: async () => {
        const { page, filters } = designLibrarySearch;

        // cancel previous search calls
        if (designLibrarySearch.isLoading) {
            DesignLibraryService.controller?.abort();
        }

        designLibrarySearch.isLoading = true;

        try {
            const controller = new AbortController();
            const { signal } = controller;
            DesignLibraryService.controller = controller;

            const searchParams = new URLSearchParams({
                page: page.toString(),
                filters: JSON.stringify(filters),
            });

            const result = await ApiService.get(
                `${publicConfig.urls.api}/projects/marketplace?${searchParams.toString()}`,
                {
                    signal,
                },
            );

            designLibrarySearch.docs = result.docs;
            designLibrarySearch.totalPages = result.totalPages;
            if (!DesignLibraryService.isInitialSearch(filters)) {
                designLibrarySearch.totalDesigns = result.totalDocs;
            }
            designLibrarySearch.isLoading = false;

            return result;
        } catch (error: any) {
            if (error?.name === 'AbortError') {
                return;
            }

            designLibrarySearch.docs = [];
            designLibrarySearch.error = true;
            designLibrarySearch.isLoading = false;
        }
    },

    getByComponent: async (componentId: string, includeUnpublished?: boolean) => {
        const searchParams = new URLSearchParams({
            page: '1',
            limit: '99',
            filters: JSON.stringify({ component: componentId, includeUnpublished }),
        });

        return await ApiService.get(`${publicConfig.urls.api}/projects/marketplace?${searchParams.toString()}`);
    },

    getByProfile: async (profileId: string, includeUnpublished?: boolean) => {
        const searchParams = new URLSearchParams({
            page: '1',
            limit: '99',
            filters: JSON.stringify({ manufacturer: profileId, includeUnpublished }),
        });

        return await ApiService.get(`${publicConfig.urls.api}/projects/marketplace?${searchParams.toString()}`);
    },

    searchByName: async (search: string) => {
        const searchParams = new URLSearchParams({
            page: '1',
            limit: '99',
            filters: JSON.stringify({ search }),
        });

        return await ApiService.get(`${publicConfig.urls.api}/projects/marketplace?${searchParams.toString()}`);
    },

    getMultiple: async (ids: string[]) => {
        const searchParams = new URLSearchParams({
            page: '1',
            limit: '99',
            filters: JSON.stringify({ ids }),
        });

        return await ApiService.get(`${publicConfig.urls.api}/projects/marketplace?${searchParams.toString()}`);
    },

    setFilter: <FilterKey extends keyof DesignLibrarySearch['filters']>(
        key: FilterKey,
        value: DesignLibrarySearch['filters'][FilterKey],
    ) => {
        designLibrarySearch.filters[key] = value;
    },

    mergeFilters(filters: ReferenceDesignQuery) {
        Object.entries(filters).forEach(([key, value]) => {
            DesignLibraryService.setFilter(key as keyof ReferenceDesignQuery, value);
        });
    },

    resetFilters: () => {
        DesignLibraryService.mergeFilters(DEFAULT_DESIGN_LIBRARY_SEARCH.filters);
    },

    toggleInlineFilter: <FilterKey extends keyof DesignLibrarySearch['showFilters']>(
        key: FilterKey,
        value: boolean,
    ) => {
        designLibrarySearch.showFilters[key] = value;
    },

    setPage: (page: number) => {
        designLibrarySearch.page = page;
    },

    setSearchBarQuery: (query: string) => {
        designLibrarySearch.searchBarQuery = query;
    },

    clearSearchBarQuery: () => {
        designLibrarySearch.searchBarQuery = undefined;
    },

    isInitialSearch: (filters: DesignLibrarySearch['filters']) => {
        return isEqual(removeUndefined(filters), removeUndefined(DEFAULT_DESIGN_LIBRARY_SEARCH.filters));
    },
};

export { DesignLibraryService };
