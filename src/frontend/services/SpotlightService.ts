import { CompanyProfile, Component, Project } from 'models';

import { openSpotlight } from '@mantine/spotlight';

import { ApiService } from 'services/ApiService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { publicConfig } from '@public-config';

const SpotlightService = {
    search: async (query: string) => {
        return ApiService.get<{
            companies: CompanyProfile[];
            components: Component[];
            projects: Project[];
        }>(`${publicConfig.urls.api}/spotlight?search=${query}`);
    },

    open: () => {
        InternalTrackingService.track('spotlight.open');

        openSpotlight();
    },
};

export { SpotlightService };
