import Router from 'next/router';

import { uid } from 'radash';
import { snapshot } from 'valtio';

import { LocalStorageService } from 'services/LocalStorageService';
import { SessionStorageService } from 'services/SessionStorageService';

import { CompressionHelpers } from 'helpers/CompressionHelpers';
import { UserHelpers } from 'helpers/UserHelpers';

import { state as currentUser } from 'state/current-user';
import { state as currentTeam } from 'state/current-team';
import { state as currentProject } from 'state/current-project';
import { geoState } from 'state/geo';

const InternalTrackingService = {
    started: false,
    disabled: false,
    sessionId: null as string | null,
    queue: [] as any[],

    track: (name: string, data: object = {}) => {
        if (typeof window === 'undefined' || InternalTrackingService.disabled) {
            return;
        }

        InternalTrackingService.startSession();

        const now = new Date();
        const event = {
            name,
            data,
            metadata: InternalTrackingService.metadata(),
            timestamp: now.getTime(),
            date: now.toISOString(),
        };

        if (LocalStorageService.get('internalTrackingServiceLogging') === 'debug') {
            console.log('Tracking Event', event);
        }

        const nextBlobSize = CompressionHelpers.calculateStringSize(
            JSON.stringify([...InternalTrackingService.queue, event]),
        );

        // There's a safe limit of 64 Kb for sendBeacon request.
        // If we cross that limit, just push the current events first
        if (nextBlobSize > 48_000) {
            InternalTrackingService.push();
        }

        InternalTrackingService.queue.push(event);
    },

    metadata: () => {
        const { pathname, asPath, query } = Router;

        const referrer = LocalStorageService.get(UserHelpers.localStorageKey.referrer);
        const email = LocalStorageService.get(UserHelpers.localStorageKey.email);

        return {
            title: document.title,
            path: asPath,
            pathname,
            query,
            search: window.location.hash,
            session: InternalTrackingService.sessionId,
            user: currentUser.user?.id || null,
            goofy: currentUser.user?.goofy || null,
            internal: currentUser.user?.internal || false,
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            team: currentTeam.team?.id || null,
            project: currentProject.project?.id || null,
            // component: currentComponentState.component?.id || null,
            geo: snapshot(geoState).geo,
            localStorage: {
                referrer,
                email,
            },
        };
    },

    startSession: () => {
        if (InternalTrackingService.sessionId) {
            return;
        }

        let id = SessionStorageService.get('sessionId');

        if (!id) {
            id = uid(12);
            SessionStorageService.store('sessionId', id);
        }

        InternalTrackingService.sessionId = id;
    },

    start: () => {
        if (InternalTrackingService.started) {
            return;
        }

        window.setInterval(InternalTrackingService.push, 10_000);

        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                InternalTrackingService.push();
            }
        });

        window.addEventListener('beforeunload', () => {
            InternalTrackingService.push();
        });

        window.addEventListener('unload', () => {
            InternalTrackingService.push();
        });

        InternalTrackingService.started = true;
    },

    push: () => {
        if (InternalTrackingService.queue.length) {
            const events = JSON.parse(JSON.stringify(InternalTrackingService.queue));
            InternalTrackingService.queue = [];

            if (!navigator.sendBeacon) {
                console.warn('sendBeacon not supported');

                return;
            }

            const blob = new Blob([JSON.stringify({ events })], {
                type: 'application/json; charset=UTF-8',
            });

            const success = navigator.sendBeacon('/api/secret', blob);

            if (!success) {
                // Possible reasons a sendBeacon call can fail:
                // 1. Spamming the API
                // 2. To big of a payload (we tackle this)
                // 3. The total size of all beacon requests in the queue is too big

                let retryCount = 1;
                let retrySuccess = false;

                const retry = () => {
                    retrySuccess = navigator.sendBeacon('/api/secret', blob);
                    retryCount++;

                    if (retrySuccess) {
                        return;
                    }

                    if (retryCount === 5) {
                        console.error('Retry Tracking Beacon Error');
                        console.log(blob.size, events.length, events);
                    } else {
                        const timeout = [1000, 4000, 10000, 15000][retryCount - 1];

                        setTimeout(retry, timeout);
                    }
                };

                setTimeout(retry, 1000);
            }
        }
    },

    skip: async (action: () => Promise<void>) => {
        InternalTrackingService.disabled = false;
        await action();
        InternalTrackingService.disabled = true;
    },
};

export { InternalTrackingService };
