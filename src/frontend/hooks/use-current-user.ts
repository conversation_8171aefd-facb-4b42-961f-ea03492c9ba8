import { useSnapshot } from 'hooks/use-safe-snapshot';

import { CompanyService, User, UserType } from 'models';

import { state } from 'state/current-user';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

const useCurrentUser = (): (User & { isLoading?: boolean }) | null => {
    const { companies, isLoading } = useCurrentTeamCompanies();

    const { user } = useSnapshot(state);

    return user
        ? {
              ...user,
              hasCompany: companies.length > 0,
              isManufacturer: user.type === UserType.MANUFACTURER || companies.length > 0,
              //   isSupportUser: !!inAppSupportCompanies.find(({ users }) => users.includes(user.id)),
              isSupportUser: !!companies.length, // TODO: for now let's show support center for all company users
              isLoading,
          }
        : null;
};

export { useCurrentUser };
