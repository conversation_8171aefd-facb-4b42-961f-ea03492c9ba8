import { useEffect } from 'react';

import { activeScrollItemState, useActiveScrollItem } from 'components/scroll-nav/hooks';

const useScrollNav = ({ topSpacing = 0 }: { topSpacing?: number }) => {
    const activeItem = useActiveScrollItem();
    const setActiveItem = (key: string | null) => {
        activeScrollItemState.activeScrollItem = key;
    };

    useEffect(() => {
        let firstScrollElement: HTMLElement;

        const checkIfScrolledToTop = () => {
            if (!activeItem) return;

            if (!firstScrollElement) {
                firstScrollElement = document.querySelector(`[data-scroll-id]`) as HTMLElement;
            }

            const firstYPos = firstScrollElement?.getBoundingClientRect().top;

            // add some extra padding
            if (firstYPos > topSpacing + 40) {
                setActiveItem(null);
            }
        };

        window.addEventListener('scroll', checkIfScrolledToTop);

        return () => {
            window.removeEventListener('scroll', checkIfScrolledToTop);
        };
    }, [activeItem, topSpacing, setActiveItem]);
};

export { useScrollNav };
