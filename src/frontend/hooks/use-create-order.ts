import { OrderService } from 'services/OrderService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { ApiService } from 'services/ApiService';

import { useRouter } from 'next/router';
import { useCallback } from 'react';
import { publicConfig } from '@public-config';

const useCreateOrder = (props?: { switchAfterCreate?: boolean }) => {
    const router = useRouter();

    return useCallback(
        async ({
            name,
            components,
            design,
        }: {
            name: string;
            components?: { component: string; quantity: number }[];
            design?: string;
        }) => {
            const response = await OrderService.create({
                name,
                components: components ?? [],
                design,
            });

            if (response.errors) {
                LocalNotificationService.showError({
                    message: 'Error creating order',
                });
            }

            if (response.doc && props?.switchAfterCreate) {
                await ApiService.post(`${publicConfig.urls.api}/users/switch-order`, { order: response.doc.id });
            }

            return response.doc;
        },
        [router],
    );
};

export { useCreateOrder };
