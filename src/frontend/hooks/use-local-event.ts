import { Event } from 'models';

import { useLocalStorage } from '@mantine/hooks';
import { useActiveEvents } from 'hooks/use-active-events';

import { UserHelpers } from 'helpers/UserHelpers';

const useLocalEvent = () => {
    const { events } = useActiveEvents();

    const [localEventId, setLocalEvent] = useLocalStorage<Event['id'] | null>({
        key: UserHelpers.localStorageKey.event,
        defaultValue: null,
    });

    const localEvent =
        events.find((event) => {
            return event.id === localEventId;
        }) || null;

    return {
        localEvent,
        setLocalEvent,
    };
};

export { useLocalEvent };
