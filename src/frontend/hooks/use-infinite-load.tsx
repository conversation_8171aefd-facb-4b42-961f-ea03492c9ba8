import { useCallback, useEffect, type JSX } from 'react';

import { Box } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';

import { SWRResponse } from 'swr';
import useSWRInfinite from 'swr/infinite';

export const PAGE_SIZE = 16;

export type UseInfiniteLoad = {
    data: any;
    isLoading: boolean;
    isLoadingMore?: boolean;
    ScrollRef: JSX.Element;
    findPageIndex: (index: number) => number;
} & SWRResponse;

const useInfiniteLoad = ({
    getSearchUrl,
    fetcher,
    pageSize = PAGE_SIZE,
}: {
    getSearchUrl: (page: number) => string;
    fetcher: (url: string) => Promise<any>;
    pageSize?: number;
}): UseInfiniteLoad => {
    const { data, size, setSize, isLoading, ...rest } = useSWRInfinite(
        (index) => getSearchUrl(index + 1),
        async (url) => fetcher(url),
    );

    const loadedItems = data ? [].concat(...data) : [];

    const isLoadingMore = isLoading || (size > 0 && data && typeof data[size - 1] === 'undefined');
    const isEmpty = data?.[0]?.length === 0;
    const isReachingEnd = isEmpty || (data && data[data.length - 1]?.length < pageSize);

    const loadMore = useCallback(() => {
        setSize(size + 1);
    }, [size, setSize]);

    const findPageIndex = useCallback((index: number) => Math.floor(index / pageSize), [pageSize]);

    // scroll ref

    const { ref: scrollHintRef, entry } = useIntersection({
        threshold: 1,
    });

    useEffect(() => {
        if (entry?.isIntersecting) {
            loadMore();
        }
    }, [entry?.isIntersecting]);

    const ScrollRef = <>{!isLoadingMore && !isReachingEnd && <Box ref={scrollHintRef} style={{ height: 100 }} />}</>;

    return {
        data: loadedItems,
        isLoading,
        isLoadingMore,
        ScrollRef,
        findPageIndex,
        ...rest,
    };
};

export { useInfiniteLoad };
