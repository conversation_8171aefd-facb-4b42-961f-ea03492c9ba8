import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { Article } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';
import { ArticleService } from 'services/ArticleService';

const useArticlesByIds = (
    ids?: string[],
): SWRResponse & {
    articles: Article[];
} => {
    const condition = !!ids && !!ids.length;

    const key = `/articles/${ids?.join(',')}`;
    const fetcher = condition ? () => ArticleService.getMultiple(ids) : null;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key,
            fetcher,
            condition,
        }),
    );

    return {
        ...swr,
        articles: swr?.data?.docs || [],
    };
};

export { useArticlesByIds };
