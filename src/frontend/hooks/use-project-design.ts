import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { useRouter } from 'next/router';

import { ProjectDesignService } from 'services/ProjectDesignService';

import { ProjectDesign } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

type UseProjectDesign = (
    useProjectDesignArguments?:
        | null
        | ProjectDesign['id']
        | {
              id: ProjectDesign['id'];
          },
    depth?: number,
) => SWRResponse & {
    projectDesign: ProjectDesign | null;
};

const useProjectDesign: UseProjectDesign = (useProjectDesignArguments = null, depth = 0) => {
    const { query } = useRouter();

    let projectDesignId = query.designId as string | undefined;

    if (useProjectDesignArguments && typeof useProjectDesignArguments === 'string') {
        projectDesignId = useProjectDesignArguments;
    }

    if (useProjectDesignArguments && typeof useProjectDesignArguments === 'object') {
        projectDesignId = useProjectDesignArguments.id;
    }

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/api/projectDesigns/${projectDesignId}?depth=${depth}`,
            fetcher: () => ProjectDesignService.get(projectDesignId!, depth),
            condition: Boolean(projectDesignId),
        }),
    );

    return {
        ...swr,
        projectDesign: swr.data || null,
    };
};

export { useProjectDesign };
