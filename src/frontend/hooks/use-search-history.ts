import { unique } from 'radash';

import { useLocalStorage } from '@mantine/hooks';

const useSearchHistory = () => {
    const [searchHistory, setSearchHistory] = useLocalStorage<string[]>({
        key: 'searchHistory',
        defaultValue: [],
    });

    const addSearchHistory = (query: string) => {
        setSearchHistory((prev) => {
            return unique([query, ...prev]).slice(0, 3);
        });
    };

    return {
        searchHistory,
        addSearchHistory,
    };
};

export { useSearchHistory };
