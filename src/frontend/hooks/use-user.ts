import type { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { UserService } from 'services/UserService';

import { User } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

type UseUser = (
    useUserArguments:
        | User['id']
        | {
              id: User['id'];
          }
        | undefined,
    fetchNew?: boolean,
) => SWRResponse & {
    user: User | null;
};

const useUser: UseUser = (useUserArguments, fetchNew = true) => {
    const userId = typeof useUserArguments === 'object' ? useUserArguments?.id : useUserArguments;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/api/users/${userId}`,
            fetcher: () => UserService.get(userId!),
            condition: Boolean(userId) && fetchNew,
        }),
        !fetchNew && typeof useUserArguments === 'object' ? { fallbackData: useUserArguments as User } : {},
    );

    return {
        ...swr,
        user: swr.data as User | null,
    };
};

export { useUser };
