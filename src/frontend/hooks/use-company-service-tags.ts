import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { CompanyProfileService } from 'services/CompanyProfileService';

const useCompanyServiceTags = (): SWRResponse & {
    serviceTags: string[];
} => {
    const swr = useSWRImmutable<{
        serviceTags: string[];
    }>(`/api/manufacturers/service-tags`, () => {
        return CompanyProfileService.getServiceTags();
    });

    return {
        ...swr,
        serviceTags: swr?.data?.serviceTags || [],
    };
};

export { useCompanyServiceTags };
