import useSWR, { SWRResponse } from 'swr';

import { ManufacturerProfile } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';
import { CompanyProfileService } from 'services/CompanyProfileService';

const useProfilesByIds = (
    ids?: string[],
): SWRResponse & {
    profiles: ManufacturerProfile[];
} => {
    const key = `/profiles/${ids?.join(',')}`;
    const fetcher = () => (ids ? CompanyProfileService.getMultiple(ids) : null);

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key,
            fetcher,
            condition: !!ids?.length,
        }),
        {
            revalidateOnFocus: false,
            keepPreviousData: true,
        },
    );

    const sortedDocs = (swr?.data?.docs ?? []).sort((a: ManufacturerProfile, b: ManufacturerProfile) => {
        const aIndex = ids?.indexOf(a.id) ?? -1;
        const bIndex = ids?.indexOf(b.id) ?? -1;

        if (aIndex === -1) return 1;
        if (bIndex === -1) return -1;

        return aIndex - bIndex;
    });

    return {
        ...swr,
        profiles: sortedDocs,
    };
};

export { useProfilesByIds };
