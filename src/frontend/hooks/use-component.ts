import useSWR, { SWRResponse } from 'swr';

import { Component } from 'models';

import { ComponentService } from 'services/ComponentService';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

const useComponent = (
    componentId: Component['id'] | null,
    options: {
        depth?: number;
        skipAccessCheck?: boolean;
    } = {},
): SWRResponse & {
    component: Component;
} => {
    const { depth = 1, skipAccessCheck = false } = options;

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: `/components/${componentId}?depth=${depth}&skipAccessCheck=${skipAccessCheck}`,
            fetcher: () => ComponentService.get(componentId!, { depth, skipAccessCheck }),
            condition: Boolean(componentId),
        }),
        { revalidateOnFocus: false },
    );

    return {
        ...swr,
        component: swr?.data,
    };
};

export { useComponent };
