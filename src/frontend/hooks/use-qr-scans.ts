import useSWRImmutable from 'swr/immutable';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { ShowtimeService } from 'services/ShowtimeService';
import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';

const useQrScans = ({ email, userId }: { email?: string; userId?: string }) => {
    const swrUser = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: ShowtimeHelpers.swr.qrScans,
            fetcher: () => ShowtimeService.getQrScansByUser(userId!),
            condition: <PERSON><PERSON><PERSON>(userId),
        }),
    );

    const swrEmail = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: ShowtimeHelpers.swr.qrScans,
            fetcher: () => ShowtimeService.getQrScansByEmail(email!),
            condition: Boolean(email) && !userId,
        }),
    );

    return {
        qrScans: swrUser?.data || swrEmail?.data || [],
        isLoading: swrUser?.isLoading || swrEmail?.isLoading,
        error: swrUser?.error || swrEmail?.error,
    };
};

export { useQrScans };
