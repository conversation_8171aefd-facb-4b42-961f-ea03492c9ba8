import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Project } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';

type UseReferenceDesigns = (
    profileId: string,
    includeUnpublished?: boolean,
) => SWRResponse & {
    key: string;
    projects: Project[];
};

const useReferenceDesignsByProfile: UseReferenceDesigns = (profileId, includeUnpublished = false) => {
    const fetcher = async () => {
        return DesignLibraryService.getByProfile(profileId, includeUnpublished);
    };

    const key = `/projects/marketplace?profileId=${profileId}&includeUnpublished=${includeUnpublished}`;
    const swr = useSWRImmutable(key, fetcher, { revalidateOnMount: true });

    return {
        ...swr,
        key,
        projects: swr?.data?.docs || [],
    };
};

export { useReferenceDesignsByProfile };
