import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { unique } from 'radash';
import { APPLICATION_SUGGESTIONS } from 'data/application-suggestions';

const useCompanyApplicationTags = (): SWRResponse & {
    applicationTags: string[];
} => {
    const swr = useSWRImmutable<{
        applicationTags: string[];
    }>(`/api/manufacturers/application-tags`, () => {
        return CompanyProfileService.getApplicationTags();
    });

    return {
        ...swr,
        applicationTags: unique([...APPLICATION_SUGGESTIONS, ...(swr?.data?.applicationTags || [])]),
    };
};

export { useCompanyApplicationTags };
