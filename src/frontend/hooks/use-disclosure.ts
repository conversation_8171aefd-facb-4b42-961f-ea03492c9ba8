import { useRef, useState } from 'react';

export const useDisclosure = (initialState: boolean, timeout = 100) => {
    const [opened, setOpened] = useState(initialState);

    const ref = useRef<NodeJS.Timeout | undefined>(undefined);

    const open = () => {
        setOpened(true);
        clearTimeout(ref.current);
    };

    const close = () => {
        ref.current = setTimeout(() => {
            setOpened(false);
        }, timeout);
    };

    return [opened, { open, close }] as const;
};
