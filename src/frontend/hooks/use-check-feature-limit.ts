import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { FeatureLimit } from 'models';

import {
    FeatureLimitCheckFunction,
    FeatureCountAndLimit,
    PermissionServiceSubscription,
} from 'services/PermissionServiceSubscription';
import { useCurrentUser } from 'hooks/use-current-user';

type FeatureLimitStatus = FeatureCountAndLimit & { pass: boolean };
type CheckFeatureSWRResponse = Omit<SWRResponse<FeatureCountAndLimit>, 'data'> & {
    status: FeatureLimitStatus | null;
};

const getFeatureLimitSWRKey = (limit: FeatureLimit, diagramId?: string) =>
    `/check-feature-limit?limit=${limit}${diagramId ? `&diagramId=${diagramId}` : ''}`;

const useCheckFeatureLimit: FeatureLimitCheckFunction<CheckFeatureSWRResponse> = (limit, diagramId?: string) => {
    const user = useCurrentUser();
    const key = user ? getFeatureLimitSWRKey(limit, diagramId) : null;
    const fetcher = () => {
        switch (limit) {
            case FeatureLimit.PROJECTS:
            case FeatureLimit.SIMULATIONS:
            case FeatureLimit.AI_REQUESTS:
                return PermissionServiceSubscription.checkFeatureLimit(limit);
            case FeatureLimit.DESIGN_FILE_UPLOADS:
            case FeatureLimit.IMAGES:
                if (!diagramId) {
                    return {
                        count: 0,
                        limit: 9999,
                    };
                }

                return PermissionServiceSubscription.checkFeatureLimit(limit, diagramId!);
        }
    };

    const { data, ...swr } = useSWRImmutable(key, fetcher);

    return { ...swr, status: data ? { ...data, pass: data.count < data.limit } : null };
};

export { useCheckFeatureLimit, getFeatureLimitSWRKey };
