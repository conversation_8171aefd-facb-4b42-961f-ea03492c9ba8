import { CompanyService, CompanyProfile, ManufacturerProfile, Team } from 'models';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { state as currentUserState } from 'state/current-user';
import { currentTeamCompaniesState } from 'state/current-team-companies';

const filter = (companies: CompanyProfile[], service: CompanyService) => {
    return companies.filter((company) => company.services.includes(service));
};

const useTeamCompanies = (
    teamId: Team['id'] | undefined,
): SWRResponse & {
    companies: ManufacturerProfile[];
    inAppSupportCompanies: ManufacturerProfile[];
    manufacturers: ManufacturerProfile[];
    distrbutors: ManufacturerProfile[];
} => {
    const swr = useSWRImmutable<{ docs: ManufacturerProfile[] }>({
        key: teamId ? `/teams/${teamId}/manufacturers` : null,
        fetcher: () => CompanyProfileService.getByTeam(teamId ?? ''),
    });

    const companies = swr?.data?.docs || [];

    return {
        ...swr,
        companies,
        inAppSupportCompanies: filter(companies, CompanyService.IN_APP_SUPPORT),
        manufacturers: filter(companies, CompanyService.MANUFACTURING),
        distrbutors: filter(companies, CompanyService.DISTRIBUTION),
    };
};

const useCurrentTeamCompanies = () => {
    // This is calling the state directly because useCurrentUser also uses this hook
    // We don't want to create an infinite loop.
    const { user } = currentUserState;
    const { companies = [] } = useSnapshot(currentTeamCompaniesState);

    const mutate = async () => {
        if (user) {
            const { docs: mutatedCompanies } = await CompanyProfileService.getByTeam(user.team);

            currentTeamCompaniesState.companies = mutatedCompanies;
        }
    };

    return {
        isLoading: false,
        mutate,
        companies,
        inAppSupportCompanies: filter(companies, CompanyService.IN_APP_SUPPORT),
        manufacturers: filter(companies, CompanyService.MANUFACTURING),
        distrbutors: filter(companies, CompanyService.DISTRIBUTION),
    };
};

export { useTeamCompanies, useCurrentTeamCompanies };
