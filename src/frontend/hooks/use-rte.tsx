import { FC, useState } from 'react';

import { <PERSON><PERSON>utton, InputWrapper, InputWrapperProps, Loader, Tooltip } from '@mantine/core';

import { Editor, JSONContent, useEditor } from '@tiptap/react';
import { Link, RichTextEditor } from '@mantine/tiptap';
import Placeholder from '@tiptap/extension-placeholder';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

import { BsListOl, BsListUl, BsTypeBold, BsTypeItalic } from 'react-icons/bs';
import { IoAttachOutline, IoLinkSharp, IoUnlinkSharp } from 'react-icons/io5';

import { LocalFile } from 'models';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';

import { FilesPreview } from 'components/tiptap/TipTapComposer.FilesPreview';

import cx from './RichTextEditor.module.scss';
import { LocalNotificationService } from 'services/LocalNotificationService';

const RTEField: FC<
    {
        editor: Editor;
        fileGroup?: string;
        files?: LocalFile[];
        addFile?: (file: string) => void;
        deleteFile?: (file: string) => void;
        mode?: 'full' | 'minimal';
    } & InputWrapperProps
> = ({ editor, fileGroup, files = [], addFile, deleteFile, mode = 'full', ...props }) => {
    return (
        <InputWrapper {...props}>
            <RichTextEditor editor={editor} variant="subtle" classNames={cx}>
                <RichTextEditor.Toolbar>
                    <RichTextEditor.ControlsGroup>
                        <RichTextEditor.Bold icon={() => <BsTypeBold />} />
                        <RichTextEditor.Italic icon={() => <BsTypeItalic />} />
                    </RichTextEditor.ControlsGroup>

                    {mode === 'full' && (
                        <RichTextEditor.ControlsGroup>
                            <RichTextEditor.Link icon={() => <IoLinkSharp />} />
                            <RichTextEditor.Unlink icon={() => <IoUnlinkSharp />} />
                        </RichTextEditor.ControlsGroup>
                    )}

                    {mode === 'full' && (
                        <RichTextEditor.ControlsGroup>
                            <RichTextEditor.BulletList icon={() => <BsListUl />} />
                            <RichTextEditor.OrderedList icon={() => <BsListOl />} />
                        </RichTextEditor.ControlsGroup>
                    )}

                    {fileGroup && addFile && (
                        <RichTextEditor.ControlsGroup>
                            <FileControl group={fileGroup} addFile={addFile} />
                        </RichTextEditor.ControlsGroup>
                    )}
                </RichTextEditor.Toolbar>

                <RichTextEditor.Content />

                {!!files?.length && <FilesPreview files={files} handleDelete={deleteFile} />}
            </RichTextEditor>
        </InputWrapper>
    );
};

const FileControl = ({ group, addFile }: { group: string; addFile: (file: string) => void }) => {
    const [loading, setLoading] = useState(false);

    const onChange = async (uploads: File[]) => {
        uploads.forEach((file) => {
            setLoading(true);

            FileService.create({
                file,
                group,
            })
                .then((upload) => {
                    addFile(upload.id);
                })
                .catch((error) => {
                    if (error instanceof FileSizeLimitExceededError) {
                        LocalNotificationService.showError({ title: error.title, message: error.message });
                    } else {
                        throw error;
                    }
                })
                .finally(() => {
                    setLoading(false);
                });
        });
    };

    return (
        <FileButton onChange={onChange} multiple>
            {({ onClick }) => (
                <Tooltip label="Add attachement">
                    <RichTextEditor.Control onClick={onClick} disabled={loading}>
                        {loading ? <Loader size={12} color="dimmed" /> : <IoAttachOutline />}
                    </RichTextEditor.Control>
                </Tooltip>
            )}
        </FileButton>
    );
};

const useRTE = (defaultContent?: JSONContent | null, placeholder?: string) => {
    const editor = useEditor(
        {
            extensions: [
                StarterKit,
                Link,
                Placeholder.configure({
                    placeholder,
                }),
                Markdown,
            ],
            content: defaultContent,
        },
        [defaultContent],
    );

    if (!editor) {
        return {};
    }

    return { editor, RTEField };
};

export { useRTE };
