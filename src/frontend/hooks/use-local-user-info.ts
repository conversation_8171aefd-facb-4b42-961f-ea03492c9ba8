import { useEffect, useState } from 'react';

import { useRouter } from 'next/router';

import { UserHelpers } from 'helpers/UserHelpers';

import { useLocalStorage } from '@mantine/hooks';
import { useCurrentUser } from 'hooks/use-current-user';
import { UserReferrer, UserType } from 'models';

const useLocalUserInfo = () => {
    const [initialized, setInitialized] = useState(false);

    const user = useCurrentUser();
    const { query } = useRouter();

    const [_email, setEmail] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.email,
        defaultValue: user?.email ?? (query.email as string),
    });
    const email = _email !== 'undefined' ? _email : undefined;

    const [referrer, setReferrer] = useLocalStorage<UserReferrer | undefined>({
        key: UserHelpers.localStorageKey.referrer,
        defaultValue: user?.referrer ?? (query.referrer as UserReferrer),
    });
    const [type, setType] = useLocalStorage<UserType | undefined>({
        key: UserHelpers.localStorageKey.type,
        defaultValue: user?.type ?? (query.type as UserType),
    });

    useEffect(() => {
        setInitialized(true);
    });

    return { initialized, email, referrer, type, setEmail, setReferrer, setType };
};

export { useLocalUserInfo };
