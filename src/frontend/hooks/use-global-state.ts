import { useEffect, useRef } from 'react';

const counters: Record<any, number> = {};

const useGlobalState = <T, K extends keyof T>(proxy: T, key: K, value: T[K], initial?: T[K]) => {
    const initialised = useRef(false);

    if (!initialised.current) {
        proxy[key] = value;
        counters[key] = (counters[key] || 0) + 1;
    }

    useEffect(() => {
        if (initialised.current) {
            proxy[key] = value;
        }
    }, [value]);

    useEffect(() => {
        initialised.current = true;

        return () => {
            counters[key] = (counters[key] || 0) - 1;

            /**
             * If the counter is 0 it means that there's another useGlobalState hook called somewhere else.
             * This means that this hook already updated the state of this proxy and we don't need to reset the current one.
             *
             * When does this happen you ask?
             * Well, here's a little story about Next.js / React and cleanup effects.
             *
             * Imagine we have page AAA, this page calls useGlobalState(); When we navigate to page BBB its first gonna
             * render page BBB (including a useGlobalState hook) and do it's magic.
             *
             * Once this page has been rendered the other page is being unmounted and calls the cleanup effects,
             * in this case it cleared the proxy key :)
             */
            if (initial !== undefined && counters[key] === 0) {
                proxy[key] = initial;
            }
        };
    }, []);
};

export { useGlobalState };
