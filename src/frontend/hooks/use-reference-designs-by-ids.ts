import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Project } from 'models';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';
import { DesignLibraryService } from 'services/DesignLibraryService';

type UseReferenceDesigns = (projectIds: string[]) => SWRResponse & {
    key: string;
    projects: Project[];
};

const useReferenceDesignsByIds: UseReferenceDesigns = (projectIds) => {
    const fetcher = async () => {
        return DesignLibraryService.getMultiple(projectIds ?? []);
    };

    const key = `/projects/marketplace?ids=${projectIds?.join(',')}`;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key,
            fetcher: fetcher,
            condition: !projectIds || projectIds.length > 0,
        }),
        { keepPreviousData: true },
    );

    return {
        ...swr,
        key,
        projects: swr?.data?.docs || [],
    };
};

export { useReferenceDesignsByIds };
