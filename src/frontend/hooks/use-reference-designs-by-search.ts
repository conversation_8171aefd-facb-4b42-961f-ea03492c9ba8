import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Project } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';

type UseReferenceDesigns = (search: string) => SWRResponse & {
    key: string;
    projects: Project[];
};

const useReferenceDesignsBySearch: UseReferenceDesigns = (search) => {
    const fetcher = async () => {
        return DesignLibraryService.searchByName(search);
    };

    const key = `/projects/marketplace?search=${search}`;
    const swr = useSWRImmutable(key, fetcher, { keepPreviousData: true });

    return {
        ...swr,
        key,
        projects: swr?.data?.docs || [],
    };
};

export { useReferenceDesignsBySearch };
