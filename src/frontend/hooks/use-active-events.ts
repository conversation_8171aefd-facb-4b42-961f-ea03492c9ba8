import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { Event } from 'models';

import { EventService } from 'services/EventService';

const useActiveEvents = (): SWRResponse & {
    events: Event[];
} => {
    const swr = useSWRImmutable<{
        events: Event[];
    }>(`/api/events/active`, EventService.getActiveEvents);

    return {
        ...swr,
        events: swr?.data?.events || [],
    };
};

export { useActiveEvents };
