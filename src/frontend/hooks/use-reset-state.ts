import { Dispatch, SetStateAction, useState } from 'react';

export const useResetState = <T = any>(
    initialState: T,
): readonly [
    T,
    { setState: Dispatch<SetStateAction<T>>; resetState: () => void; setInitialState: (state: T) => void },
] => {
    const [initialState_, setInitialState_] = useState(initialState);
    const [state, setState] = useState(initialState);

    const resetState = () => {
        setState(initialState_);
    };

    const setInitialState = (newInitialState: T) => {
        setInitialState_(newInitialState);
        setState(newInitialState);
    };

    return [state, { setState, resetState, setInitialState }];
};
