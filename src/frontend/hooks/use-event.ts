import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { Event } from 'models';

import { EventService } from 'services/EventService';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

const useEvent = (
    eventId?: string | null,
): SWRResponse & {
    event: Event | null;
} => {
    const swr = useSWRImmutable<Event>(
        ...prepareConditionalSwrArgs({
            key: `/api/event/${eventId}`,
            fetcher: () => EventService.get(eventId!),
            condition: !!eventId,
        }),
    );

    return {
        ...swr,
        event: swr?.data || null,
    };
};

export { useEvent };
