import { Article, CompanyProfile } from 'models';
import { publicConfig } from '@public-config';
import { ApiService } from 'services/ApiService';
import useSWRImmutable from 'swr/immutable';
import { useRouter } from 'next/router';

type GeneralGlobals = {
    bannerMessage?: string;
    maintenanceMessage?: string;
    updateMessage?: {
        title?: string;
        description: string;
        label?: string;
        url?: string;
    };
    highlights?: {
        caseStudies: Article[];
    };
    exampleProfile?: CompanyProfile;
};

const useGeneralGlobals = () => {
    const { pathname } = useRouter();

    const swr = useSWRImmutable<GeneralGlobals>('/api/globals/general', () => {
        if (pathname === '/sorry') {
            return {};
        }

        return ApiService.get(`${publicConfig.urls.api}/globals/general?depth=1`);
    });

    return { ...swr, globals: swr.data };
};

export { useGeneralGlobals };
