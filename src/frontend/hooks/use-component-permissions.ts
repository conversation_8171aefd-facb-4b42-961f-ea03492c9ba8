import { Component, ComponentVisibility, PermissionComponent } from 'models';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentUser } from 'hooks/use-current-user';

const useComponentPermissions = (component: Component) => {
    const user = useCurrentUser();
    const team = useCurrentTeam();

    const canEdit = user?.developer || team?.id === component.team;

    return {
        [PermissionComponent.VIEW]: component.visibility === ComponentVisibility.PUBLIC || canEdit,
        [PermissionComponent.ALL]: canEdit,
        [PermissionComponent.EDIT]: canEdit,
    };
};

export { useComponentPermissions };
