import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { UserInvitation } from 'models';

import { UserHelpers } from 'helpers/UserHelpers';
import { UserInvitationProps, UserService } from 'services/UserService';

const useUserInvitations = (
    props: UserInvitationProps,
): SWRResponse & {
    userInvitations: UserInvitation[];
} => {
    const swr = useSWRImmutable<{ docs: UserInvitation[] }>(UserHelpers.swr.userInvitations(props), () => {
        return UserService.getUserInvitation(props);
    });

    return {
        ...swr,
        userInvitations: swr?.data?.docs || [],
    };
};

export { useUserInvitations };
