import useSWRImmutable from 'swr/immutable';

import { UserService } from 'services/UserService';

const useUsers = (ids: string[]) => {
    const key = `/users/${ids.join(',')}`;
    const fetcher = async () => UserService.getMultiple(ids);

    const swr = useSWRImmutable(key, fetcher);
    const users = swr?.data?.docs || [];

    return {
        ...swr,
        users: users,
    };
};

export { useUsers };
