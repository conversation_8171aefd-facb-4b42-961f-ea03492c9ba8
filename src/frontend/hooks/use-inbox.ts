import { useSnapshot } from 'hooks/use-safe-snapshot';
import useSWRImmutable from 'swr/immutable';

import { state as inboxState } from 'state/inbox';

import { useCurrentUser } from 'hooks/use-current-user';

import { NotificationService } from 'services/NotificationService';

const useInbox = () => {
    const user = useCurrentUser();
    const inbox = useSnapshot(inboxState);

    const { data, isLoading } = useSWRImmutable(user ? '/inbox' : null, () => {
        return NotificationService.inbox();
    });
    const notifications = data || [];
    const unreadCount = notifications.filter((notification) => !notification.readAt).length;

    return {
        ...inbox,
        isLoading,
        notifications,
        unreadCount,
    };
};

export { useInbox };
