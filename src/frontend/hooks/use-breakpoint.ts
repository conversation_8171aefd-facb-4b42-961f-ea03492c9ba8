import { px, useMantineTheme } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';

const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
export type Breakpoint = (typeof breakpoints)[number];

const useBreakpoint = (): Breakpoint | null => {
    const { width } = useViewportSize();
    const theme = useMantineTheme();

    let currentBreakpoint: Breakpoint | null = null;

    const firstBreakpointWidth = px(theme.breakpoints[breakpoints[0]]) as number;
    const lastBreakpointWidth = px(theme.breakpoints[breakpoints[breakpoints.length - 1]]) as number;

    breakpoints.forEach((breakpoint, index) => {
        const prevBreakpointWidth = (px(theme.breakpoints[breakpoints[index]]) as number) ?? 0;
        const breakpointWidth = (px(theme.breakpoints[breakpoints[index + 1]]) as number) ?? 0;

        if (width === 0) {
            return;
        }

        if (width <= firstBreakpointWidth) {
            currentBreakpoint = breakpoints[0];
        }

        if (width > lastBreakpointWidth) {
            currentBreakpoint = breakpoints[breakpoints.length - 1];
        }

        if (width > prevBreakpointWidth && width <= breakpointWidth) {
            currentBreakpoint = breakpoint;
        }
    });

    return currentBreakpoint;
};

export { useBreakpoint };
