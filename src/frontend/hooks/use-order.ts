import { useCallback, useMemo } from 'react';

import useS<PERSON>, { SWRConfiguration, SWRResponse } from 'swr';
import { assign, merge } from 'radash';

import { OrderService } from 'services/OrderService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { Order, OrderComponentUpdate, OrderStatus, OrderStatuses } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

type OrderEventTriggers = Record<OrderStatus, () => Promise<void>>;

export type OrderController = {
    order: Order;
    addComponent: (componentId: string) => Promise<void>;
    removeComponent: (componentId: string) => Promise<void>;
    updateComponent: (componentId: string, update: OrderComponentUpdate) => Promise<void>;
    renameOrder: (newName: string) => Promise<void>;
    addOrderEvent: (type: OrderStatus) => Promise<void>;
    eventTriggers: OrderEventTriggers;
    addMessage: (message: string) => Promise<void>;
    isEmpty?: boolean;
    orderCompany?: { manufacturer: string } | { distributor: string } | null;
} & SWRResponse;

type UseOrder = (
    orderOrId: Order | string,
    config?: { fallback?: Order; depth?: number },
) => SWRResponse & ({ order: null } | OrderController);

const useOrder: UseOrder = (orderOrId, { fallback, depth } = {}) => {
    const id = typeof orderOrId === 'string' ? orderOrId : orderOrId?.id;

    const config: SWRConfiguration = {
        revalidateOnFocus: false,
    };

    const swr = useSWR<Order>(
        ...prepareConditionalSwrArgs({
            key: `/quotes/${id}`,
            fetcher: () => OrderService.get(id, depth),
            condition: Boolean(id),
        }),
        fallback ? { [`orders/${id}`]: fallback, ...config } : config,
    );

    const order = useMemo(() => swr.data ?? null, [swr.data]);

    const orderCompany = useMemo((): OrderController['orderCompany'] => {
        const manufacturer = order?.reviewers?.manufacturers?.[0];

        if (manufacturer) {
            return {
                manufacturer,
            };
        }

        return null;
    }, [order]);

    const mutateHelper = useCallback(
        async (updatedOrder: Order) => {
            await swr.mutate(
                async (oldOrder?: Order) =>
                    oldOrder &&
                    assign(oldOrder, {
                        components: merge(oldOrder.components ?? [], updatedOrder.components ?? [], (w) => w.component),
                    } as Order),
            );
        },
        [swr.mutate],
    );

    const errorHelper = () => {
        Promise.resolve();
        LocalNotificationService.showError({
            message: 'Something went wrong while updating the order, please try again.',
        });
    };

    const addComponent = useCallback(
        async (componentId: string) => {
            try {
                const updatedOrder = await OrderService.addComponent(id, componentId);

                await mutateHelper(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, mutateHelper],
    );

    const removeComponent = useCallback(
        async (componentId: string) => {
            try {
                const updatedOrder = await OrderService.removeComponent(id, componentId);

                await mutateHelper(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, mutateHelper],
    );

    const updateComponent = useCallback(
        async (componentId: string, update: OrderComponentUpdate) => {
            try {
                const updatedOrder = await OrderService.updateComponent(id, componentId, update);

                await mutateHelper(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, mutateHelper],
    );

    const renameOrder = useCallback(
        async (newName: string) => {
            try {
                const updatedOrder = await OrderService.update(id, { name: newName });

                await mutateHelper(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, swr.mutate],
    );

    const addOrderEvent = useCallback(
        async (type: OrderStatus) => {
            try {
                const updatedOrder = await OrderService.addEvent(id, type);
                await swr.mutate(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, swr.mutate],
    );

    const eventTriggers = useMemo(
        () =>
            Object.fromEntries(
                OrderStatuses.map((status) => [status, () => addOrderEvent(status)]),
            ) as OrderEventTriggers,
        [addOrderEvent],
    );

    const addMessage = useCallback(
        async (message: string) => {
            try {
                const updatedOrder = await OrderService.addMessage(id, message);
                await swr.mutate(updatedOrder);
            } catch {
                errorHelper();
            }
        },
        [id, swr.mutate],
    );

    if (order) {
        return {
            ...swr,
            order,
            addComponent,
            removeComponent,
            updateComponent,
            renameOrder,
            addOrderEvent,
            eventTriggers,
            addMessage,
            isEmpty: !order || !order.components || order.components.length === 0,
            orderCompany,
        };
    }

    return {
        ...swr,
        order,
    };
};

export { useOrder };
