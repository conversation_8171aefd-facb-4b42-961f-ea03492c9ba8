import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { CompanyProfileListProps, CompanyProfileService } from 'services/CompanyProfileService';

import { CompanyProfile, ManufacturerProfile } from 'models';

const useCompanyProfiles = (
    props?: CompanyProfileListProps,
): SWRResponse & {
    companies: CompanyProfile[];
    page: number;
    totalPages: number;
    totalDocs: number;
} => {
    const swr = useSWRImmutable<{
        docs: ManufacturerProfile[];
        page: number;
        totalPages: number;
        totalDocs: number;
    }>(
        `/api/profile?query=${JSON.stringify(props)}`,
        () => {
            return CompanyProfileService.list(props);
        },
        {
            keepPreviousData: true,
        },
    );

    return {
        ...swr,
        companies: swr?.data?.docs || [],
        page: swr?.data?.page || 1,
        totalPages: swr?.data?.totalPages || 1,
        totalDocs: swr?.data?.totalDocs || 0,
    };
};

export { useCompanyProfiles };
