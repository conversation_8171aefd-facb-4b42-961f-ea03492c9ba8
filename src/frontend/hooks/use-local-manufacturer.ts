import { useEffect, useState } from 'react';

import { LocalStorageService } from 'services/LocalStorageService';

const useLocalManufacturer = () => {
    const [manufacturer, setManufacturer] = useState<{
        name: string;
        logo: {
            url: string;
            width: string;
            height: string;
        };
    }>();

    useEffect(() => {
        const localStorageManufacturer = LocalStorageService.get('diagram-manufacturer');

        if (localStorageManufacturer) {
            setManufacturer(localStorageManufacturer);
        }
    }, []);

    return manufacturer;
};

export { useLocalManufacturer };
