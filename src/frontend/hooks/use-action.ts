import { useState } from 'react';

const useAction = <T extends Array<any>, U>(
    handler: (...args: T) => Promise<U>,
): [(...args: T) => Promise<U>, boolean, boolean, boolean] => {
    const [running, setRunning] = useState(false);
    const [finished, setFinished] = useState(false);
    const [justFinished, setJustFinished] = useState(false);

    const wrapped = async (...args: T) => {
        setRunning(true);
        setFinished(false);
        setJustFinished(false);

        const result = await handler(...args);

        setRunning(false);
        setFinished(true);
        setJustFinished(true);

        setTimeout(() => {
            setJustFinished(false);
        }, 5000);

        return result;
    };

    return [wrapped, running, finished, justFinished];
};

export { useAction };
