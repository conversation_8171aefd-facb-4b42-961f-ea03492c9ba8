import { ExhibitorMatchService } from 'services/ExhibitorMatchService';
import useSWRImmutable from 'swr/immutable';

export const useExhibitorMatchLeadsForCompany = (companyId: string) => {
    const swr = useSWRImmutable(
        `/api/exhibitor-match-leads/${companyId}`,
        () => {
            return ExhibitorMatchService.getLeadsForCompany(companyId);
        },
        {
            revalidateOnMount: true,
        },
    );

    return {
        ...swr,
        leads: swr.data?.docs || [],
    };
};
