import useSWR, { SWRResponse } from 'swr';

import { useRouter } from 'next/router';

import { ProjectService } from 'services/ProjectService';

import { Project } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

type UseProject = (
    useProjectArguments?:
        | null
        | Project['id']
        | {
              id: Project['id'];
          },
    options?: { depth?: number },
) => SWRResponse & {
    project: Project | null;
};

const useProject: UseProject = (useProjectArguments = null, { depth = 0 } = {}) => {
    const { query } = useRouter();

    let projectId = query.projectId as string | undefined;

    if (useProjectArguments && typeof useProjectArguments === 'string') {
        projectId = useProjectArguments;
    }

    if (useProjectArguments && typeof useProjectArguments === 'object') {
        projectId = useProjectArguments.id;
    }

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: `/api/projects/${projectId}?depth=${depth}`,
            fetcher: () => ProjectService.get(projectId!, { depth }),
            condition: Boolean(projectId),
        }),
    );

    return {
        ...swr,
        project: swr.data as Project,
    };
};

export { useProject };
