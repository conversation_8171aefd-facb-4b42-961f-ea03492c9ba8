import useSWR, { SWRResponse } from 'swr';

import { ProjectService } from 'services/ProjectService';

import { Project, ProjectDesign } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

type UseProjectByDesign = (
    useProjectArguments?:
        | null
        | ProjectDesign['id']
        | {
              id: ProjectDesign['id'];
          },
    options?: { depth?: number },
) => SWRResponse & {
    project: Project | null;
};

const useProjectByDesign: UseProjectByDesign = (useProjectArguments = null, { depth = 0 } = {}) => {
    let designId = '';

    if (useProjectArguments && typeof useProjectArguments === 'string') {
        designId = useProjectArguments;
    }

    if (useProjectArguments && typeof useProjectArguments === 'object') {
        designId = useProjectArguments.id;
    }

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: `/api/projects?depth=${depth}&designId=${designId}`,
            fetcher: () => ProjectService.getByDesign(designId),
            condition: Boolean(designId),
        }),
    );

    return {
        ...swr,
        project: swr.data?.doc as Project,
    };
};

export { useProjectByDesign };
