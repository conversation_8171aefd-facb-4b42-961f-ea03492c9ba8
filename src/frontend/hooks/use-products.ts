import useSWRImmutable from 'swr/immutable';

import { ComponentListType, ComponentService } from 'services/ComponentService';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

const useProducts = (props: ComponentListType = {}, condition: boolean = true) => {
    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/components?${JSON.stringify(props)}`,
            fetcher: () => {
                return ComponentService.list(props);
            },
            condition,
        }),
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            keepPreviousData: true,
        },
    );

    return {
        ...swr,
        products: swr.data?.docs ?? [],
    };
};

export { useProducts };
