import useSWR from 'swr';

import { SupportCenterService } from 'services/SupportCenterService';
import { CompanyProfile } from 'models';

const useSupportCenter = (company?: CompanyProfile) => {
    const swr = useSWR(
        `/support-center/${company?.id}`,
        async () => {
            return SupportCenterService.get(company?.id);
        },
        {
            revalidateOnMount: true,
        },
    );

    return {
        ...swr,
        supportCenterChannels: swr.data || [],
    };
};

export { useSupportCenter };
