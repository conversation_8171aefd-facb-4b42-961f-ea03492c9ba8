import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { StandardService } from 'services/StandardService';

type Standard = {
    id: string;
    name: string;
};

const useStandards = (): SWRResponse & {
    standards: Standard[];
} => {
    const swr = useSWRImmutable<{
        docs: Standard[];
    }>('/api/standards', () => {
        return StandardService.list();
    });

    return {
        ...swr,
        standards: swr?.data?.docs || [],
    };
};

export { useStandards };
