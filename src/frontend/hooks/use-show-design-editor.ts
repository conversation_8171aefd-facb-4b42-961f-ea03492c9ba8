import { UserReferrer } from 'models';
import { useCurrentTeam } from './use-current-team';
import { useCurrentUser } from './use-current-user';
import { TeamHelpers } from 'helpers/TeamHelpers';
import { useLocalUserInfo } from './use-local-user-info';

export const useShowDesignEditor = (): boolean => {
    const user = useCurrentUser();
    const team = useCurrentTeam();
    const { referrer: referrerInLocalStorage } = useLocalUserInfo();

    const referrer = user?.referrer ?? referrerInLocalStorage;
    const isReplus = referrer === UserReferrer.REPLUS;

    const enableCheck = isReplus;

    return TeamHelpers.showDesignEditor(team, enableCheck);
};
