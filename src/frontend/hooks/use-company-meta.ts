import { CompanyProfile, CompanyService, CompanyServiceOptions } from 'models';

const EXCLUDE_SERVICES = [CompanyService.IN_APP_SUPPORT, CompanyService.OTHER];

const useCompanyMeta = (company: CompanyProfile) => {
    const country = company.locations[0]?.address?.country;

    const subtitle = [];

    if (country) {
        subtitle.push(country);
    }

    if (company.services) {
        subtitle.push(
            ...company.services
                .filter((service) => !EXCLUDE_SERVICES.includes(service))
                .map((service) => CompanyServiceOptions.find((option) => option.value === service)?.label),
        );
    }

    return {
        subtitle: subtitle.join(' • '),
    };
};

export { useCompanyMeta };
