import useSWRImmutable from 'swr/immutable';

import { TeamService } from 'services/TeamService';
import { Team } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';
import { getId } from 'helpers/getId';

import { useCurrentUser } from 'hooks/use-current-user';
import { useRouter } from 'next/router';

const useTeam = (team: Team | string | undefined) => {
    const user = useCurrentUser();
    const teamId = team && getId(team);

    const swr = useSWRImmutable<Team>(
        ...prepareConditionalSwrArgs({
            key: `/api/teams/${teamId}`,
            fetcher: () => TeamService.get(teamId!),
            condition: <PERSON><PERSON><PERSON>(user) && <PERSON><PERSON><PERSON>(teamId),
        }),
    );

    return { ...swr, team: swr.data || null };
};

const useTeamFromURL = () => {
    const { query } = useRouter();

    const { team } = query as { team: string | undefined };

    return useTeam(team);
};

export { useTeam, useTeamFromURL };
