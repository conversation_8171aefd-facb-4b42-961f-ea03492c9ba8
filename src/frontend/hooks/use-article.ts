import useSWRImmutable from 'swr/immutable';

import { ArticleHelpers } from 'helpers/ArticleHelpers';
import { ArticleService } from 'services/ArticleService';

const useArticle = (articleId: string) => {
    const key = ArticleHelpers.swr.get(articleId);
    const swr = useSWRImmutable(key, async () => {
        return ArticleService.get(articleId);
    });

    return {
        ...swr,
        article: swr.data || null,
    };
};

export { useArticle };
