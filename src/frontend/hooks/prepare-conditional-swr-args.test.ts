import { expect, test } from '@jest/globals';

import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

test('key and fetcher should be returned when condition is true', () => {
    const key = 'key';
    const fetcher = () => null;

    const [preparedKey, preparedFetcher] = prepareConditionalSwrArgs({
        key,
        fetcher,
        condition: true,
    });

    expect(preparedKey).toEqual(key);
    expect(preparedFetcher).toBe(fetcher);
});

test('null should be returned when conditional is false', () => {
    const key = 'key';
    const fetcher = () => null;

    const [preparedKey, preparedFetcher] = prepareConditionalSwrArgs({
        key,
        fetcher,
        condition: false,
    });

    expect(preparedKey).toBeNull();
    expect(preparedFetcher).toBeNull();
});
