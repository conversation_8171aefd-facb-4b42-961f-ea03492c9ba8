import useSWRImmutable from 'swr/immutable';

import { publicConfig } from '@public-config';

import { ApiService } from 'services/ApiService';
import { Subscription } from 'models';

const usePrices = () => {
    const swr = useSWRImmutable<Record<Subscription, { monthly: number; yearly: number }>>(
        `/subscriptions/prices`,
        () => ApiService.get(`${publicConfig.urls.api}/subscriptions/prices`),
    );

    return swr;
};

export { usePrices };
