import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { OrderService } from 'services/OrderService';
import { Order } from 'models';

type UseOrders = (useOrdersArguments?: { depth?: number; limit?: number }) => SWRResponse & {
    orders: Order[];
};

const useOrders: UseOrders = (useOrdersArguments = {}) => {
    const { depth = 1, limit = 16 } = useOrdersArguments;

    const key = `/orders?depth=${depth}&limit=${limit}`;

    const fetcher = async () => {
        return OrderService.list({ depth, limit });
    };

    const swr = useSWRImmutable<{ docs: Order[] }>(key, fetcher);

    return {
        ...swr,
        orders: swr.data?.docs ?? [],
    };
};

export { useOrders };
