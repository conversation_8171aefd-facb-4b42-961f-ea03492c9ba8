import { DateService } from 'services/DateService';
import { Order } from 'models';

const useOrderMeta = (order?: Order | null) => {
    const { id, createdBy, createdAt } = order || {};

    const url = `/quotes/${id}`;

    const createdByLabel = createdBy?.name || createdBy?.email;
    const createdAtLabel = createdAt ? DateService.formatDistanceToNow(createdAt) : null;

    const totalComponentCount = (order?.components ?? []).reduce((sum, component) => sum + component.quantity, 0);
    const uniqueComponentCount = order?.components?.length ?? 0;

    return {
        url,
        createdBy: createdByLabel,
        createdAt: createdAtLabel,
        totalComponentCount,
        uniqueComponentCount,
    };
};

export { useOrderMeta };
