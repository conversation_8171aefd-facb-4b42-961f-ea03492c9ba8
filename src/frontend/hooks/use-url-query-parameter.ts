import { useRouter } from 'next/router';

export const useURLQueryParameter = (key: string): [string | string[] | undefined, (newValue: string) => void] => {
    const router = useRouter();
    const value = router.query[key];

    const setValue = (newValue: string) => {
        router.push({
            pathname: router.pathname,
            query: {
                ...router.query,
                [key]: newValue,
            },
        });
    };

    return [value, setValue];
};
