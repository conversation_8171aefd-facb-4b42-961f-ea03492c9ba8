import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { CompanyBase, CompanyProfile } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

const useCompanyProfile = (
    profile?: CompanyBase | CompanyBase['id'] | null,
): SWRResponse & {
    company: CompanyProfile | undefined;
} => {
    const profileId = profile && typeof profile === 'object' ? profile.id : profile;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: CompanyProfileHelpers.swr.profile(profileId!),
            fetcher: () => CompanyProfileService.get(profileId!),
            condition: <PERSON><PERSON>an(profileId),
        }),
    );

    return {
        ...swr,
        company: swr?.data,
    };
};

const useCompanyProfileBySlug = (
    slug?: CompanyProfile['slug'] | null,
): SWRResponse & {
    company: CompanyProfile | undefined;
} => {
    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: CompanyProfileHelpers.swr.profile(slug!),
            fetcher: () => CompanyProfileService.getBySlug(slug!),
            condition: Boolean(slug),
        }),
    );

    return {
        ...swr,
        company: swr?.data?.docs?.[0],
    };
};

export { useCompanyProfile, useCompanyProfileBySlug };
