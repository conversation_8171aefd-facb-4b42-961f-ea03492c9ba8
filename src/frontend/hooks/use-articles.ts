import useSWR from 'swr/immutable';

import { ArticleService } from 'services/ArticleService';
import { ArticleHelpers } from 'helpers/ArticleHelpers';

const useArticles = (companyId: string, type: string) => {
    const key = ArticleHelpers.swr.list(companyId, type);
    const swr = useSWR(key, async () => {
        return ArticleService.list(companyId, type);
    });

    return {
        ...swr,
        articles: swr.data || [],
    };
};

const useArticleSearch = ({ query }: { query?: string } = {}) => {
    const { data: articles = [], ...swr } = useSWR(ArticleHelpers.swr.search(query), async () => {
        const response = await ArticleService.search(query);
        return response.docs;
    });

    return {
        articles,
        totalArticles: articles.length,
        ...swr,
    };
};

export { useArticles, useArticleSearch };
