import { useEffect } from 'react';

import { AuthenticationService } from 'services/AuthenticationService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { UserService } from 'services/UserService';

import { useRouter } from 'next/router';
import { useCurrentUser } from 'hooks/use-current-user';
import { useWindowEvent } from '@mantine/hooks';

const useHeartbeat = () => {
    const router = useRouter();
    const user = useCurrentUser();

    const check = () => {
        if (!user?.id) {
            return;
        }

        UserService.heartbeat().then(async (heartbeat) => {
            if (typeof heartbeat === 'object' && 'authenticated' in heartbeat) {
                if (!heartbeat.authenticated) {
                    InternalTrackingService.track('user.session.expired', {
                        user,
                    });

                    await AuthenticationService.logout('expired');
                }
            } else {
                console.error('Unexpected heartbeat response', heartbeat);
            }
        });
    };

    useEffect(() => {
        check();
    }, [router.pathname, user?.id]);

    useWindowEvent('focus', () => {
        check();
    });
};

export { useHeartbeat };
