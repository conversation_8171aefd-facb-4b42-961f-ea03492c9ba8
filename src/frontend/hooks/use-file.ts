import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { FileService } from 'services/FileService';
import { File } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

const useFile = (
    file?: any,
): SWRResponse & {
    file: File;
} => {
    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/files/${file}`,
            fetcher: () => FileService.get(file),
            condition: file && typeof file === 'string',
        }),
    );

    return {
        ...swr,
        file: swr?.data ?? (typeof file === 'object' && file),
    };
};

export { useFile };
