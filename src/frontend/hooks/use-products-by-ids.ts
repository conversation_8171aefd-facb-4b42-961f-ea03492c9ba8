import useSWR, { SWRResponse } from 'swr';

import { Component } from 'models';

import { ComponentService } from 'services/ComponentService';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

const useProductsByIds = (
    ids?: string[],
): SWRResponse & {
    components: Component[];
} => {
    const key = Array.isArray(ids) ? `/components/${ids.join(',')}` : '/components';
    const fetcher = Array.isArray(ids) ? () => ComponentService.getMultiple(ids) : () => ComponentService.list();

    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key,
            fetcher,
            condition: !!ids && !!ids.length,
        }),
        {
            revalidateOnFocus: false,
            keepPreviousData: true,
        },
    );

    return {
        ...swr,
        components: swr?.data?.docs || [],
    };
};

export { useProductsByIds };
