import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { Event } from 'models';

import { EventService } from 'services/EventService';

const useEvents = (props?: {
    depth?: number;
}): SWRResponse & {
    events: Event[];
} => {
    const { depth = 0 } = props || {};

    const swr = useSWRImmutable<{
        events: Event[];
    }>(`/api/events`, () => EventService.list({ depth }));

    return {
        ...swr,
        events: swr?.data?.events || [],
    };
};

export { useEvents };
