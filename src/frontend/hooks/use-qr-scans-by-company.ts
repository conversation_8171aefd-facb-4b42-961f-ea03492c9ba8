import useSWR from 'swr/immutable';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

import { ShowtimeService } from 'services/ShowtimeService';
import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';

const useQrScansByCompany = ({ companyId }: { companyId: string }) => {
    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: ShowtimeHelpers.swr.qrScansByCompany(companyId),
            fetcher: () => ShowtimeService.getQrScansByCompany({ companyId }),
            condition: <PERSON><PERSON><PERSON>(companyId),
        }),
        { refreshInterval: 60 * 1000 },
    );

    return {
        qrScans: swr.data || [],
        ...swr,
    };
};

export { useQrScansByCompany };
