import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Project } from 'models';

import { DesignLibraryService } from 'services/DesignLibraryService';

type UseReferenceDesigns = (
    componentId: string,
    includeUnpublished?: boolean,
) => SWRResponse & {
    key: string;
    projects: Project[];
};

const useReferenceDesignsByComponent: UseReferenceDesigns = (componentId, includeUnpublished = false) => {
    const fetcher = async () => {
        return DesignLibraryService.getByComponent(componentId, includeUnpublished);
    };

    const key = `/projects/marketplace?component=${componentId}&includeUnpublished=${includeUnpublished}`;
    const swr = useSWRImmutable(key, fetcher);

    return {
        ...swr,
        key,
        projects: swr?.data?.docs || [],
    };
};

export { useReferenceDesignsByComponent };
