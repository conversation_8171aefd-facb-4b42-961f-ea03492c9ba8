import useSWRImmutable from 'swr/immutable';

import { Component } from 'models';

import { ComponentService } from 'services/ComponentService';

import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';
import { useCurrentTeam } from './use-current-team';

export const useTeamProducts = (): { products: Component[]; isLoading: boolean; error: boolean } => {
    const team = useCurrentTeam();
    const teamId = team?.id;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/api/components?where[team][equals]=${teamId}`,
            fetcher: () => {
                if (!teamId) {
                    return null;
                }

                return ComponentService.getAll(teamId);
            },
            condition: <PERSON><PERSON><PERSON>(teamId),
        }),
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            keepPreviousData: true,
        },
    );

    const { data, error } = swr;

    return {
        products: data?.data?.docs ?? [],
        isLoading: !error && !data,
        error: error,
    };
};
