import useSWR from 'swr';
import { DiagramComment, Project, ProjectDesign, SubscribableType } from 'models';
import { Order } from 'models';
import { SubscriptionService } from 'services/SubscriptionService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { useCurrentUser } from 'hooks/use-current-user';

type AllSubscriptions = {
    orders: Order[];
    projects: Project[];
    projectDesigns: ProjectDesign[];
    diagramComments: DiagramComment[];
};

const useSubscriptions = () => {
    const user = useCurrentUser();
    const { data: subscriptions, mutate } = useSWR<AllSubscriptions>('subscriptions', SubscriptionService.list);

    const subscribe = subscriptions
        ? async (type: SubscribableType, id: string, mutateAfter?: boolean) => {
              try {
                  if (!user) {
                      return Promise.reject();
                  }

                  await SubscriptionService.subscribe({ type, id, userId: user.id });

                  mutateAfter && mutate();
              } catch {
                  LocalNotificationService.showError({
                      message: 'Something went wrong while updating notifications, please try again.',
                  });
              }
          }
        : undefined;

    const unsubscribe = subscriptions
        ? async (type: SubscribableType, id: string, mutateAfter?: boolean) => {
              try {
                  if (!user) {
                      return Promise.reject();
                  }

                  await SubscriptionService.unsubscribe({ type, id, userId: user.id });

                  if (mutateAfter) {
                      mutate({
                          ...subscriptions,
                          [`${type}s`]: subscriptions[`${type}s`].filter((collection) => collection.id !== id),
                      });
                  }
              } catch {
                  LocalNotificationService.showError({
                      message: 'Something went wrong while updating notifications, please try again.',
                  });
              }
          }
        : undefined;

    return { subscriptions, subscribe, unsubscribe };
};

export { useSubscriptions };
