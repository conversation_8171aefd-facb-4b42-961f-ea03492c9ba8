import { DesignerSubscription, getDesignerSubscriptionData } from 'models';
import { useCurrentTeam } from './use-current-team';

const useCurrentTeamSubscription = (): DesignerSubscription => {
    const team = useCurrentTeam();

    if (!team) return DesignerSubscription.FREE;

    const subscriptionData = getDesignerSubscriptionData(team.subscriptions);

    return subscriptionData?.subscription ?? DesignerSubscription.FREE;
};

export { useCurrentTeamSubscription };
