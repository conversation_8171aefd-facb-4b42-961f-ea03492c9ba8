import { PermissionProject, TeamInfo } from 'models';
import { useCurrentUser } from 'hooks/use-current-user';
import { useTeams } from 'hooks/use-teams';
import { PermissionService } from 'services/PermissionService';
import { useTeamProjectCounts } from './use-team-project-counts';

const useCreateProjectTeams = (): {
    createTeams: TeamInfo[];
    defaultCreateTeam: TeamInfo | null;
    isLoading: boolean;
} => {
    const { teams = [] } = useTeams();
    const user = useCurrentUser();
    const { teamProjectCounts, isLoading } = useTeamProjectCounts();

    if (!teamProjectCounts) {
        return { createTeams: [], defaultCreateTeam: null, isLoading };
    }

    const createTeams = teams.filter(hasAllPermission);

    const defaultCreateTeam = createTeams.find((team) => team.id === user?.team) ?? createTeams[0];

    return { createTeams, defaultCreateTeam, isLoading };
};

const hasAllPermission = (team: TeamInfo) => {
    return PermissionService.canPermissions(PermissionProject.ALL, team.permissions || []);
};

export { useCreateProjectTeams };
