import { useClipboard } from '@mantine/hooks';

import { InternalTrackingService } from 'services/InternalTrackingService';

const useCopyShareUrl = (type: 'product' | 'profile', searchParams: Record<string, string> = {}) => {
    const clipboard = useClipboard({ timeout: 2000 });

    const share = () => {
        InternalTrackingService.track(`${type}.share`);

        const url = new URL(window.location.href);

        Object.entries(searchParams).forEach(([key, value]) => {
            url.searchParams.set(key, value);
        });

        clipboard.copy(url.toString());
    };

    return {
        clipboard,
        share,
    };
};

export { useCopyShareUrl };
