import useSWRImmutable from 'swr/immutable';

import { Diagram } from 'models';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';
import { ProjectDiagramService } from 'services/ProjectDiagramService';

export const useProjectDesignDiagram = (diagramId: Diagram['id']) => {
    const swr = useSWRImmutable<Diagram>(
        ...prepareConditionalSwrArgs({
            key: `/api/projectDesignDiagram/${diagramId}`,
            fetcher: () => ProjectDiagramService.get(diagramId),
            condition: <PERSON><PERSON><PERSON>(diagramId),
        }),
    );

    return { ...swr, diagram: swr.data };
};
