import { useRouter } from 'next/router';
import { proxy, useSnapshot } from 'valtio';

export const mobileNavState = proxy({
    isOpen: true,
});

const useMobileNav = () => {
    const { isOpen } = useSnapshot(mobileNavState);

    const router = useRouter();
    const routeIsDiagram = router.asPath.includes('/editor');

    return {
        showMobileNav: isOpen && !routeIsDiagram,
    };
};

export { useMobileNav };
