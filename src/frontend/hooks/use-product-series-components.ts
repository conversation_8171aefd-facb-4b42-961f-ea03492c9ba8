import { Component } from 'models';
import { ComponentService } from 'services/ComponentService';
import useSWR from 'swr';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

const useProductSeriesComponents = ({
    manufacturerId,
    productSeries,
}: {
    manufacturerId?: string;
    productSeries?: string;
}) => {
    const swr = useSWR<Component[]>(
        ...prepareConditionalSwrArgs({
            key: `/components/product-series/${manufacturerId}/${productSeries}`,
            fetcher: () =>
                ComponentService.getProductSeriesComponents({
                    manufacturerId: manufacturerId!,
                    productSeries: productSeries!,
                }),
            condition: <PERSON><PERSON><PERSON>(manufacturerId && productSeries),
        }),
    );

    return { ...swr, components: swr.data };
};

export { useProductSeriesComponents };
