import useSWRImmutable from 'swr/immutable';

import { SearchService } from 'components/component-overview/services/SearchService';

const useSearchAutosuggest = (query: string) => {
    const key = query ? `/search-autosuggest?search=${query}` : null;

    const swr = useSWRImmutable(key, async () => {
        return SearchService.getAutosuggest(query);
    });

    const { companies, products } = swr?.data || {
        companies: [],
        products: [],
    };

    return {
        ...swr,
        companies,
        products,
    };
};

export { useSearchAutosuggest };
