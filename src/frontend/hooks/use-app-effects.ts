import { useEffect } from 'react';

import { User } from 'models';

import { savedItemsState } from 'state/current-saved-items';
import { SavedItemsService } from 'services/SavedItemsService';

const useAppEffects = (user?: User) => {
    useEffect(() => {
        if (!user) return;
        if (savedItemsState.initialized) return;

        const fetchSavedItems = async () => {
            const result = await SavedItemsService.list({
                userId: user.id,
            });

            if (result?.docs) {
                savedItemsState.savedItems = result.docs;
                savedItemsState.initialized = true;
            }
        };

        fetchSavedItems();
    }, [user]);
};

export { useAppEffects };
