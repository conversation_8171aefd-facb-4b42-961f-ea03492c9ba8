import useSWR, { SWRResponse } from 'swr';

import { CollectionFileWithFile, Component, ComponentVisibility } from 'models';

import { ComponentService } from 'services/ComponentService';
import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

const useComponentFilesPublicOnly = (
    componentId: Component['id'] | null,
): SWRResponse & {
    files: CollectionFileWithFile[];
} => {
    return useComponentFiles(componentId, ComponentVisibility.PUBLIC);
};

const useComponentFilesPrivateOnly = (
    componentId: Component['id'] | null,
): SWRResponse & {
    files: CollectionFileWithFile[];
} => {
    return useComponentFiles(componentId, ComponentVisibility.PRIVATE);
};

const useComponentFiles = (componentId: string | null, query: ComponentVisibility) => {
    const swr = useSWR(
        ...prepareConditionalSwrArgs({
            key: `/components/${componentId}/files?${query}`,
            fetcher: () => ComponentService.getFiles(componentId!, query),
            condition: Boolean(componentId),
        }),
        { revalidateOnFocus: false },
    );

    return {
        ...swr,
        files: swr?.data || [],
    };
};
export { useComponentFilesPublicOnly, useComponentFilesPrivateOnly };
