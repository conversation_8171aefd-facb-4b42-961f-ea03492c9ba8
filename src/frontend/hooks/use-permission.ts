import { useSnapshot } from 'hooks/use-safe-snapshot';

import { PermissionDiagram } from 'models';

import { permissionsState } from 'state/current-project-permissions';

export const usePermission = (permissionToCheck: PermissionDiagram): boolean => {
    const state = useSnapshot(permissionsState);

    if (state.permissions[permissionToCheck]) {
        return Boolean(state.permissions[permissionToCheck]);
    }

    return false;
};
