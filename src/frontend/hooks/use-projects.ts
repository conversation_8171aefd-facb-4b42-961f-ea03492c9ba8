import { SWRResponse } from 'swr';

import useSWRImmutable from 'swr/immutable';

import { Project } from 'models';

import { ProjectService } from 'services/ProjectService';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

type UseProjects = (useProjectsArguments?: {
    projectIds?: string[];
    depth?: number;
    limit?: number;
    keepPreviousData?: boolean;
}) => SWRResponse & {
    key: string;
    projects: Project[];
};

const useProjects: UseProjects = (useProjectsArguments = {}) => {
    const { depth = 1, limit = 16, projectIds, keepPreviousData } = useProjectsArguments;

    const fetcher = async () => {
        return ProjectService.list({
            depth,
            limit,
            projectIds,
        });
    };

    const key = `projects?depth=${depth}&limit=${limit}&ids=${projectIds?.join(',')}`;

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key,
            fetcher: fetcher,
            condition: !projectIds || projectIds.length > 0,
        }),
        { keepPreviousData },
    );

    return {
        ...swr,
        key,
        projects: swr?.data?.docs || [],
    };
};

export { useProjects };
