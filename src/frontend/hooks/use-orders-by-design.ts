import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { OrderService } from 'services/OrderService';
import { Order } from 'models';
import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

type UseOrdersByDesign = (designId?: string) => SWRResponse & {
    orders: Order[];
};

const useOrdersByDesign: UseOrdersByDesign = (designId) => {
    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: `/orders/getByDesign?designId=${designId}`,
            fetcher: () => OrderService.getByDesign(designId),
            condition: Bo<PERSON>an(designId),
        }),
    );

    return {
        ...swr,
        orders: swr.data?.docs ?? [],
    };
};

export { useOrdersByDesign };
