'use client';

import { MantineSize, px, useMantineTheme } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';

const BREAKPOINT: MantineSize = 'md';

const useIsMobile = (breakpoint: MantineSize = BREAKPOINT) => {
    const theme = useMantineTheme();
    const breakpointWidth = px(theme.breakpoints[breakpoint]) as number;

    const { width } = useViewportSize();

    return width !== 0 && width <= breakpointWidth;
};

export { useIsMobile };
