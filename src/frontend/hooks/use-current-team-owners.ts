import { PermissionTeam, Team, User } from 'models';

import { useCurrentTeam } from './use-current-team';

export const useCurrentTeamOwners = (): User['id'][] => {
    const team = useCurrentTeam();

    if (!team) {
        return [];
    }

    return getTeamOwners(team);
};

export const getTeamOwners = (team: Team): User['id'][] => {
    const { users = [] } = team;

    const ownerUserIds = users
        .filter(({ newPermissions }) => newPermissions.includes(PermissionTeam.OWNER))
        .map(({ user }) => user);

    return ownerUserIds;
};
