import { Component, DiagramComponentInstance, PowerFlowDirection, VoltageType } from 'models';

type Port = Component['electrical']['ports'][number];
type PortConfiguration = DiagramComponentInstance['configuration']['ports'][number];

type EnabledVoltageTypes = { hasAc: boolean; hasDc: boolean };

export type CheckResult = {
    matchScore: number;

    purposeMatch: boolean;
    voltageTypeMatch: VoltageType | false;
    powerFlowDirectionMatch: PowerFlowDirection | false;

    voltageValueInRange: boolean;
    currentInRange: boolean;
    powerInRange: boolean;
    frequencyInRange: boolean;
};

export type PortCheckCriteria = Exclude<keyof CheckResult, 'matchScore'>;

enum RangeEndpointResult {
    MISSING = 'missing',
    MATCH = 'match',
    NO_MATCH = 'no_match',
}

class PortCompatabilityChecker {
    port: Port;
    portHasAc: boolean;
    portHasDc: boolean;

    constructor(port: Port) {
        this.port = port;

        const enabledVoltageTypes = PortCompatabilityChecker.getEnabledVoltageTypes(port);
        this.portHasAc = enabledVoltageTypes.hasAc;
        this.portHasDc = enabledVoltageTypes.hasDc;
    }

    public check(specification: Port, configuration = {} as PortConfiguration): CheckResult {
        const result: CheckResult = {
            matchScore: 0,
            purposeMatch: false,
            voltageTypeMatch: false,
            powerFlowDirectionMatch: false,
            voltageValueInRange: false,
            currentInRange: false,
            powerInRange: false,
            frequencyInRange: false,
        };

        const specificationVoltageTypes = PortCompatabilityChecker.getEnabledVoltageTypes(specification);

        this.checkPortPurpose(result, specification);

        this.checkVoltageType(result, specification, configuration);

        // Note: configuration.voltageType may be null
        if (this.portHasAc && specificationVoltageTypes.hasAc && configuration.voltageType !== 'DC') {
            this.checkVoltage(result, specification, 'AC');
            this.checkCurrent(result, specification, 'AC');
            this.checkPower(result, specification, 'AC');
            this.checkFrequency(result, specification);
        }

        // Note: configuration.voltageType may be null
        if (this.portHasDc && specificationVoltageTypes.hasDc && configuration.voltageType !== 'AC') {
            this.checkVoltage(result, specification, 'DC');
            this.checkCurrent(result, specification, 'DC');
            this.checkPower(result, specification, 'DC');
        }

        this.checkPowerFlowDirection(result, specification, configuration);

        return result;
    }

    protected checkPortPurpose(result: CheckResult, specification: Port) {
        const purposeShouldBeChecked =
            'purpose' in this.port && 'purpose' in specification && this.port.purpose !== null;

        if (!purposeShouldBeChecked) {
            result.purposeMatch = true;
            return;
        }

        // `'purpose' in this.port` is redundant but makes typescript happy
        if ('purpose' in this.port && this.port.purpose === specification.purpose) {
            result.purposeMatch = true;
            result.matchScore += 20;
        }
    }

    protected checkVoltageType(result: CheckResult, specification: Port, configuration: PortConfiguration): void {
        const specHasDc = 'DC' in specification && specification.DC.enabled;
        const specHasAc = 'AC' in specification && specification.AC.enabled;

        const bothHaveDc = specHasDc && this.portHasDc;
        const bothHaveAc = specHasAc && this.portHasAc;

        switch (true) {
            case bothHaveAc && bothHaveDc:
                result.matchScore += 15;
                result.voltageTypeMatch = configuration.voltageType ?? 'DC';
                return;

            case bothHaveDc:
                result.matchScore += 10;
                result.voltageTypeMatch = 'DC';
                return;

            case bothHaveAc:
                result.matchScore += 10;
                result.voltageTypeMatch = 'AC';
                return;
        }
    }

    protected checkVoltage(result: CheckResult, specification: Port, voltageType: 'AC' | 'DC') {
        const { min, max } = PortCompatabilityChecker.checkRange({
            // @ts-ignore
            specificationMin: specification[voltageType].voltage?.min,
            // @ts-ignore
            specificationMax: specification[voltageType].voltage?.max,
            // @ts-ignore
            portMin: this.port[voltageType].voltage?.min,
            // @ts-ignore
            portMax: this.port[voltageType].voltage?.max,
        });

        result.voltageValueInRange =
            result.voltageTypeMatch === voltageType &&
            min !== RangeEndpointResult.NO_MATCH &&
            max !== RangeEndpointResult.NO_MATCH;

        if (min === RangeEndpointResult.MATCH) {
            result.matchScore += 1;
        }

        if (max === RangeEndpointResult.MATCH) {
            result.matchScore += 1;
        }
    }

    protected checkCurrent(result: CheckResult, specification: Port, voltageType: 'AC' | 'DC') {
        const endpointResult = PortCompatabilityChecker.checkRangeMax({
            // @ts-ignore
            specificationMax: specification[voltageType].current?.max,
            // @ts-ignore
            portMax: this.port[voltageType].current?.max,
        });

        switch (endpointResult) {
            case RangeEndpointResult.MATCH:
                result.matchScore += 1;
            // falls through
            case RangeEndpointResult.MISSING:
                result.matchScore += 1;
                result.currentInRange = true;
        }
    }

    protected checkPower(result: CheckResult, specification: Port, voltageType: 'AC' | 'DC') {
        const endpointResult = PortCompatabilityChecker.checkRangeMax({
            // @ts-ignore
            specificationMax: specification[voltageType].power?.max,
            // @ts-ignore
            portMax: this.port[voltageType].power?.max,
        });

        switch (endpointResult) {
            case RangeEndpointResult.MATCH:
                result.matchScore += 1;
            // falls through
            case RangeEndpointResult.MISSING:
                result.matchScore += 1;
                result.powerInRange = true;
        }
    }

    protected checkFrequency(result: CheckResult, specification: Port) {
        const { min, max } = PortCompatabilityChecker.checkRange({
            // @ts-ignore
            specificationMin: specification.AC.frequency?.min,
            // @ts-ignore
            specificationMax: specification.AC.frequency?.max,
            // @ts-ignore
            portMin: this.port.AC.frequency?.min,
            // @ts-ignore
            portMax: this.port.AC.frequency?.max,
        });

        result.frequencyInRange = min !== RangeEndpointResult.NO_MATCH && max !== RangeEndpointResult.NO_MATCH;

        if (min === RangeEndpointResult.MATCH) {
            result.matchScore += 1;
        }

        if (max === RangeEndpointResult.MATCH) {
            result.matchScore += 1;
        }
    }

    protected checkPowerFlowDirection(result: CheckResult, specification: Port, configuration: PortConfiguration) {
        switch (this.port.powerFlowDirection) {
            case 'bidirectional':
                result.matchScore += configuration.powerFlowDirection === 'bidirectional' ? 6 : 3;
                result.powerFlowDirectionMatch =
                    configuration.powerFlowDirection ?? specification.powerFlowDirection ?? 'bidirectional';

                return;

            case 'input':
                if (
                    specification.powerFlowDirection === 'bidirectional' ||
                    specification.powerFlowDirection === 'input'
                ) {
                    result.matchScore += 4;
                    result.powerFlowDirectionMatch = 'input';
                }

                return;

            case 'output':
                if (
                    specification.powerFlowDirection === 'bidirectional' ||
                    specification.powerFlowDirection === 'output'
                ) {
                    result.matchScore += 4;
                    result.powerFlowDirectionMatch = 'output';
                }

                return;

            case null:
                result.powerFlowDirectionMatch =
                    configuration.powerFlowDirection ?? specification.powerFlowDirection ?? 'bidirectional';
        }
    }

    protected static getEnabledVoltageTypes(port: Port): EnabledVoltageTypes {
        return {
            hasAc: 'AC' in port && port.AC.enabled,
            hasDc: 'DC' in port && port.DC.enabled,
        };
    }

    protected static checkRange({
        specificationMin,
        specificationMax,
        portMin,
        portMax,
    }: {
        specificationMin?: number;
        specificationMax?: number;
        portMin?: number;
        portMax?: number;
    }): { min: RangeEndpointResult; max: RangeEndpointResult } {
        return {
            min: PortCompatabilityChecker.checkRangeMin({ specificationMin, portMin }),
            max: PortCompatabilityChecker.checkRangeMax({ specificationMax, portMax }),
        };
    }

    protected static checkRangeMin({ specificationMin, portMin }: { specificationMin?: number; portMin?: number }) {
        return !specificationMin || !portMin
            ? RangeEndpointResult.MISSING
            : specificationMin >= portMin
              ? RangeEndpointResult.MATCH
              : RangeEndpointResult.NO_MATCH;
    }

    protected static checkRangeMax({ specificationMax, portMax }: { specificationMax?: number; portMax?: number }) {
        return !specificationMax || !portMax
            ? RangeEndpointResult.MISSING
            : specificationMax <= portMax
              ? RangeEndpointResult.MATCH
              : RangeEndpointResult.NO_MATCH;
    }
}

export { PortCompatabilityChecker };
