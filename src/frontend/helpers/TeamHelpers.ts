import { Permission, PermissionTeam, Team, User } from 'models';
import { PermissionService } from 'services/PermissionService';

const TeamHelpers = {
    urls: {
        requestAccess: (teamId: string) => `/teams/${teamId}/request-access`,
        grantAccess: (teamId: string) => `/teams/${teamId}/grant-access`,
    },

    getUserWithPermissions: (team: Team | null, user: User | User['id'] | null) => {
        if (!team || !user) {
            return null;
        }

        const userId = typeof user === 'string' ? user : user.id;

        return team.users.find((needle) => needle.user === userId);
    },

    getUserRole: (team: Team, user: User) => {
        const teamUser = TeamHelpers.getUserWithPermissions(team, user);
        const teamUserPermissions = teamUser?.newPermissions ?? [];

        if (teamUserPermissions.includes(PermissionTeam.OWNER)) {
            return PermissionTeam.OWNER;
        }

        if (teamUserPermissions.includes(PermissionTeam.ADMIN)) {
            return PermissionTeam.ADMIN;
        }

        if (teamUserPermissions.includes(PermissionTeam.USER)) {
            return PermissionTeam.USER;
        }

        return null;
    },

    isTeamMember: (team: Team | null, user: User | User['id'] | null) => {
        return !!TeamHelpers.getUserWithPermissions(team, user);
    },

    hasPermission: (team: Team, user: User, toCheckPermissions: Permission | Permission[]) => {
        const teamUser = TeamHelpers.getUserWithPermissions(team, user);
        const teamUserPermissions = teamUser?.newPermissions ?? [];

        return PermissionService.canPermissions(toCheckPermissions, teamUserPermissions);
    },

    showDesignEditor: (team: Team | null | undefined, enableCheck: boolean): boolean => {
        if (enableCheck) {
            if (team?.showDesignEditor) {
                return true;
            }

            return false;
        }

        return true;
    },
};

export { TeamHelpers };
