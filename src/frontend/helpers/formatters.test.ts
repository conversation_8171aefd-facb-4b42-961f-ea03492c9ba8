import { expect, test } from '@jest/globals';

import { Voltage, currentConverter, energyConverter } from 'models';
import { FormatHelpers } from './formatters';

describe('FormatHelpers', () => {
    test('formatMinNomMax handles null values', async () => {
        const result = FormatHelpers.formatMinNomMax(undefined, currentConverter);
        expect(result).toBe('');
    });

    test('getUniqueVoltages removes empty voltages', async () => {
        const input: Voltage[] = [
            { min: 0, max: 24, nom: 48, unit: 'V' },
            { min: 0, max: 0, nom: 0, unit: 'V' },
        ];

        const output: Voltage[] = [{ min: 0, max: 24, nom: 48, unit: 'V' }];

        expect(FormatHelpers.getUniqueVoltages(input)).toEqual(output);
    });

    test('getUniqueVoltages sorts by count', async () => {
        const input: Voltage[] = [
            { min: 0, max: 36, nom: 64, unit: 'V' },
            { min: 0, max: 24, nom: 48, unit: 'V' },
            { min: 0, max: 24, nom: 48, unit: 'V' },
            { min: 0, max: 12, nom: 24, unit: 'V' },
        ];

        const output: Voltage[] = [
            { min: 0, max: 24, nom: 48, unit: 'V' },
            { min: 0, max: 36, nom: 64, unit: 'V' },
            { min: 0, max: 12, nom: 24, unit: 'V' },
        ];

        expect(FormatHelpers.getUniqueVoltages(input)).toEqual(output);
    });

    test('getUniqueVoltages works with negative values', async () => {
        const input: Voltage[] = [
            { min: 0, max: 36, nom: 64, unit: 'V' },
            { min: 0, max: -12, nom: -24, unit: 'V' },
        ];

        const output: Voltage[] = [
            { min: 0, max: 36, nom: 64, unit: 'V' },
            { min: 0, max: -12, nom: -24, unit: 'V' },
        ];

        expect(FormatHelpers.getUniqueVoltages(input)).toEqual(output);
    });
    test('formatMeasurement formats measurement correctly', async () => {
        const result = FormatHelpers.formatMeasurement({ value: 10, unit: 'kWh' }, energyConverter);
        expect(result).toBe('10 kWh');
    });

    test('formatValue formats value correctly', async () => {
        const result = FormatHelpers.formatValue(10, currentConverter);
        expect(result).toBe('10 A');
    });

    test('transformValue transforms value correctly', async () => {
        const result = FormatHelpers.transformValue(10, currentConverter, 'A', 'mA');
        expect(result).toBe(10000);
    });

    test('transformToBestUnit transforms to best unit correctly', async () => {
        const result = FormatHelpers.transformToBestUnit({ min: 10, nom: 20, max: 30, unit: 'A' }, currentConverter);
        expect(result).toEqual({ min: 10, nom: 20, max: 30, unit: 'A' });
    });

    test('formatMinNomMax formats min, nom, max correctly', async () => {
        const result = FormatHelpers.formatMinNomMax({ min: 10, nom: 20, max: 30, unit: 'mA' }, currentConverter);
        expect(result).toBe('20 mA (10-30 mA)');
    });

    test('formatOption formats option correctly', async () => {
        const options = [
            { value: '1', label: 'Option 1' },
            { value: '2', label: 'Option 2' },
        ];
        const result = FormatHelpers.formatOption(options, '1');
        expect(result).toBe('Option 1');
    });

    test('formatPowerFlowDirection formats power flow direction correctly', async () => {
        const result = FormatHelpers.formatPowerFlowDirection('input');
        expect(result).toBe('Input');
    });

    test('formatWireSize formats wire size correctly', async () => {
        const result = FormatHelpers.formatWireSize('1.5');
        expect(result).toBe('1.5 mm2, 16 AWG');
    });

    test('formatControlMethod formats control method correctly', async () => {
        const result = FormatHelpers.formatControlMethod('constant-voltage');
        expect(result).toBe('Constant Voltage');
    });

    test('formatIsolation formats isolation correctly', async () => {
        const result = FormatHelpers.formatIsolation(true);
        expect(result).toBe('Isolated');
    });
});
