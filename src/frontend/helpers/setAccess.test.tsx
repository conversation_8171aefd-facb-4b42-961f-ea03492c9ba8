import { expect, test } from '@jest/globals';
import { publicConfig as envConfig } from 'config/public-config';
import { setEnvironmentLockAccessCookie } from './setAccess';

test('should return "Access Token not found" if environmentLockAccess is not provided', () => {
    const res = {};

    const result = setEnvironmentLockAccessCookie(res, null);

    expect(result.props.message).toBe('Access Token not found');
});

test('should set the environmentLockAccess cookie and return "Access Token has been set" if environmentLockAccess matches envConfig', () => {
    const environmentLockAccess = 'your-access-token';
    Object.defineProperty(envConfig, 'environmentLockAccess', { value: environmentLockAccess });

    const res = { setHeader: jest.fn() };
    const result = setEnvironmentLockAccessCookie(res, environmentLockAccess);

    expect(res.setHeader).toHaveBeenCalledWith(
        'Set-Cookie',
        'environmentLockAccess=your-access-token; Max-Age=604800; Domain=localhost; Path=/; HttpOnly; Secure',
    );
    expect(result.props.message).toBe('Access Token has been set');
});

test('should return "Access Token invalid" if environmentLockAccess does not match envConfig', () => {
    const res = {};
    const environmentLockAccess = 'invalid-access-token';
    Object.defineProperty(envConfig, 'environmentLockAccess', { value: 'access-token' });

    const result = setEnvironmentLockAccessCookie(res, environmentLockAccess);

    expect(result.props.message).toBe('Access Token invalid');
});
