const parseCSV = (csvData: string) => {
    try {
        const lines = csvData.split('\n').filter((line) => line.trim() !== '');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const row = lines[i].split(',');
            const obj: any = {};

            for (let j = 0; j < headers.length; j++) {
                const key = header(headers[j]);

                if (key) {
                    obj[header(headers[j])] = row[j];
                }
            }

            data.push(obj);
        }

        return data;
    } catch (error) {
        console.error('CSV parse error', error);
    }
};

const header = (string: string) => {
    try {
        return JSON.parse(string) as string;
    } catch {
        return string;
    }
};

export { parseCSV };
