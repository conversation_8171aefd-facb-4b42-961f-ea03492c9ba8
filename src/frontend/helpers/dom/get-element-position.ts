const getElementPosition = (
    element: Element,
    anchor: 'center' | 'top-left' | 'top-right' | 'bottom-right' | 'bottom-left',
) => {
    const { top, left, width, height } = element.getBoundingClientRect();

    if (anchor === 'bottom-left') {
        return {
            x: left,
            y: top + height,
        };
    }

    return {
        x: left + width / 2,
        y: top + height / 2,
    };
};

export { getElementPosition };
