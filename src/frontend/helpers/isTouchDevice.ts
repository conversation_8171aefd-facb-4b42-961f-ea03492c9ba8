import { LocalStorageService } from 'services/LocalStorageService';

export const IS_TOUCH_LOCAL_STORAGE_KEY = 'isTouchDevice';

export enum TouchDeviceType {
    NOT_TOUCH_DEVICE = 'no-touch',
    TABLET = 'tablet',
    TV = 'tv',
}

const isTouchDevice = () => {
    return getTouchDeviceType() !== TouchDeviceType.NOT_TOUCH_DEVICE;
};

const getTouchDeviceType = () => {
    if (typeof window === 'undefined') {
        return TouchDeviceType.NOT_TOUCH_DEVICE;
    }

    const localStorageValue = LocalStorageService.get(IS_TOUCH_LOCAL_STORAGE_KEY);

    if (valueIsTouchDeviceType(localStorageValue)) {
        return localStorageValue;
    }

    if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        return TouchDeviceType.TABLET;
    }

    return TouchDeviceType.NOT_TOUCH_DEVICE;
};

const valueIsTouchDeviceType = (value: any): value is TouchDeviceType => {
    switch (value) {
        case TouchDeviceType.NOT_TOUCH_DEVICE:
        case TouchDeviceType.TABLET:
        case TouchDeviceType.TV:
            return true;

        default:
            return false;
    }
};

export { isTouchDevice, getTouchDeviceType };
