import { Project, User } from 'models';

const ProjectHelpers = {
    urls: {
        overview: () => '/projects',
        create: (
            options: {
                name?: string;
                isReferenceDesign?: boolean;
                manufacturer?: string;
                referenceComponent?: string;
            } = {
                name: '',
                isReferenceDesign: false,
                manufacturer: '',
                referenceComponent: '',
            },
        ) => {
            const { name = '', manufacturer = '', isReferenceDesign = false, referenceComponent = '' } = options;

            const query = [];

            if (name) {
                query.push(`name=${name}`);
            }

            if (isReferenceDesign) {
                query.push('isReferenceDesign=true');
            }

            if (manufacturer) {
                query.push(`manufacturer=${manufacturer}`);
            }

            if (referenceComponent) {
                query.push(`referenceComponent=${referenceComponent}`);
            }

            return `/projects/create${query.length ? `?${query.join('&')}` : ''}`;
        },
        edit: (projectId: string) => `/projects/${projectId}`,
        editor: (projectId: string, action?: 'publish') => {
            return action ? `/projects/${projectId}/editor?action=${action}` : `/projects/${projectId}/editor`;
        },
        duplicate: (projectId: string, name: string, isReferenceDesign: boolean) =>
            `/projects/create?startFromExistingProject=${projectId}&name=${name}&isReferenceDesign=${isReferenceDesign}`,
        requestAccess: (projectId: string) => `/projects/${projectId}/request-access`,
    },

    isProjectCollaborator: (project?: Project | null, user?: User | null) => {
        if (!project || !user) {
            return false;
        }

        return project.collaborators.users.some((collaborator) => {
            return collaborator.user === user.id;
        });
    },
} as const;

export { ProjectHelpers };
