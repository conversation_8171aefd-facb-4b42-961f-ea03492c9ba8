import { UserFeatureFlags, User } from 'models';
import { UserInvitationProps } from 'services/UserService';

const UserHelpers = {
    swr: {
        progress: () => '/users/me/get-progress',
        userInvitations: (props: UserInvitationProps) => `/userInvitations?query=${JSON.stringify(props)}`,
    },

    localStorageKey: {
        referrer: 'userReferrer',
        type: 'userType',
        email: 'userEmail',
        name: 'userName',
        event: 'userEvent',
    },

    checkUserFlag: (user: User | null, flag: UserFeatureFlags) => {
        return user?.flags ? user?.flags.includes(flag) : false;
    },
};

export { UserHelpers };
