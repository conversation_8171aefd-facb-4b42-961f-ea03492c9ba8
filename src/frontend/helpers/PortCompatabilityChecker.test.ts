import { PortCompatabilityChecker } from './PortCompatabilityChecker';
import { Converter } from 'models';
import { expect, test } from '@jest/globals';

type Port = Converter['electrical']['ports'][number];

test('it can be created', () => {
    const dcChecker = new PortCompatabilityChecker(getDcPortSpec());
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    expect(dcChecker).toBeInstanceOf(PortCompatabilityChecker);
    expect(acChecker).toBeInstanceOf(PortCompatabilityChecker);
});

test('it should check voltage type', () => {
    const dcChecker = new PortCompatabilityChecker(getDcPortSpec());
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    const dcResult = dcChecker.check(getDcPortSpec());
    expect(dcResult.voltageTypeMatch).toBe('DC');

    const acResult = acChecker.check(getAcPortSpec());
    expect(acResult.voltageTypeMatch).toBe('AC');

    const dcErrorResult = dcChecker.check(getAcPortSpec());
    expect(dcErrorResult.voltageTypeMatch).toBe(false);

    const acErrorResult = acChecker.check(getDcPortSpec());
    expect(acErrorResult.voltageTypeMatch).toBe(false);
});

test('it should check voltage limits', () => {
    // DC
    const dcChecker = new PortCompatabilityChecker(getDcPortSpec());

    const dcVoltageTooHigh = dcChecker.check(getDcPortSpec({ voltage: { unit: 'V', min: 400, nom: 500, max: 600 } }));
    expect(dcVoltageTooHigh.voltageValueInRange).toBe(false);

    const dcVoltageTooLow = dcChecker.check(getDcPortSpec({ voltage: { unit: 'V', min: 50, nom: 100, max: 200 } }));
    expect(dcVoltageTooLow.voltageValueInRange).toBe(false);

    const dcVoltageInRange = dcChecker.check(getDcPortSpec({ voltage: { unit: 'V', min: 120, nom: 500, max: 550 } }));
    expect(dcVoltageInRange.voltageValueInRange).toBe(true);

    // AC
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    const acVoltageTooHigh = acChecker.check(getAcPortSpec({ voltage: { unit: 'V', min: 100, nom: 120, max: 200 } }));
    expect(acVoltageTooHigh.voltageValueInRange).toBe(false);

    const acVoltageTooLow = acChecker.check(getAcPortSpec({ voltage: { unit: 'V', min: 50, nom: 110, max: 120 } }));
    expect(acVoltageTooLow.voltageValueInRange).toBe(false);

    const acVoltageInRange = acChecker.check(getAcPortSpec({ voltage: { unit: 'V', min: 110, nom: 120, max: 145 } }));
    expect(acVoltageInRange.voltageValueInRange).toBe(true);
});

test('it should check current limits', () => {
    // DC
    const dcChecker = new PortCompatabilityChecker(getDcPortSpec());

    const dcCurrentTooHigh = dcChecker.check(getDcPortSpec({ current: { unit: 'A', nom: 7, max: 20 } }));
    expect(dcCurrentTooHigh.currentInRange).toBe(false);

    const dcCurrentInRange = dcChecker.check(getDcPortSpec({ current: { unit: 'A', nom: 6, max: 10 } }));
    expect(dcCurrentInRange.currentInRange).toBe(true);

    // AC
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    const acCurrentTooHigh = acChecker.check(getAcPortSpec({ current: { unit: 'A', nom: 40, max: 60 } }));
    expect(acCurrentTooHigh.currentInRange).toBe(false);

    const acCurrentInRange = acChecker.check(getAcPortSpec({ current: { unit: 'A', nom: 40, max: 45 } }));
    expect(acCurrentInRange.currentInRange).toBe(true);
});

test('it should check power limits', () => {
    // DC
    const dcChecker = new PortCompatabilityChecker(getDcPortSpec());

    const dcPowerTooHigh = dcChecker.check(getDcPortSpec({ power: { unit: 'W', nom: 5000, max: 7000 } }));
    expect(dcPowerTooHigh.powerInRange).toBe(false);

    const dcPowerInRange = dcChecker.check(getDcPortSpec({ power: { unit: 'W', nom: 4500, max: 6000 } }));
    expect(dcPowerInRange.powerInRange).toBe(true);

    // AC
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    const acPowerTooHigh = acChecker.check(getAcPortSpec({ power: { unit: 'W', nom: 1500, max: 2500 } }));
    expect(acPowerTooHigh.powerInRange).toBe(false);

    const acPowerInRange = acChecker.check(getAcPortSpec({ power: { unit: 'W', nom: 1200, max: 1800 } }));
    expect(acPowerInRange.powerInRange).toBe(true);
});

test('it should check frequency limits', () => {
    const acChecker = new PortCompatabilityChecker(getAcPortSpec());

    const acFrequencyTooHigh = acChecker.check(getAcPortSpec({ frequency: { unit: 'Hz', min: 60, nom: 65, max: 70 } }));
    expect(acFrequencyTooHigh.frequencyInRange).toEqual(false);

    const acFrequencyTooLow = acChecker.check(getAcPortSpec({ frequency: { unit: 'Hz', min: 40, nom: 50, max: 60 } }));
    expect(acFrequencyTooLow.frequencyInRange).toEqual(false);

    const acFrequencyInRange = acChecker.check(getAcPortSpec({ frequency: { unit: 'Hz', min: 58, nom: 60, max: 62 } }));
    expect(acFrequencyInRange.frequencyInRange).toEqual(true);
});

test('it should check power flow direction', () => {
    const inputChecker = new PortCompatabilityChecker(getDcPortSpec({}, 'input'));

    const inputOnInput = inputChecker.check(getDcPortSpec({}, 'input'));
    expect(inputOnInput.powerFlowDirectionMatch).toBe('input');

    const outputOnInput = inputChecker.check(getDcPortSpec({}, 'output'));
    expect(outputOnInput.powerFlowDirectionMatch).toBe(false);

    const bidirectionalOnInput = inputChecker.check(getDcPortSpec({}, 'bidirectional'));
    expect(bidirectionalOnInput.powerFlowDirectionMatch).toBe('input');

    const outputChecker = new PortCompatabilityChecker(getAcPortSpec({}, 'output'));

    const outputOnOutput = outputChecker.check(getAcPortSpec({}, 'output'));
    expect(outputOnOutput.powerFlowDirectionMatch).toBe('output');

    const inputOnOutput = outputChecker.check(getAcPortSpec({}, 'input'));
    expect(inputOnOutput.powerFlowDirectionMatch).toBe(false);

    const bidirectionalOnOutput = outputChecker.check(getAcPortSpec({}, 'bidirectional'));
    expect(bidirectionalOnOutput.powerFlowDirectionMatch).toBe('output');

    const bidirectionalChecker = new PortCompatabilityChecker(getDcPortSpec({}, 'bidirectional'));

    expect(bidirectionalChecker.check(getDcPortSpec({}, 'input')).powerFlowDirectionMatch).toEqual('input');
    expect(bidirectionalChecker.check(getDcPortSpec({}, 'output')).powerFlowDirectionMatch).toEqual('output');
    expect(bidirectionalChecker.check(getDcPortSpec({}, 'bidirectional')).powerFlowDirectionMatch).toEqual(
        'bidirectional',
    );
});

const getDcPortSpec = (
    additionalConfiguration = {} as Partial<Port['DC']>,
    powerFlowDirection = 'input' as Port['powerFlowDirection'],
): Port => ({
    AC: {
        enabled: false,
        voltage: {
            unit: 'V',
            min: null,
            nom: null,
            max: null,
        },
        current: {
            unit: 'A',
            nom: null,
            max: null,
        },
        power: {
            unit: 'W',
            nom: null,
            max: null,
        },
        frequency: {
            unit: 'Hz',
            min: null,
            nom: null,
            max: null,
        },
        powerFactor: 0,
        controlMethods: [],
        earthingConfigurations: [],
        configuration: null,
    },
    DC: {
        enabled: true,
        voltage: {
            unit: 'V',
            min: 100,
            nom: 350,
            max: 585,
        },
        current: {
            unit: 'A',
            nom: 5,
            max: 11,
        },
        power: {
            unit: 'W',
            nom: 4000,
            max: 6435,
        },
        configuration: 'unipolar',
        controlMethods: ['constant-voltage'],
        earthingConfigurations: [],
        ...additionalConfiguration,
    },
    powerFlowDirection,
    isolated: true,
    parallelableCapacity: 1,
    capacitance: {
        unit: 'F',
        value: null,
    },
    wireSize: {
        min: '10',
        max: '16',
    },
    features: [],
    terminal: {
        type: null,
        temperature: {
            unit: 'K',
            min: null,
            max: null,
        },
        torque: {
            unit: 'N m',
            nom: null,
            max: null,
        },
    },
    purpose: null,
});

const getAcPortSpec = (
    additionalConfiguration = {} as Partial<Port['AC']>,
    powerFlowDirection = 'output' as Port['powerFlowDirection'],
): Port => ({
    AC: {
        enabled: true,
        voltage: {
            unit: 'V',
            min: 100,
            nom: 120,
            max: 150,
        },
        current: {
            unit: 'A',
            nom: 30,
            max: 50,
        },
        power: {
            unit: 'W',
            nom: 1000,
            max: 2000,
        },
        frequency: {
            unit: 'Hz',
            min: 55,
            nom: 60,
            max: 65,
        },
        powerFactor: 0,
        controlMethods: [],
        configuration: null,
        earthingConfigurations: [],
        ...additionalConfiguration,
    },
    DC: {
        enabled: false,
        voltage: {
            unit: 'V',
            min: null,
            nom: null,
            max: null,
        },
        current: {
            unit: 'A',
            nom: null,
            max: null,
        },
        power: {
            unit: 'W',
            nom: null,
            max: null,
        },
        controlMethods: [],
        configuration: null,
        earthingConfigurations: [],
    },
    powerFlowDirection,
    isolated: false,
    parallelableCapacity: 1,
    capacitance: {
        unit: 'F',
        value: null,
    },
    wireSize: {
        min: '10',
        max: '16',
    },
    features: [],
    terminal: {
        type: null,
        temperature: {
            unit: 'K',
            min: null,
            max: null,
        },
        torque: {
            unit: 'N m',
            nom: null,
            max: null,
        },
    },
    purpose: null,
});
