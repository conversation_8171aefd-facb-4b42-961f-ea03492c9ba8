const TextHelpers = {
    getTextWithEllipsis: (text: string, maxLength: number, splitOnSpace?: boolean) => {
        if (splitOnSpace) {
            const words = text.split(' ');
            let result = '';

            for (const word of words) {
                if (result.length + word.length > maxLength) {
                    return `${result}...`;
                }
                result = `${result} ${word}`;
            }

            return result;
        }

        return `${text.substring(0, maxLength)}${text.length > maxLength ? '...' : ''}`;
    },

    textMatches: (text: string, query: string, exact?: boolean) => {
        if (exact) {
            return text.trim().toLowerCase() === query.trim().toLowerCase();
        }

        return (
            text.trim().toLowerCase().includes(query.trim().toLowerCase()) ||
            query.trim().toLowerCase().includes(text.trim().toLowerCase())
        );
    },

    sort: (input: string[]) => input.sort((a, b) => a.localeCompare(b)),
};

export { TextHelpers };
