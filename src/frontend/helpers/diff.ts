import { crush, keys } from 'radash';

const diff = (source: object, next: object) => {
    const crushedSource: { [key: string]: any } = crush(source);
    const crushedNext: { [key: string]: any } = crush(next);

    const updates: { [key: string]: any } = {};
    const deletes: { [key: string]: any } = {};

    [...keys(crushedSource), ...keys(crushedNext)].forEach((key) => {
        if (crushedSource[key] !== crushedNext[key]) {
            if (crushedNext[key] === undefined) {
                deletes[key] = crushedSource[key];
            } else {
                updates[key] = crushedNext[key];
            }
        }
    });

    return {
        hasUpdates: keys(updates).length > 0,
        hasDeletes: keys(deletes).length > 0,
        updates,
        deletes,
    };
};

export { diff };
