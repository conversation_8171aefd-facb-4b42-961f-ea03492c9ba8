import { gzip, gunzip, strToU8, strFromU8 } from 'fflate';

const CompressionHelpers = {
    calculateStringSize: (string: string) => {
        return string.length;
    },

    compress: async (string: string) => {
        return new Promise<number[]>((resolve) => {
            const u8 = strToU8(string);

            gzip(u8, (_, compressed) => {
                resolve(Array.from(compressed));
            });
        });
    },

    decompress: (array: number[]) => {
        return new Promise<string>((resolve) => {
            const u8 = Uint8Array.from(array);

            gunzip(u8, (_, decompressed) => {
                const string = strFromU8(decompressed);

                resolve(string);
            });
        });
    },
};

export { CompressionHelpers };
