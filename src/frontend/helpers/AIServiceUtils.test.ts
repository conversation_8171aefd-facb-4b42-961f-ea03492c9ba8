import { expect, test } from '@jest/globals';
import { AIConversationMessage, ComponentChatMessage, AIConversationRole } from 'models';
import { AIServiceUtils } from './AIServiceUtils';

describe('convertAIMessagesToAIConversationMessages', () => {
    test('should convert component chat messages to AI conversation messages', () => {
        const history: ComponentChatMessage[] = [
            { question: 'Question 1', answer: 'Answer 1' },
            { question: 'Question 2', answer: 'Answer 2' },
        ];

        const expected: AIConversationMessage[] = [
            { role: AIConversationRole.USER, content: 'Question 1' },
            { role: AIConversationRole.ASSISTANT, content: 'Answer 1' },
            { role: AIConversationRole.USER, content: 'Question 2' },
            { role: AIConversationRole.ASSISTANT, content: 'Answer 2' },
        ];

        const result = AIServiceUtils.convertAIMessagesToAIConversationMessages(history);

        expect(result).toEqual(expected);
    });

    test('should return an empty array if history is undefined', () => {
        const history = undefined;
        const result = AIServiceUtils.convertAIMessagesToAIConversationMessages(history);

        expect(result).toEqual([]);
    });
});
