import { IntercomChannelWithLatestMessage } from 'models';
import { SupportChannel } from 'services/SupportCenterService';

const SupportCenterHelpers = {
    urls: {
        overview: (companySlug?: string) => {
            return companySlug ? `/support-center#${companySlug}` : '/support-center';
        },
    },

    getUnreadCount: (supportChannel: SupportChannel) => {
        const getChannelNbUnread = (intercomChannel: IntercomChannelWithLatestMessage[]) =>
            intercomChannel.filter((channel) => !channel.viewed).length;

        return (
            getChannelNbUnread(supportChannel.chat) +
            getChannelNbUnread(supportChannel.intercom.project) +
            getChannelNbUnread(supportChannel.intercom.component) +
            getChannelNbUnread(supportChannel.intercom.company)
        );
    },

    hasSupportMessages: (supportChannel: SupportChannel) => {
        return (
            supportChannel.chat.length > 0 ||
            supportChannel.intercom.project.length > 0 ||
            supportChannel.intercom.component.length > 0 ||
            supportChannel.intercom.company.length > 0
        );
    },
};

export { SupportCenterHelpers };
