import type { GetServerSidePropsContext } from 'next';
import type { NextRequest } from 'next/server';

const isAuthenticatedRequest = (req: GetServerSidePropsContext['req']): boolean => {
    const cookie = req.cookies['dcide-jwt'];

    return authenticateCookie(cookie);
};

const isAuthenticatedNextRequest = (req: NextRequest): boolean => {
    const cookie = req.cookies.get('dcide-jwt')?.value;

    return authenticateCookie(cookie);
};

const authenticateCookie = (cookieValue: string | undefined): boolean => {
    if (!cookieValue) {
        return false;
    }

    return true;

    // TODO: Do we want to verify the JWT here?
};

export { isAuthenticatedRequest, isAuthenticatedNextRequest };
