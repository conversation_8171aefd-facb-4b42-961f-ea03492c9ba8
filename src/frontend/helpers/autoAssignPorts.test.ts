import { ComponentVisibility, DiagramComponentInstance, PowerFlowDirection, VoltageType } from 'models';
import { Converter } from 'models';
import { getPermutations, autoAssignPorts } from './autoAssignPorts';

import { expect, test } from '@jest/globals';

test('getPermutations function', () => {
    expect(getPermutations(2, ['A', 'B'])).toEqual([
        [
            [0, 'A'],
            [1, 'B'],
        ],
        [
            [0, 'B'],
            [1, 'A'],
        ],
    ]);

    expect(getPermutations(3, ['A', 'B', 'C'])).toEqual([
        [
            [0, 'A'],
            [1, 'B'],
            [2, 'C'],
        ],
        [
            [0, 'A'],
            [1, 'C'],
            [2, 'B'],
        ],
        [
            [0, 'B'],
            [1, 'A'],
            [2, 'C'],
        ],
        [
            [0, 'B'],
            [1, 'C'],
            [2, 'A'],
        ],
        [
            [0, 'C'],
            [1, 'A'],
            [2, 'B'],
        ],
        [
            [0, 'C'],
            [1, 'B'],
            [2, 'A'],
        ],
    ]);
});

test('it should assign matching ports', () => {
    const componentInstance = getComponentInstance([
        {
            specifications: COMPONENT_INSTANCE_DC_BATTERY_PORT,
            voltageType: 'DC',
            powerFlowDirection: 'bidirectional',
        },
        {
            specifications: COMPONENT_INSTANCE_DC_LIGHT_PORT,
            voltageType: 'DC',
            powerFlowDirection: 'output',
        },
        {
            specifications: COMPONENT_INSTANCE_AC_UTILITY_PORT,
            voltageType: 'AC',
            powerFlowDirection: 'bidirectional',
        },
    ]);

    const productCatalogComponent = getProductCatalogComponent([
        PRODUCT_CATALOG_COMPONENT_AC_PORT,
        PRODUCT_CATALOG_COMPONENT_HIGH_VOLTAGE_DC_PORT,
        PRODUCT_CATALOG_COMPONENT_LOW_VOLTAGE_DC_PORT,
    ]);

    const result = autoAssignPorts({
        componentInstance,
        productCatalogComponent,
    });

    expect(result[0].componentInstancePort).toEqual(0);
    expect(result[0].productCatalogComponentPort).toEqual(1);

    expect(result[1].componentInstancePort).toEqual(1);
    expect(result[1].productCatalogComponentPort).toEqual(2);

    expect(result[2].componentInstancePort).toEqual(2);
    expect(result[2].productCatalogComponentPort).toEqual(0);
});

test('it should assign matching ports for a three port component', () => {
    const componentInstance = getThreeportComponentInstance();

    const productCatalogComponent = getThreeportProductCatalogComponent();

    const result = autoAssignPorts({
        // @ts-ignore
        componentInstance,
        // @ts-ignore
        productCatalogComponent,
    });

    expect(result[0].componentInstancePort).toEqual(0);
    expect(result[0].productCatalogComponentPort).toEqual(1);

    expect(result[1].componentInstancePort).toEqual(1);
    expect(result[1].productCatalogComponentPort).toEqual(2);

    expect(result[2].componentInstancePort).toEqual(2);
    expect(result[2].productCatalogComponentPort).toEqual(0);
});

type ConverterPort = Converter['electrical']['ports'][number];

const COMPONENT_INSTANCE_AC_UTILITY_PORT: ConverterPort = {
    features: [],
    powerFlowDirection: 'input',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    wireSize: {
        min: null,
        max: null,
    },
    purpose: null,
    AC: {
        enabled: true,
        voltage: {
            min: 350,
            nom: 450,
            max: 500,
            unit: 'V',
        },
        current: {
            nom: 40,
            max: 42,
            unit: 'A',
        },
        power: {
            nom: 30000,
            max: 30000,
            unit: 'W',
        },
        frequency: {
            min: 50,
            nom: 60,
            max: 60,
            unit: 'Hz',
        },
        powerFactor: 1,
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    DC: {
        enabled: true,
        voltage: {
            min: 100,
            nom: 120,
            max: 140,
            unit: 'V',
        },
        current: {
            nom: 60,
            max: 70,
            unit: 'A',
        },
        power: {
            nom: 1000,
            max: 2000,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: null,
    parallelableCapacity: 1,
};

const COMPONENT_INSTANCE_DC_BATTERY_PORT: ConverterPort = {
    features: [],
    powerFlowDirection: 'bidirectional',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    wireSize: {
        min: null,
        max: null,
    },
    purpose: null,
    AC: {
        enabled: true,
        voltage: {
            min: 200,
            nom: 400,
            max: 600,
            unit: 'V',
        },
        current: {
            nom: 20,
            max: 50,
            unit: 'A',
        },
        power: {
            nom: 1200,
            max: 2400,
            unit: 'W',
        },
        frequency: {
            min: null,
            nom: null,
            max: null,
            unit: 'Hz',
        },
        powerFactor: 1,
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    DC: {
        enabled: true,
        voltage: {
            min: 200,
            nom: 400,
            max: 600,
            unit: 'V',
        },
        current: {
            nom: 20,
            max: 50,
            unit: 'A',
        },
        power: {
            nom: 12000,
            max: 24000,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: null,
    parallelableCapacity: 1,
};

const COMPONENT_INSTANCE_DC_LIGHT_PORT: ConverterPort = {
    features: [],
    powerFlowDirection: 'bidirectional',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    purpose: null,
    wireSize: {
        min: null,
        max: null,
    },
    AC: {
        enabled: true,
        voltage: {
            min: 10,
            nom: 20,
            max: 30,
            unit: 'V',
        },
        current: {
            nom: 10,
            max: 15,
            unit: 'A',
        },
        power: {
            nom: 2000,
            max: 2000,
            unit: 'W',
        },
        frequency: {
            min: null,
            nom: null,
            max: null,
            unit: 'Hz',
        },
        powerFactor: 1,
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    DC: {
        enabled: true,
        voltage: {
            min: 10,
            nom: 20,
            max: 30,
            unit: 'V',
        },
        current: {
            nom: 10,
            max: 15,
            unit: 'A',
        },
        power: {
            nom: 2000,
            max: 2000,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: null,
    parallelableCapacity: 1,
};

const getComponentInstance = (
    ports: { specifications: ConverterPort; voltageType: VoltageType; powerFlowDirection: PowerFlowDirection }[],
): DiagramComponentInstance => ({
    id: 'component-XciYflJH',
    position: {
        x: 24,
        y: 23,
    },
    componentType: 'converter',
    componentId: '',
    label: '',
    notes: '',
    indicator: 1,
    designator: 'U-1',
    liveDataIdentifier: '',
    colSpan: 1,
    rowSpan: 1,
    multiple: {
        type: 'parallel',
        amount: 1,
    },
    specifications: {
        electrical: {
            ports: Object.values(ports).map(({ specifications }) => specifications),
            standards: [],
            isolationVoltage: {
                value: null,
                unit: 'V',
            },
        },
    },
    configuration: {
        stateOfCharge: null,
        ports: ports.map(({ powerFlowDirection, voltageType }) => ({
            controlMethod: null,
            voltage: {
                value: null,
                unit: 'V',
            },
            current: {
                value: null,
                unit: 'A',
            },
            power: {
                value: null,
                unit: 'W',
            },
            droopResistance: {
                value: null,
                unit: 'ohm',
            },
            droopCurrent: {
                value: null,
                unit: 'A',
            },
            voltageSeries: null,
            powerSeries: null,
            label: '',
            powerFlowDirection,
            voltageType,
        })),
    },
    manufacturer: '',
    partNumber: '',
    labelPlacement: {
        edge: 'right',
        offset: 0,
        auto: true,
    },
});

const PRODUCT_CATALOG_COMPONENT_AC_PORT: ConverterPort = {
    features: ['voltage-measurement', 'current-measurement', 'power-measurement'],
    powerFlowDirection: 'bidirectional',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    wireSize: {
        min: null,
        max: null,
    },
    purpose: null,
    AC: {
        enabled: true,
        voltage: {
            min: 334,
            nom: 480,
            max: 528,
            unit: 'V',
        },
        current: {
            nom: 37,
            max: 44,
            unit: 'A',
        },
        power: {
            nom: 30000,
            max: 30000,
            unit: 'W',
        },
        frequency: {
            min: 50,
            nom: 60,
            max: 60,
            unit: 'Hz',
        },
        powerFactor: 0.75,
        configuration: 'three-phase-delta',
        earthingConfigurations: [],
        controlMethods: ['constant-power'],
    },
    DC: {
        enabled: false,
        voltage: {
            min: null,
            nom: null,
            max: null,
            unit: 'V',
        },
        current: {
            nom: null,
            max: null,
            unit: 'A',
        },
        power: {
            nom: null,
            max: null,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: true,
    parallelableCapacity: 8,
};

const PRODUCT_CATALOG_COMPONENT_LOW_VOLTAGE_DC_PORT: ConverterPort = {
    features: [],
    powerFlowDirection: 'bidirectional',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    wireSize: {
        min: null,
        max: null,
    },
    purpose: null,
    AC: {
        enabled: false,
        voltage: {
            min: null,
            nom: null,
            max: null,
            unit: 'V',
        },
        current: {
            nom: null,
            max: null,
            unit: 'A',
        },
        power: {
            nom: null,
            max: null,
            unit: 'W',
        },
        frequency: {
            min: null,
            nom: null,
            max: null,
            unit: 'Hz',
        },
        powerFactor: 0,
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    DC: {
        enabled: true,
        voltage: {
            min: 2,
            nom: 20,
            max: 50,
            unit: 'V',
        },
        current: {
            nom: 10,
            max: 20,
            unit: 'A',
        },
        power: {
            nom: 3000,
            max: 3000,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: ['constant-voltage', 'constant-current', 'constant-power'],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: false,
    parallelableCapacity: 1,
};

const PRODUCT_CATALOG_COMPONENT_HIGH_VOLTAGE_DC_PORT: ConverterPort = {
    features: [],
    powerFlowDirection: 'bidirectional',
    terminal: {
        temperature: {
            min: null,
            max: null,
            unit: 'K',
        },
        type: null,
        torque: {
            nom: null,
            max: null,
            unit: 'N m',
        },
    },
    wireSize: {
        min: null,
        max: null,
    },
    purpose: null,
    AC: {
        enabled: false,
        voltage: {
            min: null,
            nom: null,
            max: null,
            unit: 'V',
        },
        current: {
            nom: null,
            max: null,
            unit: 'A',
        },
        power: {
            nom: null,
            max: null,
            unit: 'W',
        },
        frequency: {
            min: null,
            nom: null,
            max: null,
            unit: 'Hz',
        },
        powerFactor: 0,
        configuration: null,
        earthingConfigurations: [],
        controlMethods: [],
    },
    DC: {
        enabled: true,
        voltage: {
            min: 100,
            nom: null,
            max: 1000,
            unit: 'V',
        },
        current: {
            nom: 60,
            max: 60,
            unit: 'A',
        },
        power: {
            nom: 30000,
            max: 30000,
            unit: 'W',
        },
        configuration: null,
        earthingConfigurations: [],
        controlMethods: ['constant-voltage', 'constant-current', 'constant-power'],
    },
    capacitance: {
        value: null,
        unit: 'F',
    },
    isolated: false,
    parallelableCapacity: 1,
};

const getProductCatalogComponent = (ports: ConverterPort[]): Converter => ({
    id: '64cb803c8e16bcfbdf2116a1',
    team: '64cb803c8e16bcf1216a1fbd',
    name: 'Stabiliti 30C3',
    description:
        'Stabiliti 30C3 is an innovative multiport power converter with 3 bidirectional ports (AC/DC/DC) to easily interconnect power sources (grid, PV and batteries) and loads (AC or DC). Using a single multiport converter offers much easier systems integration, control and ongoing maintenance by simplifying system level hardware and software design. The enclosure is IP54 and can therefore be installed indoors or outdoors.\nBattery life is extended through galvanic isolation (protection against grid disturbances) and built-in smart algorithms to minimize battery stress. The wide DC voltage range allows the use of multiple storage technologies such as lead-acid, lithium-ion, super-caps, aqueous batteries, flow batteries or flywheels. The galvanic isolation also maximizes PV production over the long-term, by reducing PID (Potential-Induced Degradation).',
    type: 'converter',
    manufacturer: '646e08a51b6f81686af02188',
    teamManufacturer: null,
    distributorsDetails: [],
    distributors: [],
    productSeries: 'Stabiliti',
    productIdentifier: '30C3',
    msrp: null,
    leadTime: null,
    website: 'https://www.cet-america.com/en/product/stabiliti-30c3-ul/',
    lifecycle: {
        release: null,
        endOfLife: null,
    },
    compatibleWith: [],
    compatibleWithCrossReference: [],
    compatibleWithPlaceholders: [],
    electrical: {
        ports,
        standards: [],
        isolationVoltage: {
            value: null,
            unit: 'V',
        },
    },
    communication: {
        interfaces: ['ethernet', 'rs485'],
        protocols: ['modbus-tcp-ip', 'modbus-rtu'],
    },
    environmental: {
        operatingTemperature: {
            min: 248.15,
            max: 333.15,
            unit: 'K',
        },
        storageTemperature: {
            min: 233.15,
            max: 358.15,
            unit: 'K',
        },
        operatingHumidity: {
            min: 0,
            max: 100,
            unit: '%',
        },
        storageHumidity: {
            min: null,
            max: null,
            unit: '%',
        },
        ingressProtection_IP: 'IP54',
        ingressProtection_NEMA: '3R',
        maximumOperatingAltitude: {
            value: null,
            unit: 'm',
        },
        coolingMethod: 'forced-air',
    },
    performance: {
        standbyPower: {
            value: null,
            unit: 'W',
        },
        efficiency: {
            nom: 95,
            max: 95.5,
            unit: '%',
        },
        losses: {
            nom: null,
            max: 1500,
            unit: 'W',
        },
    },
    mechanical: {
        dimensions: {
            width: 0.52,
            length: 1.016,
            height: 0.406,
            unit: 'm',
        },
        weight: {
            value: 61,
            unit: 'kg',
        },
        mountingType: ['wall'],
    },
    compliance: {
        CE: false,
        UL: true,
        currentOS: false,
        emergeAlliance: false,
        ODCA: false,
        other: true,
    },
    application: [],
    regionAvailability: [],
    standards: ['64d38d6aea34f6c51834f8af', '64d38d80ea34f6c51834f8b9', '64d0e0dacb456658b1535384'],
    questions: [],
    archivedAt: null,
    deletedAt: null,
    createdBy: '64c9dbed3f4dd5160d67162a',
    specificationsSummary: '',
    metadata: {},
    files: [],
    images: [],
    updatedBy: null,
    visibility: ComponentVisibility.PUBLIC,
    reviewed: false,
    publishedAt: null,
    publishedBy: null,
});

const getThreeportComponentInstance = () => ({
    id: 'component-HCfujImy',
    indicator: 3,
    designator: 'U-3',
    liveDataIdentifier: '',
    position: {
        x: 34,
        y: 36,
    },
    colSpan: 1,
    rowSpan: 1,
    componentType: 'converter',
    componentId: '',
    multiple: {
        type: 'parallel',
        amount: 1,
    },
    label: '',
    labelPlacement: {
        edge: null,
        offset: 0,
        auto: true,
    },
    notes: '',
    specifications: {
        electrical: {
            ports: [
                {
                    features: [],
                    powerFlowDirection: 'bidirectional',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
                {
                    features: [],
                    powerFlowDirection: 'bidirectional',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
                {
                    features: [],
                    powerFlowDirection: 'bidirectional',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
            ],
            standards: [],
            isolationVoltage: {
                value: null,
                unit: 'V',
            },
        },
    },
    manufacturer: null,
    partNumber: null,
    configuration: {
        ports: [
            {
                label: '',
                voltageType: 'DC',
                powerFlowDirection: 'bidirectional',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
            {
                label: '',
                voltageType: 'DC',
                powerFlowDirection: 'bidirectional',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
            {
                label: '',
                voltageType: 'AC',
                powerFlowDirection: 'bidirectional',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
        ],
        stateOfCharge: null,
    },
});

const getThreeportProductCatalogComponent = () => ({
    id: '64cb803c8e16bcfbdf2116a1',
    name: 'Stabiliti 30C3',
    description:
        'Stabiliti 30C3 is an innovative multiport power converter with 3 bidirectional ports (AC/DC/DC) to easily interconnect power sources (grid, PV and batteries) and loads (AC or DC). Using a single multiport converter offers much easier systems integration, control and ongoing maintenance by simplifying system level hardware and software design. The enclosure is IP54 and can therefore be installed indoors or outdoors.\nBattery life is extended through galvanic isolation (protection against grid disturbances) and built-in smart algorithms to minimize battery stress. The wide DC voltage range allows the use of multiple storage technologies such as lead-acid, lithium-ion, super-caps, aqueous batteries, flow batteries or flywheels. The galvanic isolation also maximizes PV production over the long-term, by reducing PID (Potential-Induced Degradation).',
    type: 'converter',
    manufacturer: '646e08a51b6f81686af02188',
    productSeries: 'Stabiliti',
    productIdentifier: '30C3',
    msrp: null,
    leadTime: null,
    website: 'https://www.cet-america.com/en/product/stabiliti-30c3-ul/',
    lifecycle: {
        release: null,
        endOfLife: null,
    },
    electrical: {
        ports: [
            {
                features: ['voltage-measurement', 'current-measurement', 'power-measurement'],
                powerFlowDirection: 'bidirectional',
                terminal: {
                    temperature: {
                        min: null,
                        max: null,
                        unit: 'K',
                    },
                    type: null,
                    torque: {
                        nom: null,
                        max: null,
                        unit: 'N m',
                    },
                },
                wireSize: {
                    min: null,
                    max: null,
                },
                AC: {
                    enabled: true,
                    voltage: {
                        min: 334,
                        nom: 480,
                        max: 528,
                        unit: 'V',
                    },
                    current: {
                        nom: 37,
                        max: 44,
                        unit: 'A',
                    },
                    power: {
                        nom: 30000,
                        max: 30000,
                        unit: 'W',
                    },
                    frequency: {
                        min: 50,
                        nom: 60,
                        max: 60,
                        unit: 'Hz',
                    },
                    powerFactor: 0.75,
                    configuration: 'three-phase-delta',
                    controlMethods: ['constant-power'],
                },
                DC: {
                    enabled: false,
                    voltage: {
                        min: null,
                        nom: null,
                        max: null,
                        unit: 'V',
                    },
                    current: {
                        nom: null,
                        max: null,
                        unit: 'A',
                    },
                    power: {
                        nom: null,
                        max: null,
                        unit: 'W',
                    },
                    configuration: null,
                    controlMethods: [],
                },
                capacitance: {
                    value: null,
                    unit: 'F',
                },
                isolated: true,
                parallelableCapacity: 8,
            },
            {
                features: ['residual-current-detection'],
                powerFlowDirection: 'bidirectional',
                terminal: {
                    temperature: {
                        min: null,
                        max: null,
                        unit: 'K',
                    },
                    type: null,
                    torque: {
                        nom: null,
                        max: null,
                        unit: 'N m',
                    },
                },
                wireSize: {
                    min: null,
                    max: null,
                },
                AC: {
                    enabled: false,
                    voltage: {
                        min: null,
                        nom: null,
                        max: null,
                        unit: 'V',
                    },
                    current: {
                        nom: null,
                        max: null,
                        unit: 'A',
                    },
                    power: {
                        nom: null,
                        max: null,
                        unit: 'W',
                    },
                    frequency: {
                        min: null,
                        nom: null,
                        max: null,
                        unit: 'Hz',
                    },
                    powerFactor: 0,
                    configuration: null,
                    controlMethods: [],
                },
                DC: {
                    enabled: true,
                    voltage: {
                        min: 100,
                        nom: null,
                        max: 1000,
                        unit: 'V',
                    },
                    current: {
                        nom: 60,
                        max: 60,
                        unit: 'A',
                    },
                    power: {
                        nom: 30000,
                        max: 30000,
                        unit: 'W',
                    },
                    configuration: null,
                    controlMethods: ['constant-voltage', 'constant-current', 'constant-power'],
                },
                capacitance: {
                    value: null,
                    unit: 'F',
                },
                isolated: false,
                parallelableCapacity: 1,
            },
            {
                features: ['residual-current-detection'],
                powerFlowDirection: 'bidirectional',
                terminal: {
                    temperature: {
                        min: null,
                        max: null,
                        unit: 'K',
                    },
                    type: null,
                    torque: {
                        nom: null,
                        max: null,
                        unit: 'N m',
                    },
                },
                wireSize: {
                    min: null,
                    max: null,
                },
                AC: {
                    enabled: false,
                    voltage: {
                        min: null,
                        nom: null,
                        max: null,
                        unit: 'V',
                    },
                    current: {
                        nom: null,
                        max: null,
                        unit: 'A',
                    },
                    power: {
                        nom: null,
                        max: null,
                        unit: 'W',
                    },
                    frequency: {
                        min: null,
                        nom: null,
                        max: null,
                        unit: 'Hz',
                    },
                    powerFactor: 0,
                    configuration: null,
                    controlMethods: [],
                },
                DC: {
                    enabled: true,
                    voltage: {
                        min: 100,
                        nom: null,
                        max: 1000,
                        unit: 'V',
                    },
                    current: {
                        nom: 60,
                        max: 60,
                        unit: 'A',
                    },
                    power: {
                        nom: 30000,
                        max: 30000,
                        unit: 'W',
                    },
                    configuration: null,
                    controlMethods: ['constant-power', 'constant-current', 'constant-voltage'],
                },
                capacitance: {
                    value: null,
                    unit: 'F',
                },
                isolated: false,
                parallelableCapacity: 1,
            },
        ],
        standards: [],
        isolationVoltage: {
            value: null,
            unit: 'V',
        },
    },
    communication: {
        interfaces: ['ethernet', 'rs485'],
        protocols: ['modbus-tcp-ip', 'modbus-rtu'],
    },
    environmental: {
        operatingTemperature: {
            min: 248.15,
            max: 333.15,
            unit: 'K',
        },
        storageTemperature: {
            min: 233.15,
            max: 358.15,
            unit: 'K',
        },
        operatingHumidity: {
            min: 0,
            max: 100,
            unit: '%',
        },
        storageHumidity: {
            min: 0,
            max: 100,
            unit: '%',
        },
        ingressProtection_IP: 'IP54',
        ingressProtection_NEMA: '3R',
        maximumOperatingAltitude: {
            value: null,
            unit: 'm',
        },
        coolingMethod: 'forced-air',
    },
    performance: {
        standbyPower: {
            value: null,
            unit: 'W',
        },
        efficiency: {
            nom: 95,
            max: 95.5,
            unit: '%',
        },
        losses: {
            nom: null,
            max: 1500,
            unit: 'W',
        },
    },
    mechanical: {
        dimensions: {
            width: 0.52,
            length: 1.016,
            height: 0.406,
            unit: 'm',
        },
        weight: {
            value: 61,
            unit: 'kg',
        },
        mountingType: ['wall'],
    },
    files: [
        {
            file: '64cb80628e16bcfbdf2116d4',
            type: 'datasheet',
            id: '65ba634349f0b800018e0648',
        },
        {
            file: '64cb86e2ac9a82d210bb38d0',
            type: 'operationalManual',
            id: '65ba634349f0b800018e0649',
        },
        {
            file: '64cb86fcac9a82d210bb3911',
            type: 'applicationNotes',
            id: '65ba634349f0b800018e064a',
        },
        {
            file: '64d543ac9caa56e410f1e9cf',
            type: 'applicationNotes',
            id: '65ba634349f0b800018e064b',
        },
        {
            file: '64d543bf9caa56e410f1e9fd',
            type: 'installationManual',
            id: '65ba634349f0b800018e064c',
        },
        {
            file: '64d643f1509664bb9eeef5c3',
            type: 'installationManual',
            id: '65ba634349f0b800018e064d',
        },
    ],
    images: [
        {
            file: {
                url: null,
            },
            type: 'thumbnail',
            id: '65ba634349f0b800018e064e',
        },
        {
            file: {
                url: null,
            },
            type: 'ISOPicture',
            id: '65ba634349f0b800018e064f',
        },
        {
            file: {
                url: null,
            },
            type: 'front',
            id: '65ba634349f0b800018e0650',
        },
        {
            file: {
                url: null,
            },
            type: 'rear',
            id: '65ba634349f0b800018e0651',
        },
        {
            file: {
                url: null,
            },
            type: 'left',
            id: '65ba634349f0b800018e0652',
        },
        {
            file: {
                url: null,
            },
            type: 'right',
            id: '65ba634349f0b800018e0653',
        },
        {
            file: {
                url: null,
            },
            type: 'top',
            id: '65ba634349f0b800018e0654',
        },
        {
            file: {
                url: null,
            },
            type: 'bottom',
            id: '65ba634349f0b800018e0655',
        },
    ],
    compliance: {
        CE: false,
        UL: true,
    },
    application: [],
    standards: ['64d38d6aea34f6c51834f8af', '64d38d80ea34f6c51834f8b9', '64d0e0dacb456658b1535384'],
    questions: [],
    archivedAt: null,
    createdBy: '64c9dbed3f4dd5160d67162a',
    team: '649046b4f759645852d0809c',
    createdAt: '2023-08-03T10:23:56.149Z',
    updatedAt: '2024-07-11T22:21:33.122Z',
    __v: 0,
    specificationsSummary: '',
    metadata: {
        electrical: {
            isolationVoltage: {
                value: {
                    display: 'unknown',
                },
            },
            ports: [
                {
                    capacitance: {
                        value: {
                            display: 'unknown',
                        },
                    },
                },
                {
                    capacitance: {
                        value: {
                            display: 'unknown',
                        },
                    },
                },
                {
                    capacitance: {
                        value: {
                            display: 'unknown',
                        },
                    },
                },
            ],
        },
        performance: {
            standbyPower: {
                value: {
                    display: 'unknown',
                },
            },
        },
        environmental: {
            maximumOperatingAltitude: {
                value: {
                    display: 'unknown',
                },
            },
        },
    },
    reviewed: false,
    visibility: 'public',
    videos: [],
    projects: [],
    sortRank: 10,
    compatibleWithPlaceholders: [],
    distributors: [],
    distributorsDetails: [],
    manufacturerObjectId: '646e08a51b6f81686af02188',
    manufacturerDetails: [],
    queryPort0Matches: [false, true, true],
    queryPort1Matches: [false, true, true],
    queryPort2Matches: [true, false, false],
    avgProgress: 77.6858141858142,
});
