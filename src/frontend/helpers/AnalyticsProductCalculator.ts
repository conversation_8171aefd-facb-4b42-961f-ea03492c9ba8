import { ComponentQuery } from 'models';

export interface ComponentStats {
    componentId: string;
    query: string;
    clicks: number;
    impressions: number;
    averagePosition: number;
}

export interface PaginatedResult {
    results: ComponentStats[];
    totalPages: number;
    currentPage: number;
    totalResults: number;
}

export interface ComponentTotals {
    componentId: string;
    stats: ComponentStats[];
    totalClicks: number;
    totalImpressions: number;
    averagePosition: number;
}

interface DataItem {
    _id: string;
    name: 'component.search.click' | 'component.search.view';
    data: {
        component: string;
        company: string;
        position: number;
        search: {
            query: string | null;
            filters: ComponentQuery;
        };
    };
    timestamp: number;
}
export interface PaginatedComponentTotals {
    results: ComponentTotals[];
    totalPages: number;
    currentPage: number;
    totalResults: number;
}
export const AnalyticsProductCalculator = {
    calculateComponentStats: (data: DataItem[]): ComponentStats[] => {
        const groupedStats: {
            [componentId: string]: {
                [query: string]: {
                    clicks: number;
                    impressions: number;
                    positions: number[];
                };
            };
        } = {};

        data.forEach((item) => {
            const componentId = item.data.component;
            const query = item.data.search.filters.search || 'no-query';

            if (!groupedStats[componentId]) {
                groupedStats[componentId] = {};
            }
            if (!groupedStats[componentId][query]) {
                groupedStats[componentId][query] = {
                    clicks: 0,
                    impressions: 0,
                    positions: [],
                };
            }

            if (item.name === 'component.search.click') {
                groupedStats[componentId][query].clicks += 1;
            } else if (item.name === 'component.search.view') {
                groupedStats[componentId][query].impressions += 1;
                groupedStats[componentId][query].positions.push(item.data.position);
            }
        });

        const result: ComponentStats[] = [];
        for (const componentId in groupedStats) {
            for (const query in groupedStats[componentId]) {
                const stats = groupedStats[componentId][query];
                const averagePosition =
                    stats.positions.length > 0
                        ? stats.positions.reduce((sum, pos) => sum + pos, 0) / stats.positions.length
                        : 0;

                result.push({
                    componentId,
                    query,
                    clicks: stats.clicks,
                    impressions: stats.impressions,
                    averagePosition: Number(averagePosition.toFixed(2)),
                });
            }
        }

        return result;
    },

    calculateComponentTotals: (stats: ComponentStats[]): ComponentTotals[] => {
        const totalsByComponent: {
            [componentId: string]: {
                stats: ComponentStats[];
                totalClicks: number;
                totalImpressions: number;
                weightedPositionSum: number;
                totalImpressionWeight: number;
            };
        } = {};

        stats.forEach((stat: ComponentStats) => {
            const componentId = stat.componentId;

            if (!totalsByComponent[componentId]) {
                totalsByComponent[componentId] = {
                    stats: [],
                    totalClicks: 0,
                    totalImpressions: 0,
                    weightedPositionSum: 0,
                    totalImpressionWeight: 0,
                };
            }

            totalsByComponent[componentId].totalClicks += stat.clicks;
            totalsByComponent[componentId].totalImpressions += stat.impressions;
            if (!isNaN(stat.averagePosition) && stat.impressions > 0) {
                totalsByComponent[componentId].weightedPositionSum += stat.averagePosition * stat.impressions;
                totalsByComponent[componentId].totalImpressionWeight += stat.impressions;
            }
            totalsByComponent[componentId].stats.push(stat);
        });

        const result: ComponentTotals[] = [];
        for (const componentId in totalsByComponent) {
            const totals = totalsByComponent[componentId];
            const averagePosition =
                totals.totalImpressionWeight > 0
                    ? Number((totals.weightedPositionSum / totals.totalImpressionWeight).toFixed(2))
                    : 0;

            result.push({
                componentId,
                stats: totals.stats,
                totalClicks: totals.totalClicks,
                totalImpressions: totals.totalImpressions,
                averagePosition,
            });
        }

        return result;
    },

    sortComponentTotals: (
        totals: ComponentTotals[],
        page: number,
        sortBy: 'clicks' | 'impressions',
        resultsPerPage: number = 3,
    ): PaginatedComponentTotals => {
        const sortedTotals = [...totals].sort((a, b) => {
            if (sortBy === 'clicks') {
                return b.totalClicks - a.totalClicks;
            }
            return b.totalImpressions - a.totalImpressions;
        });

        const totalResults = sortedTotals.length;
        const totalPages = Math.ceil(totalResults / resultsPerPage);
        const currentPage = Math.max(1, Math.min(page, totalPages));
        const startIndex = (currentPage - 1) * resultsPerPage;
        const endIndex = startIndex + resultsPerPage;

        const results = sortedTotals.slice(startIndex, endIndex);
        return {
            results,
            totalPages,
            currentPage,
            totalResults,
        };
    },
};
