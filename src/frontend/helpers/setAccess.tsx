import cookie from 'cookie';

import { publicConfig as envConfig } from '@public-config';

const setEnvironmentLockAccessCookie = (res: any, environmentLockAccess: string | null) => {
    if (!environmentLockAccess) {
        return {
            props: {
                message: 'Access Token not found',
            },
        };
    }

    if (envConfig.environmentLockAccess !== undefined && envConfig.environmentLockAccess === environmentLockAccess) {
        res.setHeader(
            'Set-Cookie',
            cookie.serialize('environmentLockAccess', environmentLockAccess, {
                domain: envConfig.domain,
                httpOnly: true,
                secure: true,
                maxAge: 60 * 60 * 24 * 7, // 1 week
                path: '/',
            }),
        );

        return {
            props: {
                message: 'Access Token has been set',
            },
        };
    }

    return {
        props: {
            message: 'Access Token invalid',
        },
    };
};

export { setEnvironmentLockAccessCookie };
