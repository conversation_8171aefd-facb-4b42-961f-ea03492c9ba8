const IntercomHelpers = {
    swr: {
        channels: {
            component: (componentId: string) => `intercom/channels?component=${componentId}`,
            company: (companyId: string) => `intercom/channels?company=${companyId}`,
            project: (projectId: string) => `intercom/channels?project=${projectId}`,
        },
        channelsForTeam: {
            component: (componentId: string, teamId: string) =>
                `intercom/channels?component=${componentId}&team=${teamId}`,
            company: (companyId: string, teamId: string) => `intercom/channels?company=${companyId}&team=${teamId}`,
            project: (projectId: string, teamId: string) => `intercom/channels?project=${projectId}&team=${teamId}`,
        },
        messages: (channelId: string) => `intercom/channels/${channelId}/messages`,
    },
};

export { IntercomHelpers };
