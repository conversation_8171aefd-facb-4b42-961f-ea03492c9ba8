import { ZodObject } from 'zod';

import { TextHelpers } from 'helpers/TextHelpers';

const addUniqueNameValidator = (validator: ZodObject<any>, existingValues: string[], message: string = '') => {
    return validator.extend<any>({
        name: validator.shape.name.refine(
            (value: string) => {
                return !existingValues.some((existingValue) => {
                    return TextHelpers.textMatches(existingValue, value ?? '', true);
                });
            },
            {
                message,
            },
        ),
    });
};

export { addUniqueNameValidator };
