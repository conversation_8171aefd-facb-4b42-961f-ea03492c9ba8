const NumberHelpers = {
    getCleanNumber: (price: number) => {
        if (price === Infinity || price === -Infinity || isNaN(price)) return null;

        return price;
    },

    getPercentageBetweenLimits: ({
        value,
        upperLimit,
        lowerLimit,
    }: {
        value: number;
        upperLimit: number;
        lowerLimit: number;
    }) => {
        if (upperLimit === lowerLimit) return 0;
        if (value < lowerLimit) return 0;
        if (value > upperLimit) return 1;

        return (value - lowerLimit) / (upperLimit - lowerLimit);
    },

    formatPrice: (price: number, fractionDigits: number = 2) => {
        try {
            return `$${price.toLocaleString('en-US', {
                minimumFractionDigits: fractionDigits,
                maximumFractionDigits: fractionDigits,
            })}`;
        } catch (error) {
            console.error('Could not format price', error);
            return '';
        }
    },

    formatPriceShort: (price: number, fractionDigits: number = 0) => {
        if (price >= 1000000) {
            return `$${(price / 1000000).toFixed(fractionDigits)}M`;
        }

        if (price >= 1000) {
            return `$${(price / 1000).toFixed(fractionDigits)}K`;
        }

        return NumberHelpers.formatPrice(price);
    },
};

export { NumberHelpers };
