import { Component } from 'models';
import { DiagramComponentInstance } from 'models';
import { CheckResult, PortCompatabilityChecker } from './PortCompatabilityChecker';

type AutoAssignPortsProps = {
    componentInstance: DiagramComponentInstance;
    productCatalogComponent: Component;
};

export type Primitive = string | number | boolean;
export type Pairing<T extends Primitive> = [number, T];
export type Permutation<T extends Primitive> = Pairing<T>[];
export type Permutations<T extends Primitive> = Permutation<T>[];

type PermutationWithScore = { permutation: Permutation<number>; totalScore: number };
type PermutationWithCheckResults = {
    componentInstancePort: number;
    productCatalogComponentPort: number;
    checkResult: CheckResult;
}[];

const autoAssignPorts = ({
    componentInstance: {
        specifications: {
            electrical: { ports: componentInstanceSpecificationPorts },
        },
        configuration: { ports: componentInstanceConfigurationPorts },
    },
    productCatalogComponent: {
        electrical: { ports: productCatalogComponentPorts },
    },
}: AutoAssignPortsProps): PermutationWithCheckResults => {
    const portMatchScores = productCatalogComponentPorts.map((productCatalogComponentPort) => {
        const checker = new PortCompatabilityChecker(productCatalogComponentPort);

        return componentInstanceSpecificationPorts.map((componentInstanceSpecificationPort, index) =>
            checker.check(componentInstanceSpecificationPort, componentInstanceConfigurationPorts?.[index] ?? {}),
        );
    });

    const validPermutations = getPermutations(
        componentInstanceSpecificationPorts.length,
        productCatalogComponentPorts.map((_, index) => index),
    ).filter(permutationPassesMinimumCriteria(portMatchScores));

    if (validPermutations.length === 0) {
        throw new AutoAssignError('No valid linking found');
    }

    const highestScoringValidPermutation = validPermutations
        .map(totalScoreForPermutation(portMatchScores))
        .reduce(getHighestScoredPermutation);

    return addCheckResultIntoPermutation(highestScoringValidPermutation, portMatchScores);
};

const permutationPassesMinimumCriteria = (portMatchScores: CheckResult[][]) => (permutation: Permutation<number>) =>
    permutation
        .map(getCheckResultFromPairing(portMatchScores))
        .every((checkResult) => checkResult.voltageTypeMatch && checkResult.powerFlowDirectionMatch);

const totalScoreForPermutation =
    (portMatchScores: CheckResult[][]) =>
    (permutation: Permutation<number>): PermutationWithScore => {
        const checkResults = permutation.map(getCheckResultFromPairing(portMatchScores));

        return {
            permutation,
            totalScore: checkResults.reduce((total, checkResult) => total + checkResult.matchScore, 0),
        };
    };

const getHighestScoredPermutation = (highest: PermutationWithScore, next: PermutationWithScore) =>
    next.totalScore > highest.totalScore ? next : highest;

const addCheckResultIntoPermutation = (
    highestScoringValidPermutation: PermutationWithScore,
    portMatchScores: CheckResult[][],
): PermutationWithCheckResults => {
    const getCheckResult = getCheckResultFromPairing(portMatchScores);

    return highestScoringValidPermutation.permutation.map((pairing) => ({
        componentInstancePort: pairing[0],
        productCatalogComponentPort: pairing[1],
        checkResult: getCheckResult(pairing),
    }));
};

const getCheckResultFromPairing =
    (portMatchScores: CheckResult[][]) =>
    ([componentInstancePortIndex, productCatalogComponentPortIndex]: Pairing<number>) =>
        portMatchScores[productCatalogComponentPortIndex][componentInstancePortIndex];

export const getPermutations = <T extends Primitive>(numberOfSlots: number, items: T[]): Permutations<T> => {
    if (numberOfSlots <= 0) {
        throw new Error('Invalid number of slots');
    }

    if (numberOfSlots === 1) {
        return items.map((item) => [[0, item]]);
    }

    if (items.length === 1) {
        const permutations: Permutations<T> = [];
        for (let slot = 0; slot < numberOfSlots; slot++) {
            permutations.push([[slot, items[0]]]);
        }

        return permutations;
    }

    const permutations: Permutations<T> = [];
    for (const [itemIndex, item] of items.entries()) {
        permutations.push(
            ...getPermutations(numberOfSlots - 1, getItemsWithoutIndex(items, itemIndex))
                .map(addSlotNumberOffset)
                .map(addPairingToPermutation([0, item])),
        );
    }

    return permutations;
};

const getItemsWithoutIndex = <T extends Primitive>(items: T[], index: number) => [
    ...items.slice(0, index),
    ...items.slice(index + 1),
];

const addSlotNumberOffset = <T extends Primitive>(permutation: Permutation<T>): Permutation<T> =>
    permutation.map(([slot, item]) => [slot + 1, item]);

const addPairingToPermutation =
    <T extends Primitive>(pairing: Pairing<T>): ((permutation: Permutation<T>) => Permutation<T>) =>
    (permutation) => [pairing, ...permutation];

export class AutoAssignError extends Error {
    static readonly isAutoAssignError = true;
}

export { autoAssignPorts };
