/**
 * Converts a plain text string with paragraphs and simple bullet lists into basic HTML.
 * Assumes paragraphs are separated by double newlines ('\n\n').
 * Assumes bullet points start with '•' possibly preceded/followed by whitespace on a new line.
 */
function textToHtml(text: string = '') {
    // Handle empty or invalid input
    if (!text || typeof text !== 'string') {
        return '';
    }

    // Normalize line breaks to \n and trim leading/trailing whitespace from the whole input
    const normalizedText = text.trim().replace(/\r\n/g, '\n');

    // Split the text into blocks based on one or more blank lines (double newlines)
    const blocks = normalizedText.split(/\n\n+/);

    let htmlOutput = '';
    const listItemRegex = /^\s*•\s*(.*)/; // Regex to detect list items and capture content

    blocks.forEach((block) => {
        const trimmedBlock = block.trim();
        if (!trimmedBlock) return; // Skip empty blocks resulting from multiple blank lines

        // Split the block into individual lines
        const lines = trimmedBlock.split('\n');

        // Find the index of the first line that looks like a list item
        const listStartIndex = lines.findIndex((line) => listItemRegex.test(line.trim()));

        if (listStartIndex === -1) {
            // No list items found in this block, treat the whole block as a single paragraph.
            // Replace any remaining single newlines within the block with spaces for a standard paragraph flow.
            htmlOutput += `<p>${trimmedBlock.replace(/\n/g, ' ').trim()}</p>\n`;
        } else {
            // List items were found in this block

            // 1. Handle any text *before* the list starts as a paragraph
            if (listStartIndex > 0) {
                const introText = lines.slice(0, listStartIndex).join(' ').trim(); // Join potential intro lines with spaces
                if (introText) {
                    htmlOutput += `<p>${introText}</p>\n`;
                }
            }

            // 2. Process the list items
            htmlOutput += '<ul>\n';
            for (let i = listStartIndex; i < lines.length; i++) {
                const trimmedLine = lines[i].trim();
                const match = trimmedLine.match(listItemRegex);
                // Check if the line matches the list item pattern and has captured content
                if (match && match[1] !== undefined) {
                    // Add list item, trimming the captured content
                    htmlOutput += `  <li>${match[1].trim()}</li>\n`;
                }
                // Optional: Handle lines within the list block that *don't* match the pattern.
                // Current behavior: Ignores them. Could potentially add them as plain text or nested paragraphs if needed.
            }
            htmlOutput += '</ul>\n';
        }
    });

    return htmlOutput.trim(); // Return the combined HTML, trimming any final newline
}

export { textToHtml };
