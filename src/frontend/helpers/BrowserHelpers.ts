const BrowserHelpers = {
    isApple: () => {
        if (typeof navigator === 'undefined') {
            return false;
        }

        if (/(Mac|iPhone|iPod|iPad)/i.test(navigator.userAgent)) {
            return true;
        }

        return false;
    },

    isIOS: () => {
        if (typeof navigator === 'undefined') {
            return false;
        }

        if (/(iPhone|iPod|iPad)/i.test(navigator.userAgent)) {
            return true;
        }

        return false;
    },
};

export { BrowserHelpers };
