const StringHelpers = {
    sluggify: (text: string) => {
        const parts = text.toLowerCase().split('');
        const allowed = [
            'a',
            'b',
            'c',
            'd',
            'e',
            'f',
            'g',
            'h',
            'i',
            'j',
            'k',
            'l',
            'm',
            'n',
            'o',
            'p',
            'q',
            'r',
            's',
            't',
            'u',
            'v',
            'w',
            'x',
            'y',
            'z',
            '0',
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8',
            '9',
            '-',
            ' ',
        ];

        return parts
            .filter((part) => allowed.includes(part))
            .join('')
            .replaceAll(' ', '-')
            .replaceAll('----', '-')
            .replaceAll('---', '-')
            .replaceAll('--', '-');
    },

    nl2br: (stringOrLiteral: string) => {
        return stringOrLiteral.replaceAll('\n', '<br/>');
    },

    trim: (stringOrLiteral: string) => {
        const lines = stringOrLiteral.split('\n');

        // Delete first line if it's empty
        if (lines[0] !== undefined && !lines[0].trim()) {
            delete lines[0];
        }

        // Delete last line if it's empty
        if (lines[lines.length - 1] !== undefined && !lines[lines.length - 1].trim()) {
            delete lines[lines.length - 1];
        }

        return lines
            .filter((line) => line !== null)
            .map((line) => line.trim())
            .join('\n');
    },
};

export { StringHelpers };
