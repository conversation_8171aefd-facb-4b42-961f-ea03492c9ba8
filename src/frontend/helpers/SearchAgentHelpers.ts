export type MarkdownRefType = 'company' | 'product' | 'case_study';

type ParsedMarkdownContent = {
    markdownText: string;
    allReferences: { type: MarkdownRefType; id: string }[];
};

const SearchAgentHelpers = {
    getLabelForRef: (refType: MarkdownRefType) => {
        switch (refType) {
            case 'company':
                return 'profile';
            case 'product':
                return 'product';
            case 'case_study':
                return 'case study';
            default:
                return '';
        }
    },

    getUrlForRef: (refType: MarkdownRefType, id: string) => {
        switch (refType) {
            case 'company':
                return `${window.origin}/profiles/${id}`;
            case 'product':
                return `${window.origin}/products/${id}`;
            case 'case_study':
                return `${window.origin}/articles/${id}`;
            default:
                return '';
        }
    },

    parseTextToMarkdownAndReferences: (text: string): ParsedMarkdownContent => {
        const regex = /\[\[(\w+):([a-f0-9]+)\]\]/g;
        const allReferences: { type: MarkdownRefType; id: string }[] = [];
        const markdownText = text.replace(regex, (_, type, id) => {
            const safeType = type as MarkdownRefType;
            allReferences.push({ type: safeType, id });
            return `[View ${SearchAgentHelpers.getLabelForRef(safeType)}](${SearchAgentHelpers.getUrlForRef(safeType, id)})`;
        });

        return { markdownText, allReferences };
    },
};

export { SearchAgentHelpers };
