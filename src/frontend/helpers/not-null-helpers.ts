import { crush } from 'radash';

export const valueHasNonNullNonUnitValue = (value: any): boolean => {
    if (Array.isArray(value)) {
        return value.some(valueHasNonNullNonUnitValue);
    }

    switch (typeof value) {
        case 'string':
        case 'number':
        case 'bigint':
        case 'boolean':
        case 'symbol':
        case 'function':
            return true;
        case 'undefined':
            return false;

        case 'object':
            return value === null ? false : objectHasNonNullNonUnitValue(value);

        default:
            return false;
    }
};

const objectHasNonNullNonUnitValue = (obj: Record<string, any>): boolean =>
    Object.entries(crush(obj)).some(([key, value]) => value !== null && !key.endsWith('unit'));
