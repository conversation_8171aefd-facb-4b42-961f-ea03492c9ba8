import { CompanyProfile, Component, ComponentType } from 'models';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { RouterHelpers } from 'helpers/RouterHelpers';
import { StringHelpers } from 'helpers/StringHelpers';

const ComponentHelpers = {
    urls: {
        overview: () => RouterHelpers.urls.searchTab('products'),
        manage: () => '/products/manage',
        teamComponents: () => '/team-components',
        create: ({ component, company }: { component?: string; company?: CompanyProfile } = {}) => {
            const base = component ? `/products/create/${component}` : '/products/create';

            const companyIsDistributor = CompanyProfileHelpers.isDistributor(company);

            if (company && companyIsDistributor) {
                return `${base}?distributor=${company.id}`;
            }

            if (company) {
                return `${base}?manufacturer=${company.id}`;
            }

            return base;
        },
        view: (componentId: string, slug?: string) => {
            return slug ? `/products/${slug}-${componentId}` : `/products/${componentId}`;
        },
        duplicate: (componentId: string) => `/products/${componentId}/duplicate`,
        generate: (componentId: string) => `/products/${componentId}/generate`,
        bulk: (companyId: string, productSeries: string) =>
            `/products/bulk/${companyId}/${encodeURIComponent(productSeries)}`,
    },

    generateMetadata: (component: Pick<Component, 'name' | 'productSeries' | 'productIdentifier'>) => {
        const { productIdentifier, productSeries } = component;

        let identifier = productIdentifier || '';

        if (productIdentifier && productSeries) {
            if (productIdentifier.toLowerCase().includes(productSeries.toLowerCase())) {
                identifier = productIdentifier;
            } else {
                identifier = `${productIdentifier}, ${productSeries}`;
            }
        }

        const shortName = component.name;
        const longName = [component.name, identifier].filter(Boolean).join(' • ');
        const name = component.name === identifier ? shortName : longName;

        const slug = StringHelpers.sluggify(name);

        return {
            identifier,
            name,
            slug,
        };
    },

    componentTypeHasElectrical: (componentType?: ComponentType) => !componentType || componentType !== 'other',
    componentTypeHasPerformance: (componentType?: ComponentType) => !componentType || componentType !== 'cable',
    componentTypeHasCommunication: (componentType?: ComponentType) => !componentType || componentType !== 'cable',
} as const;

export { ComponentHelpers };
