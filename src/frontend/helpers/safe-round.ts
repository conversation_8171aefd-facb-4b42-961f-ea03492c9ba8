export const safeRound = (value: number, precision = 2) => {
    const precisionMultiplier = Math.pow(10, getDecimalPlacesBeforeFirstSignificantDigit(value) + precision);

    return Math.round(value * precisionMultiplier) / precisionMultiplier;
};

const getDecimalPlacesBeforeFirstSignificantDigit = (value: number) => {
    const absoluteValue = Math.abs(value);

    if (absoluteValue === 0 || absoluteValue >= 1) {
        return 0;
    }

    return Math.abs(Math.ceil(Math.log10(absoluteValue)));
};
