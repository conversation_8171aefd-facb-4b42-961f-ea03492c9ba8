import { construct, get, omit } from 'radash';
import {
    AIConversationMessage,
    ComponentChatMessage,
    AIConversationRole,
    ComponentType,
    crush,
    getComponentValidator,
    Component,
    MeasurementFieldTypes,
} from 'models';
import { z } from 'zod';

class AIServiceUtils {
    static convertAIMessagesToAIConversationMessages = (history?: ComponentChatMessage[]): AIConversationMessage[] => {
        if (!history) {
            return [];
        }

        return history.flatMap((item) => [
            { role: AIConversationRole.USER, content: item.question },
            { role: AIConversationRole.ASSISTANT, content: item.answer },
        ]);
    };

    static getCleanResult = (componentType: ComponentType, result: Partial<Component>) => {
        let cleanResult = crush(result);

        try {
            const validator = getComponentValidator(componentType);
            cleanResult = crush(validator.parse(result));
        } catch (err) {
            if (err instanceof z.ZodError) {
                const invalidPaths = err.issues.map((issue) => issue.path.join('.')) as string[];

                cleanResult = Object.fromEntries(
                    Object.entries(cleanResult).filter(([key]) => !invalidPaths.some((path) => key.startsWith(path))),
                );
            }
        }

        const measurementFields = Object.keys(cleanResult).filter((key) => key.includes('.unit'));

        const constructedCleanResult = construct(cleanResult) as any;

        // we don't want to set measurement fields individually
        // but set them as a whole object
        measurementFields.forEach((unitFieldKey) => {
            const key = unitFieldKey.replace('.unit', '');
            const objectValue = get(constructedCleanResult, key) as any;

            // remove individual fields
            cleanResult = omit(cleanResult, [unitFieldKey, ...MeasurementFieldTypes.map((field) => `${key}.${field}`)]);

            // set object field
            cleanResult[key] = objectValue;
        });

        return cleanResult;
    };

    static safelyParseJSON(value: string) {
        if (!value || value === 'None') {
            return { success: false, value: null };
        }

        const cleaned = value.replaceAll("'", '"').replaceAll('None', 'null');

        try {
            return {
                success: true,
                value: JSON.parse(cleaned),
            };
        } catch (e) {
            console.error('JSON parse error:', e);
            return {
                success: false,
                value: cleaned,
            };
        }
    }

    static processResponse(response: string, componentType: ComponentType, key: string) {
        if (!response) {
            return { success: false };
        }

        const parseResult = AIServiceUtils.safelyParseJSON(response);

        if (!parseResult.success) {
            return { success: false };
        }

        if (parseResult.value !== null) {
            const parsed = AIServiceUtils.getCleanResult(componentType, construct({ [key]: parseResult.value }));

            if (parsed && Object.keys(parsed).length > 0) {
                return { success: true, data: parsed };
            }
        }

        return { success: true, data: {} };
    }
}

export { AIServiceUtils };
