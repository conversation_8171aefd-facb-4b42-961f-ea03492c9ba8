import { describe, it, expect } from '@jest/globals';

import { safeRound } from './safe-round';

describe('safeRound', () => {
    it('should return 0 when the value is 0', () => {
        expect(safeRound(0)).toBe(0);
    });

    it('should round numbers greater than or equal to 1 to the specified precision', () => {
        expect(safeRound(1.2345, 2)).toBe(1.23);
        expect(safeRound(1.2367, 2)).toBe(1.24);
        expect(safeRound(123.456, 1)).toBe(123.5);
        expect(safeRound(1, 2)).toBe(1);
    });

    it('should round numbers less than 1 to specified precision past the first significant digit', () => {
        expect(safeRound(0.0001234, 4)).toBe(0.0001234);
        expect(safeRound(0.0048, 2)).toBe(0.0048);
        expect(safeRound(0.01234, 3)).toBe(0.0123);
        expect(safeRound(0.0005678, 5)).toBe(0.0005678);
        expect(safeRound(0.0005678, 2)).toBe(0.00057);
    });

    it('should use the default precision of 2 when no precision is specified', () => {
        expect(safeRound(1.2345)).toBe(1.23);
        expect(safeRound(0.01234)).toBe(0.012);
    });

    it('should handle negative numbers correctly', () => {
        expect(safeRound(-1.2345, 2)).toBe(-1.23);
        expect(safeRound(-1.2367, 2)).toBe(-1.24);
        expect(safeRound(-123.456, 1)).toBe(-123.5);

        expect(safeRound(-0.000123, 2)).toBe(-0.00012);
        expect(safeRound(-0.0005678, 5)).toBe(-0.0005678);
        expect(safeRound(-0.0005678, 2)).toBe(-0.00057);
    });
});
