import { ComponentQuery, Team, User, UserReferrer } from 'models';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

const RouterHelpers = {
    urls: {
        search: (props?: { productsQuery?: Partial<ComponentQuery> }) => {
            const { productsQuery } = props || {};

            let query = '';

            if (productsQuery) {
                query = `?query=${JSON.stringify(productsQuery)}#products`;
            }

            if (query) {
                return `/search${query}`;
            }

            return '/search';
        },
        homepage: (team?: Team | null, user?: User | null, referrer?: UserReferrer) => {
            if (team?.showDesignEditor) {
                return ProjectHelpers.urls.overview();
            }

            if (user?.referrer === UserReferrer.REPLUS) {
                return RouterHelpers.urls.search();
            }

            return referrer === UserReferrer.REPLUS ? RouterHelpers.urls.search() : ProjectHelpers.urls.overview();
        },
        searchTab: (hash: 'overview' | 'products' | 'designs' | 'profiles' | 'caseStudies' | 'dc-microgrids') =>
            `/search#${hash}`,
        searchAssistant: () => '/resourcepro/plan',
    },
};

export { RouterHelpers };
