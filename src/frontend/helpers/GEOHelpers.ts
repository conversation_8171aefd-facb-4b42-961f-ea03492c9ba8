import { Region } from 'models';

const GEOHelpers = {
    getRegionFromVercelRegion: (vercelRegion: string) => {
        // This is just for reference https://vercel.com/docs/edge-network/regions#region-list
        // const lookup = {
        //     arn1: 'eu-north-1',
        //     bom1: 'ap-south-1',
        //     cdg1: 'eu-west-3',
        //     cle1: 'us-east-2',
        //     cpt1: 'af-south-1',
        //     dub1: 'eu-west-1',
        //     fra1: 'eu-central-1',
        //     gru1: 'sa-east-1',
        //     hkg1: 'ap-east-1',
        //     hnd1: 'ap-northeast-1',
        //     iad1: 'us-east-1',
        //     icn1: 'ap-northeast-2',
        //     kix1: 'ap-northeast-3',
        //     lhr1: 'eu-west-2',
        //     pdx1: 'us-west-2',
        //     sfo1: 'us-west-1',
        //     sin1: 'ap-southeast-1',
        //     syd1: 'ap-southeast-2',
        // };

        const EU = ['arn1', 'cdg1', 'dub1', 'fra1', 'lhr1'];
        const US = ['cle1', 'iad1', 'pdx1', 'sfo1'];

        if (EU.includes(vercelRegion)) {
            return Region.EU;
        }

        if (US.includes(vercelRegion)) {
            return Region.US;
        }

        return null;
    },
};

export { GEOHelpers };
