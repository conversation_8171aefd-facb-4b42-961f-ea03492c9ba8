type Config = {
    duration?: number;
    repeat?: number;
    onUpdate: (value: number) => void;
};

const animate = (from: number, to: number, config: Config) => {
    let startTime: any = null;
    let animationFrameId: any = null;
    // let currentIteration = 0;

    const { duration = 333, repeat = Infinity, onUpdate } = config;

    const loop = (currentTime: number) => {
        if (!startTime) {
            startTime = currentTime;
        }

        const elapsed = currentTime - startTime;

        let progress = elapsed / duration;

        if (repeat === Infinity) {
            progress = progress % 1;
        } else {
            // TODO?
        }

        const value = from + (to - from) * progress;

        onUpdate(value);

        animationFrameId = requestAnimationFrame(loop);
    };

    animationFrameId = requestAnimationFrame(loop);

    return {
        stop: () => {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
        },
    };
};

export { animate };
