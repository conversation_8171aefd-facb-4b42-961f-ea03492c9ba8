import { CollectionConfig, PayloadRequest } from 'payload';

import { unique } from 'radash';

import { CreatedBy, CreatedByCompany, TeamFromProject } from '../../fields/metadata';
import { RequestAccess } from '../../access/RequestAccess';

import { broadcast } from './hooks/broadcast';
import { notify } from './hooks/notify';

import { addView as addChannelView } from '../diagram-chat-channels/helpers/add-view';
import { getTipTapMentions } from '../diagram-comments/helpers/get-tiptap-mentions';
import { ChatAccess } from '../diagram-chat/access/ChatAccess';
import { ProjectAccessChecks } from '../projects/ProjectAccessChecks';
import { getRequestManufacturers } from '../../helpers/get-request-manufacturers';
import { getCollection } from '../../helpers/get-collection';
import { Reactions } from '@/fields/Reactions';

const ChatAccessChecker = new ChatAccess('channel');
const access = async (req: PayloadRequest) => {
    const manufacturers = await getRequestManufacturers(req);

    return req.user
        ? {
              or: [
                  ChatAccessChecker.isTeamUser(req),
                  ChatAccessChecker.isChannelUser(req),
                  ChatAccessChecker.isManufacturerUser(manufacturers),
              ],
          }
        : false;
};

const DiagramChatMessage: CollectionConfig = {
    slug: 'diagramChatMessages',
    access: {
        create: async ({ req }) => {
            return access(req);
        },
        read: async ({ req }) => {
            if (ProjectAccessChecks.userIsDeveloper(req.user)) {
                return true;
            }

            return access(req);
        },
        update: async ({ req, id }) => {
            if (id && ProjectAccessChecks.userIsDeveloper(req.user)) {
                return true;
            }

            return access(req);
        },
        delete: RequestAccess.userIsDeveloper,
    },
    fields: [
        {
            name: 'project',
            type: 'relationship',
            relationTo: 'projects',
            hasMany: false,
            required: true,
            maxDepth: 0,
        },
        {
            name: 'channel',
            type: 'relationship',
            relationTo: 'diagramChatChannels',
            hasMany: false,
            required: true,
            maxDepth: 0,
        },
        {
            name: 'parent',
            type: 'relationship',
            relationTo: 'diagramChatMessages',
            hasMany: false,
            maxDepth: 0,
        },
        {
            name: 'content',
            type: 'blocks',
            blocks: [
                {
                    slug: 'message',
                    fields: [
                        {
                            name: 'content',
                            type: 'text',
                        },
                        {
                            name: 'files',
                            type: 'array',
                            fields: [
                                {
                                    name: 'file',
                                    type: 'upload',
                                    relationTo: 'files',
                                },
                            ],
                        },
                    ],
                },
            ],
            minRows: 1,
            maxRows: 1,
            required: true,
        },
        Reactions.field,
        CreatedBy,
        CreatedByCompany,
        TeamFromProject,
    ],
    hooks: {
        beforeChange: [
            async ({ req, operation, data }) => {
                if (operation === 'create') {
                    const channel = await getCollection('diagramChatChannels', data.channel);
                    const company = channel.access?.company
                        ? await getCollection('manufacturers', channel.access.company)
                        : null;

                    const userPartOfCompany = company?.users?.some((user) => user === req.user?.id);

                    if (userPartOfCompany) {
                        data.createdByCompany = company?.id;
                    }
                }
            },
            async ({ data, originalDoc, req }) => {
                const { users } = getTipTapMentions(JSON.parse(data.content[0].content).content, 'mentions-@');

                if (users.length) {
                    const channelId = originalDoc?.channel || data.channel;
                    const channel = await req.payload.findByID({
                        collection: 'diagramChatChannels',
                        id: channelId,
                        depth: 0,
                        req,
                    });

                    if (channel.access?.users) {
                        channel.access.users.push(...users);
                        channel.access.users = unique(channel.access.users);
                    }

                    await req.payload.update({
                        collection: 'diagramChatChannels',
                        id: channelId,
                        data: {
                            access: channel.access,
                        },
                        req,
                    });
                }
            },
        ],
        afterChange: [
            async ({ doc, req }) => {
                if (!req.user) {
                    return;
                }

                const channel = await req.payload.findByID({
                    collection: 'diagramChatChannels',
                    id: doc.channel,
                    depth: 0,
                });

                await addChannelView(req.payload, channel, req.user);
            },
            broadcast,
            notify,
        ],
    },
    endpoints: [Reactions.endpoint('diagramChatMessages')],
};

export default DiagramChatMessage;
