import { CollectionConfig } from 'payload';

import { getId } from '../helpers/get-id';
import { getRequestUser } from '../helpers/get-request-user';
import {
    ArchivedAt,
    ComponentPublishedAt,
    ComponentPublishedBy,
    CreatedBy,
    DeletedAt,
    Description,
    Name,
    TeamFromUser,
    Website,
} from '../fields';

import { VectorStore } from '@/fields/VectorStore';

import {
    count,
    files,
    search,
    changeReviewStatus,
    addDistributor,
    getProductSeries,
    getProductSeriesComponents,
    bulkEdit,
    deleteComponent,
    featuredProducts,
} from './components/endpoints';

import {
    getPorts,
    calculateCompleteness,
    cleanData,
    extractDistributorIds,
    savePortsBeforeChange,
    savePortsOnCreate,
    updateCompatibleWithCrossReference,
    setComponentCreateContext,
    generateAISummary,
    updateCompanyEmbedding,
    updateManufacturerDetails,
    convertEnergyCapacity,
} from './components/hooks';

import { excludeDeleted } from '../hooks';
import { collectionsAreEqual } from '../helpers/collections-are-equal';
import { SlackService } from '../services/SlackService';
import { CryptoHelpers } from '../helpers/CryptoHelpers';

import {
    all,
    ComponentApplication,
    ComponentFileTypes,
    ComponentType,
    ComponentVisibility,
    RegionAvailability,
} from 'models';

import { RequestAccess } from '../access/RequestAccess';

import { ComponentTypeField } from '@/fields/ComponentType';
import { generateEmbeddingDatasheet } from './components/hooks/generateEmbeddingDatasheet';
import { resetEmbedding } from '@/endpoints/reset-embeddings';

const Components: CollectionConfig = {
    slug: 'components',
    admin: {
        useAsTitle: 'name',
        listSearchableFields: ['productSeries', 'productIdentifier'],
    },
    access: {
        create: ({ req }) => !!req.user,
        read: async ({ req, data, id }) => {
            const user = await getRequestUser(req);

            if (user?.developer) {
                return true;
            }

            if (CryptoHelpers.unsafeSimpleHash(id) === req.searchParams.get('hash')) {
                return true;
            }

            if (data && data.visibility && data.team) {
                return data.visibility === 'public' || collectionsAreEqual(data.team, user?.team);
            }

            const conditions: any[] = [
                {
                    visibility: { equals: 'public' },
                },
            ];

            if (user && user.team) {
                conditions.push({
                    team: { equals: user.team },
                });
            }

            return { or: conditions };
        },
        update: async ({ req }) => {
            // Removed because data.team is undefined even when present in the database
            // if (data) {
            //     return collectionsAreEqual(data.team, req.user?.team);
            // }
            const user = await getRequestUser(req);
            if (user?.developer) {
                return true;
            }
            return user ? { team: { equals: user.team } } : false;
        },
        delete: () => false,
    },
    fields: [
        {
            name: 'grantAccess',
            type: 'ui',
            admin: {
                position: 'sidebar',
                components: {
                    Field: '/Publish',
                },
            },
        },
        {
            type: 'tabs',
            tabs: [
                {
                    label: 'General',
                    fields: [
                        Name,
                        Description,
                        ComponentTypeField,
                        {
                            name: 'canServeAs',
                            type: 'select',
                            options: Object.values(all).map((componentDefinition) => ({
                                value: componentDefinition.type,
                                label: componentDefinition.name,
                            })),
                            hasMany: true,
                        },
                        {
                            name: 'visibility',
                            type: 'select',
                            defaultValue: 'private',
                            options: [
                                {
                                    label: 'Private',
                                    value: 'private',
                                },
                                {
                                    label: 'Public',
                                    value: 'public',
                                },
                            ],
                            hooks: {
                                afterChange: [
                                    async ({ req, data, previousDoc, originalDoc }) => {
                                        const user = await getRequestUser(req);

                                        const wasPrivate =
                                            !previousDoc?.visibility ||
                                            previousDoc.visibility === ComponentVisibility.PRIVATE;
                                        const isPublic = data?.visibility === ComponentVisibility.PUBLIC;

                                        if (wasPrivate && isPublic && user) {
                                            const isAdmin = user.developer;

                                            // Change review status to true when a component is published by an admin
                                            if (isAdmin) {
                                                await req.payload.update({
                                                    collection: 'components',
                                                    id: getId(originalDoc),
                                                    data: {
                                                        reviewed: true,
                                                    },
                                                });
                                            }

                                            // Send a slack message when a component is published by a non-admin
                                            if (!isAdmin) {
                                                SlackService.componentPublish({
                                                    publishedBy: user,
                                                    component: originalDoc,
                                                });
                                            }
                                        }
                                    },
                                ],
                            },
                        },
                        {
                            name: 'application',
                            type: 'select',
                            options: Object.values(ComponentApplication),
                            hasMany: true,
                        },
                        {
                            name: 'reviewed',
                            type: 'checkbox',
                            defaultValue: false,
                        },
                    ],
                },
                {
                    label: 'Product',
                    fields: [
                        {
                            name: 'manufacturer',
                            type: 'relationship',
                            relationTo: 'manufacturers',
                            maxDepth: 0,
                        },
                        {
                            name: 'teamManufacturer', // used as save manufacturer for team components
                            type: 'text',
                        },
                        {
                            name: 'distributorsDetails',
                            label: 'Distributors',
                            type: 'array',
                            fields: [
                                {
                                    name: 'distributor',
                                    type: 'relationship',
                                    relationTo: 'manufacturers',
                                    maxDepth: 0,
                                },
                                // TODO: extra fields for the future
                                {
                                    name: 'price',
                                    type: 'number',
                                },
                            ],
                            defaultValue: [],
                        },
                        {
                            name: 'distributors',
                            type: 'relationship',
                            relationTo: 'manufacturers',
                            hasMany: true,
                            admin: {
                                readOnly: true,
                            },
                            maxDepth: 0,
                        },
                        {
                            type: 'row',
                            fields: [
                                {
                                    name: 'productSeries',
                                    type: 'text',
                                },
                                {
                                    name: 'productIdentifier',
                                    type: 'text',
                                },
                            ],
                            admin: {
                                condition: (values) => {
                                    return !!values?.manufacturer?.length;
                                },
                            },
                        },
                        {
                            name: 'msrp',
                            type: 'number',
                        },
                        {
                            name: 'leadTime',
                            type: 'number',
                        },
                        Website,
                        {
                            name: 'regionAvailability',
                            type: 'select',
                            hasMany: true,
                            options: RegionAvailability.options,
                            defaultValue: [],
                        },
                        {
                            name: 'lifecycle',
                            type: 'group',
                            fields: [
                                {
                                    name: 'release',
                                    type: 'date',
                                },
                                {
                                    name: 'endOfLife',
                                    type: 'date',
                                },
                                {
                                    name: 'endOfLifeAlternatives',
                                    type: 'relationship',
                                    relationTo: 'components',
                                    hasMany: true,
                                    admin: {
                                        disabled: true,
                                    },
                                    maxDepth: 0,
                                },
                            ],
                        },
                    ],
                },
                {
                    label: 'Electrical',
                    fields: [
                        {
                            name: 'electrical',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                    ],
                },
                {
                    label: 'Communication',
                    fields: [
                        {
                            name: 'communication',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                    ],
                },
                {
                    label: 'Environmental',
                    fields: [
                        {
                            name: 'environmental',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                    ],
                },
                {
                    label: 'Performance',
                    fields: [
                        {
                            name: 'performance',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                    ],
                },
                {
                    label: 'Mechanical',
                    fields: [
                        {
                            name: 'mechanical',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                    ],
                },
                {
                    label: 'Attachments',
                    fields: [
                        {
                            name: 'files',
                            type: 'array',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                            fields: [
                                {
                                    type: 'row',
                                    fields: [
                                        {
                                            name: 'file',
                                            type: 'upload',
                                            relationTo: 'files',
                                            maxDepth: 1,
                                        },
                                        {
                                            name: 'type',
                                            type: 'select',
                                            options: ComponentFileTypes.options,
                                        },
                                        {
                                            name: 'visibility',
                                            type: 'select',
                                            defaultValue: 'public',
                                            options: [
                                                {
                                                    label: 'Private',
                                                    value: 'private',
                                                },
                                                {
                                                    label: 'Public',
                                                    value: 'public',
                                                },
                                            ],
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            name: 'images',
                            type: 'array',
                            fields: [
                                {
                                    type: 'row',
                                    fields: [
                                        {
                                            name: 'file',
                                            type: 'upload',
                                            relationTo: 'files',
                                            maxDepth: 1,
                                        },
                                        {
                                            name: 'type',
                                            type: 'select',
                                            options: [
                                                {
                                                    value: 'thumbnail',
                                                    label: 'Thumbnail',
                                                },
                                                {
                                                    value: 'front',
                                                    label: 'Front',
                                                },
                                                {
                                                    value: 'rear',
                                                    label: 'Rear',
                                                },
                                                {
                                                    value: 'left',
                                                    label: 'Left',
                                                },
                                                {
                                                    value: 'right',
                                                    label: 'Right',
                                                },
                                                { value: 'top', label: 'Top' },
                                                {
                                                    value: 'bottom',
                                                    label: 'Bottom',
                                                },
                                                {
                                                    value: 'general',
                                                    label: 'General',
                                                },
                                                {
                                                    value: 'ISOPicture',
                                                    label: 'ISO Picture',
                                                },
                                            ],
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            name: 'videos',
                            type: 'array',
                            fields: [
                                {
                                    type: 'row',
                                    fields: [
                                        {
                                            name: 'video',
                                            type: 'text',
                                        },
                                    ],
                                },
                            ],
                        },
                        VectorStore('filesVectorStore'),
                    ],
                },
                {
                    label: 'Questions',
                    fields: [
                        {
                            name: 'questions',
                            type: 'array',
                            fields: [
                                {
                                    name: 'question',
                                    type: 'text',
                                },
                                {
                                    name: 'answer',
                                    type: 'textarea',
                                },
                            ],
                        },
                    ],
                },
                {
                    name: 'compliance',
                    label: 'Compliance',
                    fields: [
                        {
                            name: 'CE',
                            type: 'checkbox',
                        },
                        {
                            name: 'UL',
                            type: 'checkbox',
                        },
                        {
                            name: 'currentOS',
                            type: 'checkbox',
                        },
                        {
                            name: 'emergeAlliance',
                            type: 'checkbox',
                        },
                        {
                            name: 'ODCA',
                            type: 'checkbox',
                        },
                        {
                            name: 'other',
                            type: 'checkbox',
                        },
                        {
                            name: 'otherInput',
                            type: 'text',
                        },
                    ],
                },
                {
                    label: 'Standards',
                    fields: [
                        {
                            name: 'standards',
                            type: 'relationship',
                            relationTo: 'standards',
                            hasMany: true,
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                            maxDepth: 0,
                        },
                    ],
                },
                {
                    label: 'Metadata',
                    fields: [
                        {
                            type: 'row',
                            fields: [ArchivedAt, DeletedAt],
                        },
                        CreatedBy,
                        TeamFromUser,
                        ComponentPublishedBy,
                        ComponentPublishedAt,
                        {
                            name: 'metadata',
                            type: 'json',
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                        {
                            type: 'number',
                            name: 'sortRank',
                            defaultValue: 0,
                        },
                        {
                            type: 'relationship',
                            name: 'compatibleWith',
                            label: 'Compatible With',
                            relationTo: 'components',
                            hasMany: true,
                            maxDepth: 0,
                            hooks: {
                                afterChange: [updateCompatibleWithCrossReference],
                            },
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                        {
                            type: 'relationship',
                            name: 'compatibleWithCrossReference',
                            label: 'Compatible With Cross Reference',
                            relationTo: 'components',
                            hasMany: true,
                            maxDepth: 0,
                        },
                        {
                            type: 'array',
                            name: 'compatibleWithPlaceholders',
                            label: 'Compatible With Placeholders',
                            fields: [
                                { type: 'text', name: 'manufacturer', required: true },
                                { type: 'text', name: 'productIdentifier', required: true },
                                {
                                    type: 'select',
                                    name: 'componentType',
                                    options: ComponentType.options,
                                },
                            ],
                            access: {
                                read: RequestAccess.userIsLoggedIn,
                            },
                        },
                        {
                            name: 'bulkUpload',
                            type: 'checkbox',
                            defaultValue: false,
                        },
                        {
                            name: 'bulkUploadId',
                            type: 'text',
                        },
                        {
                            name: 'completeness',
                            type: 'number',
                            defaultValue: 0,
                        },
                        {
                            name: 'dataSource',
                            type: 'relationship',
                            relationTo: 'dataSources',
                        },
                        {
                            name: 'embeddingText',
                            type: 'text',
                            hidden: true,
                        },
                        {
                            name: 'embedding',
                            type: 'json',
                            hidden: true,
                        },
                        {
                            name: 'specificationsSummary',
                            type: 'textarea',
                        },
                        {
                            name: 'manufacturerDetails',
                            type: 'json',
                            hidden: true,
                        },
                        {
                            name: 'savedCount',
                            type: 'number',
                            defaultValue: 0,
                            hidden: true,
                        },
                        {
                            name: 'viewedCount',
                            type: 'number',
                            defaultValue: 0,
                            hidden: true,
                        },
                    ],
                },
            ],
        },
    ],
    hooks: {
        beforeOperation: [excludeDeleted],
        beforeValidate: [cleanData],
        beforeChange: [
            setComponentCreateContext,
            generateEmbeddingDatasheet,
            extractDistributorIds,
            calculateCompleteness,
            savePortsBeforeChange,
            updateManufacturerDetails,
            convertEnergyCapacity,
        ],
        afterChange: [savePortsOnCreate, generateAISummary, updateCompanyEmbedding],
        afterRead: [getPorts],
    },
    endpoints: [
        search,
        files,
        count,
        changeReviewStatus,
        addDistributor,
        resetEmbedding('components'),
        getProductSeries,
        getProductSeriesComponents,
        bulkEdit,
        deleteComponent,
        featuredProducts,
    ],
};

export default Components;
