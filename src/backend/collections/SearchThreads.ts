import { CollectionConfig } from 'payload';
import { RequestAccess } from '../access/RequestAccess';

import { excludeDeleted } from '@/hooks';
import { CreatedAt, CreatedBy, DeletedAt } from '@/fields';
import { deleteSearchThread } from '@/collections/search-threads/endpoints/delete';

const SearchThreads: CollectionConfig = {
    slug: 'searchThreads',
    access: {
        create: () => true,
        read: async ({ req, id }) => {
            if (await RequestAccess.userIsDeveloper({ req })) {
                return true;
            }

            if (await RequestAccess.createdByUser({ req })) {
                return true;
            }

            // anonymous users can access a chat that has no user
            if (id && !req.user) {
                return {
                    createdBy: { equals: null },
                };
            }

            return false;
        },
        update: async ({ req, id }) => {
            if (await RequestAccess.userIsDeveloper({ req })) {
                return true;
            }

            if (await RequestAccess.createdByUser({ req })) {
                return true;
            }

            // anonymous users can access a chat that has no user
            if (id && !req.user) {
                return {
                    createdBy: { equals: null },
                };
            }

            return false;
        },
        delete: RequestAccess.userIsDeveloper,
    },
    fields: [
        {
            name: 'thread',
            type: 'json',
        },
        {
            name: 'email',
            type: 'email',
        },
        {
            name: 'exhibitorMatch',
            type: 'relationship',
            relationTo: 'exhibitorMatches',
        },
        {
            name: 'internal',
            type: 'checkbox',
            defaultValue: false,
        },
        {
            name: 'event',
            type: 'relationship',
            relationTo: 'events',
            maxDepth: 0,
        },
        CreatedBy,
        CreatedAt,
        DeletedAt,
    ],
    hooks: {
        beforeOperation: [excludeDeleted],
    },
    endpoints: [deleteSearchThread],
};

export default SearchThreads;
