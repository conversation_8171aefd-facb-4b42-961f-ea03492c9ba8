import { CollectionConfig } from 'payload';

import { CreatedBy, CreatedByCompany } from '../../fields/metadata';
import { Reactions } from '../../fields/Reactions';

import { addChannelView } from './hooks/add-channel-view';
import { broadcast } from './hooks/broadcast';
import { notify } from './hooks/notify';

import { getCollection } from '../../helpers/get-collection';

const IntercomMessages: CollectionConfig = {
    slug: 'intercomMessages',
    fields: [
        {
            name: 'channel',
            type: 'relationship',
            relationTo: 'intercomChannels',
            hasMany: false,
            required: true,
            maxDepth: 0,
        },
        {
            name: 'content',
            type: 'blocks',
            blocks: [
                {
                    slug: 'message',
                    fields: [
                        {
                            name: 'content',
                            type: 'text',
                        },
                        {
                            name: 'files',
                            type: 'array',
                            fields: [
                                {
                                    name: 'file',
                                    type: 'upload',
                                    relationTo: 'files',
                                },
                            ],
                        },
                    ],
                },
            ],
            minRows: 1,
            maxRows: 1,
            required: true,
        },
        Reactions.field,
        CreatedBy,
        CreatedByCompany,
    ],
    hooks: {
        beforeChange: [
            async ({ req, operation, data }) => {
                if (operation === 'create') {
                    const channel = await getCollection('intercomChannels', data.channel);
                    const company = channel.access?.company
                        ? await getCollection('manufacturers', channel.access.company)
                        : null;

                    const userPartOfCompany = company?.users?.some((user) => user === req.user?.id);

                    if (userPartOfCompany) {
                        data.createdByCompany = company?.id;
                    }
                }
            },
        ],
        afterChange: [addChannelView, broadcast, notify],
    },
    endpoints: [Reactions.endpoint('intercomMessages')],
};

export default IntercomMessages;
