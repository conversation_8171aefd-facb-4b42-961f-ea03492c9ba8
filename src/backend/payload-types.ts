/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "StubArray".
 */
export type StubArray =
  | {
      name?: string | null;
      description?:
        | {
            [k: string]: unknown;
          }
        | unknown[]
        | string
        | number
        | boolean
        | null;
      button?: {
        label?: string | null;
        url?: string | null;
      };
      image?: (string | null) | File;
      imageCrop?: ('contain' | 'crop') | null;
      files?:
        | {
            file?: (string | null) | File;
            id?: string | null;
          }[]
        | null;
      tags?:
        | {
            [k: string]: unknown;
          }
        | unknown[]
        | string
        | number
        | boolean
        | null;
      createdAt?: string | null;
      id?: string | null;
    }[]
  | null;
/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s J<PERSON><PERSON><PERSON><PERSON>hema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Africa/Abidjan'
  | 'Africa/Accra'
  | 'Africa/Addis_Ababa'
  | 'Africa/Algiers'
  | 'Africa/Bamako'
  | 'Africa/Bangui'
  | 'Africa/Banjul'
  | 'Africa/Bissau'
  | 'Africa/Blantyre'
  | 'Africa/Brazzaville'
  | 'Africa/Bujumbura'
  | 'Africa/Cairo'
  | 'Africa/Casablanca'
  | 'Africa/Ceuta'
  | 'Africa/Conakry'
  | 'Africa/Dakar'
  | 'Africa/Dar_es_Salaam'
  | 'Africa/Djibouti'
  | 'Africa/Douala'
  | 'Africa/El_Aaiun'
  | 'Africa/Freetown'
  | 'Africa/Gaborone'
  | 'Africa/Harare'
  | 'Africa/Johannesburg'
  | 'Africa/Juba'
  | 'Africa/Kampala'
  | 'Africa/Khartoum'
  | 'Africa/Kigali'
  | 'Africa/Kinshasa'
  | 'Africa/Lagos'
  | 'Africa/Libreville'
  | 'Africa/Lome'
  | 'Africa/Luanda'
  | 'Africa/Lubumbashi'
  | 'Africa/Lusaka'
  | 'Africa/Malabo'
  | 'Africa/Maputo'
  | 'Africa/Maseru'
  | 'Africa/Mbabane'
  | 'Africa/Mogadishu'
  | 'Africa/Monrovia'
  | 'Africa/Nairobi'
  | 'Africa/Ndjamena'
  | 'Africa/Niamey'
  | 'Africa/Nouakchott'
  | 'Africa/Ouagadougou'
  | 'Africa/Porto-Novo'
  | 'Africa/Sao_Tome'
  | 'Africa/Tripoli'
  | 'Africa/Tunis'
  | 'Africa/Windhoek'
  | 'America/Adak'
  | 'America/Anchorage'
  | 'America/Anguilla'
  | 'America/Antigua'
  | 'America/Araguaina'
  | 'America/Aruba'
  | 'America/Asuncion'
  | 'America/Bahia'
  | 'America/Bahia_Banderas'
  | 'America/Barbados'
  | 'America/Belem'
  | 'America/Belize'
  | 'America/Blanc-Sablon'
  | 'America/Boa_Vista'
  | 'America/Bogota'
  | 'America/Boise'
  | 'America/Cambridge_Bay'
  | 'America/Campo_Grande'
  | 'America/Cancun'
  | 'America/Caracas'
  | 'America/Cayenne'
  | 'America/Cayman'
  | 'America/Chicago'
  | 'America/Chihuahua'
  | 'America/Costa_Rica'
  | 'America/Creston'
  | 'America/Cuiaba'
  | 'America/Curacao'
  | 'America/Danmarkshavn'
  | 'America/Dawson'
  | 'America/Dawson_Creek'
  | 'America/Denver'
  | 'America/Detroit'
  | 'America/Dominica'
  | 'America/Edmonton'
  | 'America/Eirunepe'
  | 'America/El_Salvador'
  | 'America/Fort_Nelson'
  | 'America/Fortaleza'
  | 'America/Glace_Bay'
  | 'America/Godthab'
  | 'America/Goose_Bay'
  | 'America/Grand_Turk'
  | 'America/Grenada'
  | 'America/Guadeloupe'
  | 'America/Guatemala'
  | 'America/Guayaquil'
  | 'America/Guyana'
  | 'America/Halifax'
  | 'America/Havana'
  | 'America/Hermosillo'
  | 'America/Inuvik'
  | 'America/Iqaluit'
  | 'America/Jamaica'
  | 'America/Juneau'
  | 'America/La_Paz'
  | 'America/Lima'
  | 'America/Los_Angeles'
  | 'America/Lower_Princes'
  | 'America/Maceio'
  | 'America/Managua'
  | 'America/Manaus'
  | 'America/Marigot'
  | 'America/Martinique'
  | 'America/Matamoros'
  | 'America/Mazatlan'
  | 'America/Menominee'
  | 'America/Merida'
  | 'America/Metlakatla'
  | 'America/Mexico_City'
  | 'America/Miquelon'
  | 'America/Moncton'
  | 'America/Monterrey'
  | 'America/Montevideo'
  | 'America/Montserrat'
  | 'America/Nassau'
  | 'America/New_York'
  | 'America/Nome'
  | 'America/Noronha'
  | 'America/North_Dakota/Beulah'
  | 'America/North_Dakota/Center'
  | 'America/North_Dakota/New_Salem'
  | 'America/Ojinaga'
  | 'America/Panama'
  | 'America/Paramaribo'
  | 'America/Phoenix'
  | 'America/Port-au-Prince'
  | 'America/Port_of_Spain'
  | 'America/Porto_Velho'
  | 'America/Puerto_Rico'
  | 'America/Punta_Arenas'
  | 'America/Rankin_Inlet'
  | 'America/Recife'
  | 'America/Regina'
  | 'America/Resolute'
  | 'America/Rio_Branco'
  | 'America/Santarem'
  | 'America/Santiago'
  | 'America/Santo_Domingo'
  | 'America/Sao_Paulo'
  | 'America/Scoresbysund'
  | 'America/Sitka'
  | 'America/St_Barthelemy'
  | 'America/St_Johns'
  | 'America/St_Kitts'
  | 'America/St_Lucia'
  | 'America/St_Thomas'
  | 'America/St_Vincent'
  | 'America/Swift_Current'
  | 'America/Tegucigalpa'
  | 'America/Thule'
  | 'America/Tijuana'
  | 'America/Toronto'
  | 'America/Tortola'
  | 'America/Vancouver'
  | 'America/Whitehorse'
  | 'America/Winnipeg'
  | 'America/Yakutat'
  | 'Antarctica/Casey'
  | 'Antarctica/Davis'
  | 'Antarctica/DumontDUrville'
  | 'Antarctica/Macquarie'
  | 'Antarctica/Mawson'
  | 'Antarctica/Palmer'
  | 'Antarctica/Rothera'
  | 'Antarctica/Syowa'
  | 'Antarctica/Troll'
  | 'Antarctica/Vostok'
  | 'Arctic/Longyearbyen'
  | 'Asia/Aden'
  | 'Asia/Almaty'
  | 'Asia/Amman'
  | 'Asia/Anadyr'
  | 'Asia/Aqtau'
  | 'Asia/Aqtobe'
  | 'Asia/Ashgabat'
  | 'Asia/Atyrau'
  | 'Asia/Baghdad'
  | 'Asia/Bahrain'
  | 'Asia/Baku'
  | 'Asia/Bangkok'
  | 'Asia/Barnaul'
  | 'Asia/Beirut'
  | 'Asia/Bishkek'
  | 'Asia/Brunei'
  | 'Asia/Chita'
  | 'Asia/Colombo'
  | 'Asia/Damascus'
  | 'Asia/Dhaka'
  | 'Asia/Dili'
  | 'Asia/Dubai'
  | 'Asia/Dushanbe'
  | 'Asia/Famagusta'
  | 'Asia/Gaza'
  | 'Asia/Hebron'
  | 'Asia/Hong_Kong'
  | 'Asia/Hovd'
  | 'Asia/Irkutsk'
  | 'Asia/Jakarta'
  | 'Asia/Jayapura'
  | 'Asia/Jerusalem'
  | 'Asia/Kabul'
  | 'Asia/Kamchatka'
  | 'Asia/Karachi'
  | 'Asia/Khandyga'
  | 'Asia/Krasnoyarsk'
  | 'Asia/Kuala_Lumpur'
  | 'Asia/Kuching'
  | 'Asia/Kuwait'
  | 'Asia/Macau'
  | 'Asia/Magadan'
  | 'Asia/Makassar'
  | 'Asia/Manila'
  | 'Asia/Muscat'
  | 'Asia/Nicosia'
  | 'Asia/Novokuznetsk'
  | 'Asia/Novosibirsk'
  | 'Asia/Omsk'
  | 'Asia/Oral'
  | 'Asia/Phnom_Penh'
  | 'Asia/Pontianak'
  | 'Asia/Pyongyang'
  | 'Asia/Qatar'
  | 'Asia/Qostanay'
  | 'Asia/Qyzylorda'
  | 'Asia/Riyadh'
  | 'Asia/Sakhalin'
  | 'Asia/Samarkand'
  | 'Asia/Seoul'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Srednekolymsk'
  | 'Asia/Taipei'
  | 'Asia/Tashkent'
  | 'Asia/Tbilisi'
  | 'Asia/Tehran'
  | 'Asia/Thimphu'
  | 'Asia/Tokyo'
  | 'Asia/Tomsk'
  | 'Asia/Ulaanbaatar'
  | 'Asia/Urumqi'
  | 'Asia/Ust-Nera'
  | 'Asia/Vientiane'
  | 'Asia/Vladivostok'
  | 'Asia/Yakutsk'
  | 'Asia/Yekaterinburg'
  | 'Asia/Yerevan'
  | 'Atlantic/Azores'
  | 'Atlantic/Bermuda'
  | 'Atlantic/Canary'
  | 'Atlantic/Cape_Verde'
  | 'Atlantic/Madeira'
  | 'Atlantic/Reykjavik'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/St_Helena'
  | 'Atlantic/Stanley'
  | 'Australia/Adelaide'
  | 'Australia/Brisbane'
  | 'Australia/Broken_Hill'
  | 'Australia/Darwin'
  | 'Australia/Eucla'
  | 'Australia/Hobart'
  | 'Australia/Lindeman'
  | 'Australia/Lord_Howe'
  | 'Australia/Melbourne'
  | 'Australia/Perth'
  | 'Australia/Sydney'
  | 'Europe/Amsterdam'
  | 'Europe/Andorra'
  | 'Europe/Astrakhan'
  | 'Europe/Athens'
  | 'Europe/Belgrade'
  | 'Europe/Berlin'
  | 'Europe/Bratislava'
  | 'Europe/Brussels'
  | 'Europe/Bucharest'
  | 'Europe/Budapest'
  | 'Europe/Busingen'
  | 'Europe/Chisinau'
  | 'Europe/Copenhagen'
  | 'Europe/Dublin'
  | 'Europe/Gibraltar'
  | 'Europe/Guernsey'
  | 'Europe/Helsinki'
  | 'Europe/Isle_of_Man'
  | 'Europe/Istanbul'
  | 'Europe/Jersey'
  | 'Europe/Kaliningrad'
  | 'Europe/Kiev'
  | 'Europe/Kirov'
  | 'Europe/Lisbon'
  | 'Europe/Ljubljana'
  | 'Europe/London'
  | 'Europe/Luxembourg'
  | 'Europe/Madrid'
  | 'Europe/Malta'
  | 'Europe/Mariehamn'
  | 'Europe/Minsk'
  | 'Europe/Monaco'
  | 'Europe/Moscow'
  | 'Europe/Oslo'
  | 'Europe/Paris'
  | 'Europe/Podgorica'
  | 'Europe/Prague'
  | 'Europe/Riga'
  | 'Europe/Rome'
  | 'Europe/Samara'
  | 'Europe/San_Marino'
  | 'Europe/Sarajevo'
  | 'Europe/Saratov'
  | 'Europe/Simferopol'
  | 'Europe/Skopje'
  | 'Europe/Sofia'
  | 'Europe/Stockholm'
  | 'Europe/Tallinn'
  | 'Europe/Tirane'
  | 'Europe/Ulyanovsk'
  | 'Europe/Vaduz'
  | 'Europe/Vatican'
  | 'Europe/Vienna'
  | 'Europe/Vilnius'
  | 'Europe/Volgograd'
  | 'Europe/Warsaw'
  | 'Europe/Zagreb'
  | 'Europe/Zurich'
  | 'Indian/Antananarivo'
  | 'Indian/Chagos'
  | 'Indian/Christmas'
  | 'Indian/Cocos'
  | 'Indian/Comoro'
  | 'Indian/Kerguelen'
  | 'Indian/Mahe'
  | 'Indian/Maldives'
  | 'Indian/Mauritius'
  | 'Indian/Mayotte'
  | 'Indian/Reunion'
  | 'Pacific/Apia'
  | 'Pacific/Auckland'
  | 'Pacific/Bougainville'
  | 'Pacific/Chatham'
  | 'Pacific/Easter'
  | 'Pacific/Efate'
  | 'Pacific/Enderbury'
  | 'Pacific/Fakaofo'
  | 'Pacific/Fiji'
  | 'Pacific/Funafuti'
  | 'Pacific/Galapagos'
  | 'Pacific/Gambier'
  | 'Pacific/Guadalcanal'
  | 'Pacific/Guam'
  | 'Pacific/Honolulu'
  | 'Pacific/Kiritimati'
  | 'Pacific/Kosrae'
  | 'Pacific/Kwajalein'
  | 'Pacific/Majuro'
  | 'Pacific/Marquesas'
  | 'Pacific/Midway'
  | 'Pacific/Nauru'
  | 'Pacific/Niue'
  | 'Pacific/Norfolk'
  | 'Pacific/Noumea'
  | 'Pacific/Pago_Pago'
  | 'Pacific/Palau'
  | 'Pacific/Pitcairn'
  | 'Pacific/Port_Moresby'
  | 'Pacific/Rarotonga'
  | 'Pacific/Saipan'
  | 'Pacific/Tahiti'
  | 'Pacific/Tarawa'
  | 'Pacific/Tongatapu'
  | 'Pacific/Wake'
  | 'Pacific/Wallis';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    aiSuggestions: AiSuggestion;
    aiMessages: AiMessage;
    aiembeddings: Aiembedding;
    components: Component;
    componentFiles: ComponentFile;
    componentPorts: ComponentPort;
    diagramChatChannels: DiagramChatChannel;
    diagramChatMessages: DiagramChatMessage;
    diagramAIMessages: DiagramAIMessage;
    diagramSimulationProfiles: DiagramSimulationProfile;
    diagramLiveData: DiagramLiveDatum;
    diagramLiveDataAggregated: DiagramLiveDataAggregated;
    intercomChannels: IntercomChannel;
    intercomMessages: IntercomMessage;
    manufacturers: Manufacturer;
    projects: Project;
    projectDesigns: ProjectDesign;
    projectDesignDiagrams: ProjectDesignDiagram;
    diagramComments: DiagramComment;
    teams: Team;
    users: User;
    files: File;
    formSubmissions: FormSubmission;
    standards: Standard;
    transactions: Transaction;
    orders: Order;
    notifications: Notification;
    loginCodes: LoginCode;
    billOfMaterials: BillOfMaterial;
    stripeRequests: StripeRequest;
    simulations: Simulation;
    faultSimulations: FaultSimulation;
    wireSizings: WireSizing;
    jobLogs: JobLog;
    dataSources: DataSource;
    savedItems: SavedItem;
    events: Event;
    views: View;
    articles: Article;
    companyImports: CompanyImport;
    searchThreads: SearchThread;
    exhibitorMatches: ExhibitorMatch;
    exhibitorMatchLeads: ExhibitorMatchLead;
    emails: Email;
    userInvitations: UserInvitation;
    qrScans: QrScan;
    qrScansAISummaries: QrScansAISummary;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    aiSuggestions: AiSuggestionsSelect<false> | AiSuggestionsSelect<true>;
    aiMessages: AiMessagesSelect<false> | AiMessagesSelect<true>;
    aiembeddings: AiembeddingsSelect<false> | AiembeddingsSelect<true>;
    components: ComponentsSelect<false> | ComponentsSelect<true>;
    componentFiles: ComponentFilesSelect<false> | ComponentFilesSelect<true>;
    componentPorts: ComponentPortsSelect<false> | ComponentPortsSelect<true>;
    diagramChatChannels: DiagramChatChannelsSelect<false> | DiagramChatChannelsSelect<true>;
    diagramChatMessages: DiagramChatMessagesSelect<false> | DiagramChatMessagesSelect<true>;
    diagramAIMessages: DiagramAIMessagesSelect<false> | DiagramAIMessagesSelect<true>;
    diagramSimulationProfiles: DiagramSimulationProfilesSelect<false> | DiagramSimulationProfilesSelect<true>;
    diagramLiveData: DiagramLiveDataSelect<false> | DiagramLiveDataSelect<true>;
    diagramLiveDataAggregated: DiagramLiveDataAggregatedSelect<false> | DiagramLiveDataAggregatedSelect<true>;
    intercomChannels: IntercomChannelsSelect<false> | IntercomChannelsSelect<true>;
    intercomMessages: IntercomMessagesSelect<false> | IntercomMessagesSelect<true>;
    manufacturers: ManufacturersSelect<false> | ManufacturersSelect<true>;
    projects: ProjectsSelect<false> | ProjectsSelect<true>;
    projectDesigns: ProjectDesignsSelect<false> | ProjectDesignsSelect<true>;
    projectDesignDiagrams: ProjectDesignDiagramsSelect<false> | ProjectDesignDiagramsSelect<true>;
    diagramComments: DiagramCommentsSelect<false> | DiagramCommentsSelect<true>;
    teams: TeamsSelect<false> | TeamsSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    files: FilesSelect<false> | FilesSelect<true>;
    formSubmissions: FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    standards: StandardsSelect<false> | StandardsSelect<true>;
    transactions: TransactionsSelect<false> | TransactionsSelect<true>;
    orders: OrdersSelect<false> | OrdersSelect<true>;
    notifications: NotificationsSelect<false> | NotificationsSelect<true>;
    loginCodes: LoginCodesSelect<false> | LoginCodesSelect<true>;
    billOfMaterials: BillOfMaterialsSelect<false> | BillOfMaterialsSelect<true>;
    stripeRequests: StripeRequestsSelect<false> | StripeRequestsSelect<true>;
    simulations: SimulationsSelect<false> | SimulationsSelect<true>;
    faultSimulations: FaultSimulationsSelect<false> | FaultSimulationsSelect<true>;
    wireSizings: WireSizingsSelect<false> | WireSizingsSelect<true>;
    jobLogs: JobLogsSelect<false> | JobLogsSelect<true>;
    dataSources: DataSourcesSelect<false> | DataSourcesSelect<true>;
    savedItems: SavedItemsSelect<false> | SavedItemsSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    views: ViewsSelect<false> | ViewsSelect<true>;
    articles: ArticlesSelect<false> | ArticlesSelect<true>;
    companyImports: CompanyImportsSelect<false> | CompanyImportsSelect<true>;
    searchThreads: SearchThreadsSelect<false> | SearchThreadsSelect<true>;
    exhibitorMatches: ExhibitorMatchesSelect<false> | ExhibitorMatchesSelect<true>;
    exhibitorMatchLeads: ExhibitorMatchLeadsSelect<false> | ExhibitorMatchLeadsSelect<true>;
    emails: EmailsSelect<false> | EmailsSelect<true>;
    userInvitations: UserInvitationsSelect<false> | UserInvitationsSelect<true>;
    qrScans: QrScansSelect<false> | QrScansSelect<true>;
    qrScansAISummaries: QrScansAISummariesSelect<false> | QrScansAISummariesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    general: General;
    sitemap: Sitemap;
  };
  globalsSelect: {
    general: GeneralSelect<false> | GeneralSelect<true>;
    sitemap: SitemapSelect<false> | SitemapSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiSuggestions".
 */
export interface AiSuggestion {
  id: string;
  types: (
    | 'battery'
    | 'bus'
    | 'capacitor'
    | 'breaker'
    | 'charger'
    | 'combinerBox'
    | 'contactor'
    | 'converter'
    | 'custom'
    | 'disconnect'
    | 'fuse'
    | 'generator'
    | 'grounding'
    | 'hvac'
    | 'hydro'
    | 'load'
    | 'light'
    | 'meter'
    | 'motor'
    | 'other'
    | 'panel'
    | 'powerDistributionUnit'
    | 'rapidShutdownDevice'
    | 'solar'
    | 'solution'
    | 'transferSwitch'
    | 'transformer'
    | 'utility'
    | 'wind'
    | 'cable'
  )[];
  suggestion: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiMessages".
 */
export interface AiMessage {
  id: string;
  question: string;
  answer: string;
  references?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  rating?: ('thumbsUp' | 'thumbsDown') | null;
  component?: (string | null) | Component;
  project?: (string | null) | Project;
  createdBy?: (string | null) | User;
  team: string | Team;
  deletedAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "components".
 */
export interface Component {
  id: string;
  name: string;
  description?: string | null;
  type:
    | 'battery'
    | 'bus'
    | 'capacitor'
    | 'breaker'
    | 'charger'
    | 'combinerBox'
    | 'contactor'
    | 'converter'
    | 'custom'
    | 'disconnect'
    | 'fuse'
    | 'generator'
    | 'grounding'
    | 'hvac'
    | 'hydro'
    | 'load'
    | 'light'
    | 'meter'
    | 'motor'
    | 'other'
    | 'panel'
    | 'powerDistributionUnit'
    | 'rapidShutdownDevice'
    | 'solar'
    | 'solution'
    | 'transferSwitch'
    | 'transformer'
    | 'utility'
    | 'wind'
    | 'cable';
  canServeAs?:
    | (
        | 'battery'
        | 'bus'
        | 'capacitor'
        | 'breaker'
        | 'charger'
        | 'combinerBox'
        | 'contactor'
        | 'converter'
        | 'custom'
        | 'disconnect'
        | 'fuse'
        | 'generator'
        | 'grounding'
        | 'hvac'
        | 'hydro'
        | 'load'
        | 'light'
        | 'meter'
        | 'motor'
        | 'other'
        | 'panel'
        | 'powerDistributionUnit'
        | 'rapidShutdownDevice'
        | 'solar'
        | 'solution'
        | 'transferSwitch'
        | 'transformer'
        | 'utility'
        | 'wind'
        | 'cable'
      )[]
    | null;
  visibility?: ('private' | 'public') | null;
  application?: 'dc-microgrid'[] | null;
  reviewed?: boolean | null;
  manufacturer?: (string | null) | Manufacturer;
  teamManufacturer?: string | null;
  distributorsDetails?:
    | {
        distributor?: (string | null) | Manufacturer;
        price?: number | null;
        id?: string | null;
      }[]
    | null;
  distributors?: (string | Manufacturer)[] | null;
  productSeries?: string | null;
  productIdentifier?: string | null;
  msrp?: number | null;
  leadTime?: number | null;
  website?: string | null;
  regionAvailability?: ('EU' | 'US' | 'China' | 'Japan')[] | null;
  lifecycle?: {
    release?: string | null;
    endOfLife?: string | null;
    endOfLifeAlternatives?: (string | Component)[] | null;
  };
  electrical?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  communication?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  environmental?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  performance?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  mechanical?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  files?:
    | {
        file?: (string | null) | File;
        type?:
          | (
              | 'datasheet'
              | 'userManual'
              | 'installationManual'
              | 'applicationNotes'
              | 'operationalManual'
              | 'quickstartGuide'
              | 'characteristics'
              | 'dynamicCharacteristics'
              | 'outputImpedanceCharacteristics'
              | 'certificate'
              | 'other'
            )
          | null;
        visibility?: ('private' | 'public') | null;
        id?: string | null;
      }[]
    | null;
  images?:
    | {
        file?: (string | null) | File;
        type?: ('thumbnail' | 'front' | 'rear' | 'left' | 'right' | 'top' | 'bottom' | 'general' | 'ISOPicture') | null;
        id?: string | null;
      }[]
    | null;
  videos?:
    | {
        video?: string | null;
        id?: string | null;
      }[]
    | null;
  filesVectorStore?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  questions?:
    | {
        question?: string | null;
        answer?: string | null;
        id?: string | null;
      }[]
    | null;
  compliance?: {
    CE?: boolean | null;
    UL?: boolean | null;
    currentOS?: boolean | null;
    emergeAlliance?: boolean | null;
    ODCA?: boolean | null;
    other?: boolean | null;
    otherInput?: string | null;
  };
  standards?: (string | Standard)[] | null;
  archivedAt?: string | null;
  deletedAt?: string | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  publishedBy?: (string | null) | User;
  publishedAt?: string | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  sortRank?: number | null;
  compatibleWith?: (string | Component)[] | null;
  compatibleWithCrossReference?: (string | Component)[] | null;
  compatibleWithPlaceholders?:
    | {
        manufacturer: string;
        productIdentifier: string;
        componentType?:
          | (
              | 'battery'
              | 'capacitor'
              | 'charger'
              | 'breaker'
              | 'bus'
              | 'combinerBox'
              | 'contactor'
              | 'converter'
              | 'custom'
              | 'disconnect'
              | 'fuse'
              | 'generator'
              | 'grounding'
              | 'hvac'
              | 'load'
              | 'light'
              | 'meter'
              | 'motor'
              | 'other'
              | 'panel'
              | 'powerDistributionUnit'
              | 'rapidShutdownDevice'
              | 'solar'
              | 'solution'
              | 'transferSwitch'
              | 'transformer'
              | 'utility'
              | 'hydro'
              | 'wind'
              | 'cable'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  bulkUpload?: boolean | null;
  bulkUploadId?: string | null;
  completeness?: number | null;
  dataSource?: (string | null) | DataSource;
  embeddingText?: string | null;
  embedding?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  specificationsSummary?: string | null;
  manufacturerDetails?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  savedCount?: number | null;
  viewedCount?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "manufacturers".
 */
export interface Manufacturer {
  id: string;
  name: string;
  type?: ('manufacturer' | 'distributor' | 'replus' | 'organization' | 'designer') | null;
  /**
   * A short description of the company that will be used in overviews and teasers.
   */
  description?: string | null;
  /**
   * Must start with a #, eg. #000 or #000000
   */
  color?: string | null;
  /**
   * Group: company:[id]:images eg. company:65b8e1df4923ac5ccb823796:images
   */
  logos?: {
    small?: (string | null) | File;
    large?: (string | null) | File;
  };
  /**
   * Group: company:[id]:images eg. company:65b8e1df4923ac5ccb823796:images
   */
  cover?: (string | null) | File;
  systemSize?: {
    min?: number | null;
    max?: number | null;
  };
  projectBudget?: {
    min?: number | null;
    max?: number | null;
  };
  applicationTags?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  highlights?: StubArray;
  /**
   * Allow users to ask questions about your products in the app. Users will be able to add support users to their projects to ask questions.
   */
  inAppSupport?: boolean | null;
  services?:
    | (
        | 'manufacturing'
        | 'distribution'
        | 'inAppSupport'
        | 'engineering'
        | 'installation'
        | 'integrator'
        | 'engineeringProcurementConstruction'
        | 'organization'
        | 'replus'
        | 'other'
      )[]
    | null;
  otherServices?: string | null;
  serviceTags?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  users?: (string | User)[] | null;
  about?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  mission?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  foundedIn?: string | null;
  companySize?:
    | (
        | 'Self-employed'
        | '1-10 employees'
        | '11-50 employees'
        | '51-200 employees'
        | '201-500 employees'
        | '501-1000 employees'
        | '1001-5000 employees'
        | '5001-10,000 employees'
        | '10,001+ employees'
      )
    | null;
  certificates?: string | null;
  compliance?: {
    currentOS?: boolean | null;
    emergeAlliance?: boolean | null;
    ODCA?: boolean | null;
    other?: boolean | null;
    otherInput?: string | null;
  };
  website?: string | null;
  locations?:
    | {
        name?: string | null;
        description?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        /**
         * Group: company:[id]:images eg. company:65b8e1df4923ac5ccb823796:images
         */
        image?: (string | null) | File;
        address?: {
          name?: string | null;
          street?: string | null;
          number?: string | null;
          postalCode?: string | null;
          city?: string | null;
          state?: string | null;
          country?: string | null;
          /**
           * @minItems 2
           * @maxItems 2
           */
          coordinates?: [number, number] | null;
        };
        contactInformation?: {
          email?: string | null;
          phone?: string | null;
        };
        technicalSupport?: {
          email?: string | null;
          phone?: string | null;
        };
        isHeadquarter?: boolean | null;
        id?: string | null;
      }[]
    | null;
  socials?: {
    linkedin?: string | null;
    facebook?: string | null;
    twitter?: string | null;
    youtube?: string | null;
  };
  contactPeople?:
    | {
        name: string;
        email?: string | null;
        phone?: string | null;
        position?: string | null;
        image?: (string | null) | File;
        id?: string | null;
      }[]
    | null;
  highlightedComponents?: (string | Component)[] | null;
  highlightedProjects?: (string | Project)[] | null;
  partners?:
    | {
        company?: (string | null) | Manufacturer;
        name?: string | null;
        description?: string | null;
        linkedin?: string | null;
        email?: string | null;
        logo?: (string | null) | File;
        id?: string | null;
      }[]
    | null;
  partnersCrossReference?: (string | Manufacturer)[] | null;
  promos?: StubArray;
  files?:
    | {
        file?: (string | null) | File;
        url?: string | null;
        type?:
          | (
              | 'datasheet'
              | 'userManual'
              | 'installationManual'
              | 'applicationNotes'
              | 'operationalManual'
              | 'quickstartGuide'
              | 'characteristics'
              | 'dynamicCharacteristics'
              | 'outputImpedanceCharacteristics'
              | 'certificate'
              | 'other'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  videos?:
    | {
        /**
         * YouTube, Dailymotion etc. video URL
         */
        url: string;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  verified?: boolean | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  internal?: boolean | null;
  publishedBy?: (string | null) | User;
  publishedAt?: string | null;
  /**
   * When the company profile was claimed
   */
  claimedAt?: string | null;
  completeness?: number | null;
  imported?: boolean | null;
  showUpgradeMessage?: boolean | null;
  subscription?: ('company.free' | 'company.premium' | 'company.none') | null;
  admin?: {
    note?: string | null;
  };
  status?: ('draft' | 'review' | 'published') | null;
  embedding?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  embeddingText?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "files".
 */
export interface File {
  id: string;
  name: string;
  group?: string | null;
  subfolder?: string | null;
  createdBy?: (string | null) | User;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  name?: string | null;
  goofy?: string | null;
  email: string;
  profileImage?: (string | null) | File;
  lastActiveDate?: string | null;
  dismissedTips?: ('addComment' | 'linkProduct' | 'inviteCollaborator' | 'downloadImage' | 'companySignup')[] | null;
  completedProgressItems?:
    | ('createProject' | 'inviteTeamUsers' | 'createProfile' | 'createProduct' | 'createReferenceDesign')[]
    | null;
  team?: (string | null) | Team;
  recentProjects?: (string | Project)[] | null;
  measurementSystem?: ('metric' | 'imperial') | null;
  type?: ('designer' | 'manufacturer') | null;
  referrer?: ('replus' | 'reddit') | null;
  adminPanel?: boolean | null;
  internal?: boolean | null;
  developer?: boolean | null;
  flags?:
    | ('aiProductAssistant' | 'intercom' | 'simulation' | 'wireSizing' | 'bulkUpload' | 'quotes' | 'oemSubscription')[]
    | null;
  phone?: string | null;
  source?: string | null;
  signup?: {
    company?: string | null;
    job?: string | null;
    industry?: string | null;
    industryOther?: string | null;
    referral?: string | null;
    systemSize?: {
      min?: number | null;
      max?: number | null;
    };
  };
  dismissedFeatureSuggestions?:
    | {
        feature?: string | null;
        data?: string | null;
        id?: string | null;
      }[]
    | null;
  teamSuggestions?:
    | {
        team?: (string | null) | Team;
        reason?: string | null;
        id?: string | null;
      }[]
    | null;
  teamSuggestionsDismissed?: boolean | null;
  companySuggestions?:
    | {
        company?: (string | null) | Manufacturer;
        reason?: string | null;
        id?: string | null;
      }[]
    | null;
  companySuggestionsDismissed?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "teams".
 */
export interface Team {
  id: string;
  name: string;
  users: {
    user: string | User;
    newPermissions?:
      | (
          | 'component.all'
          | 'component.view'
          | 'component.edit'
          | 'order.all'
          | 'order.review'
          | 'project.all'
          | 'project.admin'
          | 'project.view'
          | 'project.edit'
          | 'project.delete'
          | 'team.owner'
          | 'team.admin'
          | 'team.user'
        )[]
      | null;
    id?: string | null;
  }[];
  inactiveUsers?:
    | {
        user: string | User;
        newPermissions?:
          | (
              | 'component.all'
              | 'component.view'
              | 'component.edit'
              | 'order.all'
              | 'order.review'
              | 'project.all'
              | 'project.admin'
              | 'project.view'
              | 'project.edit'
              | 'project.delete'
              | 'team.owner'
              | 'team.admin'
              | 'team.user'
            )[]
          | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: (string | null) | User;
  stripeCustomerId?: string | null;
  stripeSubscription?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  stripeSubscriptionSchedule?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  subscriptions?:
    | {
        subscription?: ('free' | 'plus' | 'pro' | 'company.free' | 'company.premium' | 'company.none') | null;
        quantity?: number | null;
        id?: string | null;
      }[]
    | null;
  subscriptionBillingCycle?: string | null;
  subscriptionUpdateNotice?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  subscriptionCancelsAt?: number | null;
  voltageClasses?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  interestedIn?: string | null;
  showDesignEditor?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects".
 */
export interface Project {
  id: string;
  name: string;
  /**
   * Group: projects:[id]:images eg. projects:65b8e1df4923ac5ccb823796:images
   */
  thumbnail?: (string | null) | File;
  description?: string | null;
  customer?: string | null;
  tags?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  location?: {
    name?: string | null;
    street?: string | null;
    number?: string | null;
    postalCode?: string | null;
    city?: string | null;
    state?: string | null;
    country?: string | null;
    /**
     * @minItems 2
     * @maxItems 2
     */
    coordinates?: [number, number] | null;
  };
  designs?: (string | ProjectDesign)[] | null;
  collaborators?: {
    users?:
      | {
          user: string | User;
          permissions?: ('project.all' | 'project.admin' | 'project.view' | 'project.edit' | 'project.delete')[] | null;
          id?: string | null;
        }[]
      | null;
    manufacturers?: (string | Manufacturer)[] | null;
  };
  status?: string | null;
  statusUpdates?:
    | {
        status?: ('progress' | 'review' | 'done' | 'archived' | 'deleted') | null;
        date?: string | null;
        user?: (string | null) | User;
        id?: string | null;
      }[]
    | null;
  wireSizing?: {
    standard?: ('IEC' | 'NEC') | null;
    voltageDrop?: number | null;
  };
  files?:
    | {
        file?: (string | null) | File;
        url?: string | null;
        type?:
          | (
              | 'datasheet'
              | 'userManual'
              | 'installationManual'
              | 'applicationNotes'
              | 'operationalManual'
              | 'quickstartGuide'
              | 'characteristics'
              | 'dynamicCharacteristics'
              | 'outputImpedanceCharacteristics'
              | 'certificate'
              | 'other'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  visibility?: ('public' | 'private' | 'marketplace') | null;
  isReferenceDesign?: boolean | null;
  template?: {
    profile?: (string | null) | Manufacturer;
    components?: (string | Component)[] | null;
    indicators?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  reference?: {
    profile?: (string | null) | Manufacturer;
    project?: (string | null) | Project;
  };
  liveDataEnabled?: boolean | null;
  secret?: string | null;
  everyonePermissions?: ('project.all' | 'project.admin' | 'project.view' | 'project.edit' | 'project.delete')[] | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  subscribers?: (string | User)[] | null;
  updatedBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projectDesigns".
 */
export interface ProjectDesign {
  id: string;
  name: string;
  diagrams?: (string | ProjectDesignDiagram)[] | null;
  orders?: (string | Order)[] | null;
  subscribers?: (string | User)[] | null;
  files?:
    | {
        file?: (string | null) | File;
        url?: string | null;
        type?:
          | (
              | 'datasheet'
              | 'userManual'
              | 'installationManual'
              | 'applicationNotes'
              | 'operationalManual'
              | 'quickstartGuide'
              | 'characteristics'
              | 'dynamicCharacteristics'
              | 'outputImpedanceCharacteristics'
              | 'certificate'
              | 'other'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  filesVectorStore?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  createdBy?: (string | null) | User;
  project?: (string | null) | Project;
  archivedAt?: string | null;
  deletedAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projectDesignDiagrams".
 */
export interface ProjectDesignDiagram {
  id: string;
  name: string;
  componentInstances?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  connections?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  communications?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  images?:
    | {
        file?: (string | null) | File;
        position?: {
          x?: number | null;
          y?: number | null;
        };
        dimensions?: {
          width?: number | null;
          height?: number | null;
        };
        id?: string | null;
      }[]
    | null;
  textareas?:
    | {
        position?: {
          x?: number | null;
          y?: number | null;
        };
        dimensions?: {
          width?: number | null;
          height?: number | null;
        };
        color?: string | null;
        fontSize?: ('13px' | '16px' | '20px' | '24px' | '32px') | null;
        borderStyle?: string | null;
        borderColor?: string | null;
        backgroundColor?: string | null;
        content?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  groups?:
    | {
        label?: string | null;
        position: {
          x?: number | null;
          y?: number | null;
        };
        width: number;
        height: number;
        borderStyle?: string | null;
        borderColor?: string | null;
        fillColor?: string | null;
        id?: string | null;
      }[]
    | null;
  voltageClasses?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  createdBy?: (string | null) | User;
  archivedAt?: string | null;
  deletedAt?: string | null;
  project: string | Project;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: string;
  name: string;
  design?: (string | null) | ProjectDesign;
  components?:
    | {
        component: string | Component;
        quantity: number;
        price?: number | null;
        leadTime?: number | null;
        availability?: ('inStock' | 'preorder' | 'backorder' | 'outOfStock') | null;
        notes?: string | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  status?:
    | (
        | 'draft'
        | 'quoteRequested'
        | 'quoteSubmitted'
        | 'orderSubmitted'
        | 'orderAcknowledged'
        | 'orderCancelled'
        | 'orderInProgress'
        | 'orderCompleted'
        | 'archived'
        | 'deleted'
      )
    | null;
  totalPrice?: number | null;
  events?:
    | {
        type:
          | 'draft'
          | 'quoteRequested'
          | 'quoteSubmitted'
          | 'orderSubmitted'
          | 'orderAcknowledged'
          | 'orderCancelled'
          | 'orderInProgress'
          | 'orderCompleted'
          | 'archived'
          | 'deleted';
        date: string;
        user?: (string | null) | User;
        id?: string | null;
      }[]
    | null;
  messages?:
    | {
        message: string;
        date: string;
        user: string | User;
        id?: string | null;
      }[]
    | null;
  files?:
    | {
        file?: (string | null) | File;
        url?: string | null;
        type?:
          | (
              | 'datasheet'
              | 'userManual'
              | 'installationManual'
              | 'applicationNotes'
              | 'operationalManual'
              | 'quickstartGuide'
              | 'characteristics'
              | 'dynamicCharacteristics'
              | 'outputImpedanceCharacteristics'
              | 'certificate'
              | 'other'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  subscribers?: (string | User)[] | null;
  reviewers?: {
    manufacturers?: (string | Manufacturer)[] | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "standards".
 */
export interface Standard {
  id: string;
  name: string;
  createdBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dataSources".
 */
export interface DataSource {
  id: string;
  name: string;
  url?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiembeddings".
 */
export interface Aiembedding {
  id: string;
  collectionType: string;
  collectionRecordId: string;
  type: string;
  embeddingText: string;
  embedding?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "componentFiles".
 */
export interface ComponentFile {
  id: string;
  component?: (string | null) | Component;
  file?: (string | null) | File;
  type?: string | null;
  team: string | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "componentPorts".
 */
export interface ComponentPort {
  id: string;
  component: string | Component;
  index: number;
  type:
    | 'battery'
    | 'bus'
    | 'capacitor'
    | 'breaker'
    | 'charger'
    | 'combinerBox'
    | 'contactor'
    | 'converter'
    | 'custom'
    | 'disconnect'
    | 'fuse'
    | 'generator'
    | 'grounding'
    | 'hvac'
    | 'hydro'
    | 'load'
    | 'light'
    | 'meter'
    | 'motor'
    | 'other'
    | 'panel'
    | 'powerDistributionUnit'
    | 'rapidShutdownDevice'
    | 'solar'
    | 'solution'
    | 'transferSwitch'
    | 'transformer'
    | 'utility'
    | 'wind'
    | 'cable';
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramChatChannels".
 */
export interface DiagramChatChannel {
  id: string;
  name?: string | null;
  project: string | Project;
  type?: ('team' | 'engineering-support' | 'manufacturing-support') | null;
  access?: {
    users?: (string | User)[] | null;
    company?: (string | null) | Manufacturer;
  };
  views?:
    | {
        user?: (string | null) | User;
        date?: string | null;
        id?: string | null;
      }[]
    | null;
  viewed?: boolean | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramChatMessages".
 */
export interface DiagramChatMessage {
  id: string;
  project: string | Project;
  channel: string | DiagramChatChannel;
  parent?: (string | null) | DiagramChatMessage;
  content: {
    content?: string | null;
    files?:
      | {
          file?: (string | null) | File;
          id?: string | null;
        }[]
      | null;
    id?: string | null;
    blockName?: string | null;
    blockType: 'message';
  }[];
  reactions?:
    | {
        reaction?: string | null;
        createdBy?: (string | null) | User;
        createdAt?: string | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: (string | null) | User;
  createdByCompany?: (string | null) | Manufacturer;
  team: string | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramAIMessages".
 */
export interface DiagramAIMessage {
  id: string;
  question?: string | null;
  feedback?: string | null;
  answer?: string | null;
  changes?: {
    applied?: boolean | null;
    reverted?: boolean | null;
    failed?: boolean | null;
    transaction?: (string | null) | Transaction;
  };
  action?: {
    executed?: boolean | null;
    failed?: boolean | null;
    result?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  project: string | Project;
  diagram: string | ProjectDesignDiagram;
  diagramSnapshot:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagramValidations:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  prompt?: {
    system?: string | null;
    user?: string | null;
  };
  status?: ('pending' | 'processing' | 'done' | 'cancelled' | 'error') | null;
  createdBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "transactions".
 */
export interface Transaction {
  id: string;
  diagram: string | ProjectDesignDiagram;
  date: string;
  changes: {
    type: 'create' | 'update' | 'delete';
    data:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    previousData?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    id?: string | null;
  }[];
  createdBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramSimulationProfiles".
 */
export interface DiagramSimulationProfile {
  id: string;
  name?: string | null;
  type?: ('load' | 'generation' | 'combined') | null;
  data?: string | null;
  project: string | Project;
  diagram: string | ProjectDesignDiagram;
  createdBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramLiveData".
 */
export interface DiagramLiveDatum {
  id: string;
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  project: string | Project;
  diagram: string | ProjectDesignDiagram;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramLiveDataAggregated".
 */
export interface DiagramLiveDataAggregated {
  id: string;
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  start?: string | null;
  end?: string | null;
  interval?: ('5 minutes' | '15 minutes' | '1 hour' | '1 day') | null;
  project: string | Project;
  diagram: string | ProjectDesignDiagram;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "intercomChannels".
 */
export interface IntercomChannel {
  id: string;
  name?: string | null;
  type?: ('component' | 'company' | 'project') | null;
  project?: (string | null) | Project;
  company: string | Manufacturer;
  component?: (string | null) | Component;
  access?: {
    company?: (string | null) | Manufacturer;
  };
  views?:
    | {
        user?: (string | null) | User;
        date?: string | null;
        id?: string | null;
      }[]
    | null;
  viewed?: boolean | null;
  createdBy?: (string | null) | User;
  team: string | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "intercomMessages".
 */
export interface IntercomMessage {
  id: string;
  channel: string | IntercomChannel;
  content: {
    content?: string | null;
    files?:
      | {
          file?: (string | null) | File;
          id?: string | null;
        }[]
      | null;
    id?: string | null;
    blockName?: string | null;
    blockType: 'message';
  }[];
  reactions?:
    | {
        reaction?: string | null;
        createdBy?: (string | null) | User;
        createdAt?: string | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: (string | null) | User;
  createdByCompany?: (string | null) | Manufacturer;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramComments".
 */
export interface DiagramComment {
  id: string;
  diagram: string | ProjectDesignDiagram;
  componentInstances?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  position: {
    x: number;
    y: number;
  };
  thread?:
    | (
        | {
            user?: (string | null) | User;
            date?: string | null;
            status: 'resolved' | 'activated' | 'deleted';
            id?: string | null;
            blockName?: string | null;
            blockType: 'status';
          }
        | {
            user?: (string | null) | User;
            date?: string | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'view';
          }
        | {
            user?: (string | null) | User;
            manufacturer?: (string | null) | Manufacturer;
            date?: string | null;
            content?: string | null;
            files?:
              | {
                  file?: (string | null) | File;
                  id?: string | null;
                }[]
              | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            date?: string | null;
            content?: string | null;
            json?:
              | {
                  [k: string]: unknown;
                }
              | unknown[]
              | string
              | number
              | boolean
              | null;
            generating?: boolean | null;
            error?: string | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'ai';
          }
      )[]
    | null;
  createdBy?: (string | null) | User;
  subscribers?: (string | User)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "formSubmissions".
 */
export interface FormSubmission {
  id: string;
  type?: string | null;
  name?: string | null;
  content?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "notifications".
 */
export interface Notification {
  id: string;
  from?: (string | null) | User;
  to: string | User;
  type?: string | null;
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  cache?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  readAt?: string | null;
  handledAt?: string | null;
  broken?: boolean | null;
  deletedAt?: string | null;
  team: string | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "loginCodes".
 */
export interface LoginCode {
  id: string;
  email: string;
  token: string;
  code: string;
  expiresAt: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "billOfMaterials".
 */
export interface BillOfMaterial {
  id: string;
  projectDesign: string | ProjectDesign;
  project: string | Project;
  components?:
    | {
        component?: (string | null) | Component;
        componentInstances?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        quantity?: number | null;
        price?: number | null;
        furnishedBy?: string | null;
        installedBy?: string | null;
        notes?: string | null;
        link?: string | null;
        /**
         * Custom field values
         */
        customFields?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  componentInstances?:
    | {
        componentInstance?: string | null;
        quantity?: number | null;
        price?: number | null;
        furnishedBy?: string | null;
        installedBy?: string | null;
        notes?: string | null;
        link?: string | null;
        /**
         * Custom field values
         */
        customFields?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  groups?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  totalPrice?: number | null;
  /**
   * Custom field definitions
   */
  customFields?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "stripeRequests".
 */
export interface StripeRequest {
  id: string;
  eventId?: string | null;
  createdAt: string;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "simulations".
 */
export interface Simulation {
  id: string;
  name: string;
  execution?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagram: string | ProjectDesignDiagram;
  project: string | Project;
  team: string | Team;
  createdBy?: (string | null) | User;
  deletedAt?: string | null;
  status: 'pending' | 'processing' | 'done' | 'error';
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  result?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagramSnapshot?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  file?: (string | null) | File;
  type?: ('steady_state' | 'quasi_steady_state') | null;
  profiles?: (string | DiagramSimulationProfile)[] | null;
  nbTimestamps?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faultSimulations".
 */
export interface FaultSimulation {
  id: string;
  name: string;
  execution?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagram: string | ProjectDesignDiagram;
  project: string | Project;
  team: string | Team;
  createdBy?: (string | null) | User;
  deletedAt?: string | null;
  status: 'pending' | 'processing' | 'done' | 'error';
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  result?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagramSnapshot?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  nbTimestamps?: number | null;
  faultConnectionId: string;
  faultTime: number;
  faultResistance: number;
  faultLocation: number;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "wireSizings".
 */
export interface WireSizing {
  id: string;
  name: string;
  execution?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagram: string | ProjectDesignDiagram;
  project: string | Project;
  team: string | Team;
  createdBy?: (string | null) | User;
  deletedAt?: string | null;
  status: 'pending' | 'processing' | 'done' | 'error';
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  result?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  diagramSnapshot?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "jobLogs".
 */
export interface JobLog {
  id: string;
  job?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  parentJob:
    | {
        relationTo: 'simulations';
        value: string | Simulation;
      }
    | {
        relationTo: 'wireSizings';
        value: string | WireSizing;
      };
  type: string;
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  message: string;
  execution?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "savedItems".
 */
export interface SavedItem {
  id: string;
  user: string | User;
  item:
    | {
        relationTo: 'components';
        value: string | Component;
      }
    | {
        relationTo: 'manufacturers';
        value: string | Manufacturer;
      }
    | {
        relationTo: 'projects';
        value: string | Project;
      };
  createdAt: string;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: string;
  name: string;
  slug: string;
  isActive?: boolean | null;
  start: string;
  start_tz: SupportedTimezones;
  end: string;
  end_tz: SupportedTimezones;
  companies?:
    | {
        company: string | Manufacturer;
        booth?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "views".
 */
export interface View {
  id: string;
  page:
    | {
        relationTo: 'projects';
        value: string | Project;
      }
    | {
        relationTo: 'manufacturers';
        value: string | Manufacturer;
      }
    | {
        relationTo: 'components';
        value: string | Component;
      };
  createdBy?: (string | null) | User;
  createdAt: string;
  team: string | Team;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles".
 */
export interface Article {
  id: string;
  name: string;
  type?: ('caseStudy' | 'promotion') | null;
  file?: (string | null) | File;
  teaser?: {
    description?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    button?: {
      label?: string | null;
      url?: string | null;
    };
    image?: (string | null) | File;
  };
  embeddingText?: string | null;
  embedding?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  company?: (string | null) | Manufacturer;
  createdAt: string;
  createdBy?: (string | null) | User;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "companyImports".
 */
export interface CompanyImport {
  id: string;
  name: string;
  linkedin: string;
  status: 'pending' | 'imported' | 'archived' | 'ignored' | 'failed';
  imported?: (string | null) | Manufacturer;
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  notes?: string | null;
  createdBy?: (string | null) | User;
  createdAt: string;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "searchThreads".
 */
export interface SearchThread {
  id: string;
  thread?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  email?: string | null;
  exhibitorMatch?: (string | null) | ExhibitorMatch;
  internal?: boolean | null;
  event?: (string | null) | Event;
  createdBy?: (string | null) | User;
  createdAt: string;
  deletedAt?: string | null;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitorMatches".
 */
export interface ExhibitorMatch {
  id: string;
  thread?: (string | null) | SearchThread;
  summarizeMatchResult?: string | null;
  requirements?: string | null;
  productSearchResults?: (string | Component)[] | null;
  companySearchResults?: (string | Manufacturer)[] | null;
  caseStudiesResults?: (string | Article)[] | null;
  internetProductResults?: string | null;
  sentToCompanies?:
    | {
        company?: (string | null) | Manufacturer;
        lead?: (string | null) | ExhibitorMatchLead;
        id?: string | null;
      }[]
    | null;
  event?: (string | null) | Event;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitorMatchLeads".
 */
export interface ExhibitorMatchLead {
  id: string;
  exhibitorMatch: string | ExhibitorMatch;
  company: string | Manufacturer;
  requirements?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  email?: string | null;
  user?: (string | null) | User;
  event?: (string | null) | Event;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "emails".
 */
export interface Email {
  id: string;
  type: string;
  email: string;
  user?: (string | null) | User;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "userInvitations".
 */
export interface UserInvitation {
  id: string;
  email?: string | null;
  user?: (string | null) | User;
  message?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  status?: ('pending' | 'accepted') | null;
  references: {
    exhibitorMatchLead: string | ExhibitorMatchLead;
    company: string | Manufacturer;
  };
  createdBy?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "qrScans".
 */
export interface QrScan {
  id: string;
  user?: (string | null) | User;
  userInfo?: {
    name?: string | null;
    email?: string | null;
  };
  company: string | Manufacturer;
  event?: (string | null) | Event;
  contact?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  note?: string | null;
  noteCategories?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  createdAt: string;
  deletedAt?: string | null;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "qrScansAISummaries".
 */
export interface QrScansAISummary {
  id: string;
  type: 'summarize' | 'followUp' | 'popularTopics' | 'noteSummary' | 'visitorNotes';
  summary: string;
  company?: (string | null) | Manufacturer;
  event?: (string | null) | Event;
  user?: (string | null) | User;
  email?: string | null;
  /**
   * SHA-256 hash of the QR scan data used to generate this summary
   */
  dataHash: string;
  createdBy?: (string | null) | User;
  team?: (string | null) | Team;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'aiSuggestions';
        value: string | AiSuggestion;
      } | null)
    | ({
        relationTo: 'aiMessages';
        value: string | AiMessage;
      } | null)
    | ({
        relationTo: 'aiembeddings';
        value: string | Aiembedding;
      } | null)
    | ({
        relationTo: 'components';
        value: string | Component;
      } | null)
    | ({
        relationTo: 'componentFiles';
        value: string | ComponentFile;
      } | null)
    | ({
        relationTo: 'componentPorts';
        value: string | ComponentPort;
      } | null)
    | ({
        relationTo: 'diagramChatChannels';
        value: string | DiagramChatChannel;
      } | null)
    | ({
        relationTo: 'diagramChatMessages';
        value: string | DiagramChatMessage;
      } | null)
    | ({
        relationTo: 'diagramAIMessages';
        value: string | DiagramAIMessage;
      } | null)
    | ({
        relationTo: 'diagramSimulationProfiles';
        value: string | DiagramSimulationProfile;
      } | null)
    | ({
        relationTo: 'diagramLiveData';
        value: string | DiagramLiveDatum;
      } | null)
    | ({
        relationTo: 'diagramLiveDataAggregated';
        value: string | DiagramLiveDataAggregated;
      } | null)
    | ({
        relationTo: 'intercomChannels';
        value: string | IntercomChannel;
      } | null)
    | ({
        relationTo: 'intercomMessages';
        value: string | IntercomMessage;
      } | null)
    | ({
        relationTo: 'manufacturers';
        value: string | Manufacturer;
      } | null)
    | ({
        relationTo: 'projects';
        value: string | Project;
      } | null)
    | ({
        relationTo: 'projectDesigns';
        value: string | ProjectDesign;
      } | null)
    | ({
        relationTo: 'projectDesignDiagrams';
        value: string | ProjectDesignDiagram;
      } | null)
    | ({
        relationTo: 'diagramComments';
        value: string | DiagramComment;
      } | null)
    | ({
        relationTo: 'teams';
        value: string | Team;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'files';
        value: string | File;
      } | null)
    | ({
        relationTo: 'formSubmissions';
        value: string | FormSubmission;
      } | null)
    | ({
        relationTo: 'standards';
        value: string | Standard;
      } | null)
    | ({
        relationTo: 'transactions';
        value: string | Transaction;
      } | null)
    | ({
        relationTo: 'orders';
        value: string | Order;
      } | null)
    | ({
        relationTo: 'notifications';
        value: string | Notification;
      } | null)
    | ({
        relationTo: 'loginCodes';
        value: string | LoginCode;
      } | null)
    | ({
        relationTo: 'billOfMaterials';
        value: string | BillOfMaterial;
      } | null)
    | ({
        relationTo: 'stripeRequests';
        value: string | StripeRequest;
      } | null)
    | ({
        relationTo: 'simulations';
        value: string | Simulation;
      } | null)
    | ({
        relationTo: 'faultSimulations';
        value: string | FaultSimulation;
      } | null)
    | ({
        relationTo: 'wireSizings';
        value: string | WireSizing;
      } | null)
    | ({
        relationTo: 'jobLogs';
        value: string | JobLog;
      } | null)
    | ({
        relationTo: 'dataSources';
        value: string | DataSource;
      } | null)
    | ({
        relationTo: 'savedItems';
        value: string | SavedItem;
      } | null)
    | ({
        relationTo: 'events';
        value: string | Event;
      } | null)
    | ({
        relationTo: 'views';
        value: string | View;
      } | null)
    | ({
        relationTo: 'articles';
        value: string | Article;
      } | null)
    | ({
        relationTo: 'companyImports';
        value: string | CompanyImport;
      } | null)
    | ({
        relationTo: 'searchThreads';
        value: string | SearchThread;
      } | null)
    | ({
        relationTo: 'exhibitorMatches';
        value: string | ExhibitorMatch;
      } | null)
    | ({
        relationTo: 'exhibitorMatchLeads';
        value: string | ExhibitorMatchLead;
      } | null)
    | ({
        relationTo: 'emails';
        value: string | Email;
      } | null)
    | ({
        relationTo: 'userInvitations';
        value: string | UserInvitation;
      } | null)
    | ({
        relationTo: 'qrScans';
        value: string | QrScan;
      } | null)
    | ({
        relationTo: 'qrScansAISummaries';
        value: string | QrScansAISummary;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiSuggestions_select".
 */
export interface AiSuggestionsSelect<T extends boolean = true> {
  types?: T;
  suggestion?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiMessages_select".
 */
export interface AiMessagesSelect<T extends boolean = true> {
  id?: T;
  question?: T;
  answer?: T;
  references?: T;
  rating?: T;
  component?: T;
  project?: T;
  createdBy?: T;
  team?: T;
  deletedAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aiembeddings_select".
 */
export interface AiembeddingsSelect<T extends boolean = true> {
  collectionType?: T;
  collectionRecordId?: T;
  type?: T;
  embeddingText?: T;
  embedding?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "components_select".
 */
export interface ComponentsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  type?: T;
  canServeAs?: T;
  visibility?: T;
  application?: T;
  reviewed?: T;
  manufacturer?: T;
  teamManufacturer?: T;
  distributorsDetails?:
    | T
    | {
        distributor?: T;
        price?: T;
        id?: T;
      };
  distributors?: T;
  productSeries?: T;
  productIdentifier?: T;
  msrp?: T;
  leadTime?: T;
  website?: T;
  regionAvailability?: T;
  lifecycle?:
    | T
    | {
        release?: T;
        endOfLife?: T;
        endOfLifeAlternatives?: T;
      };
  electrical?: T;
  communication?: T;
  environmental?: T;
  performance?: T;
  mechanical?: T;
  files?:
    | T
    | {
        file?: T;
        type?: T;
        visibility?: T;
        id?: T;
      };
  images?:
    | T
    | {
        file?: T;
        type?: T;
        id?: T;
      };
  videos?:
    | T
    | {
        video?: T;
        id?: T;
      };
  filesVectorStore?: T;
  questions?:
    | T
    | {
        question?: T;
        answer?: T;
        id?: T;
      };
  compliance?:
    | T
    | {
        CE?: T;
        UL?: T;
        currentOS?: T;
        emergeAlliance?: T;
        ODCA?: T;
        other?: T;
        otherInput?: T;
      };
  standards?: T;
  archivedAt?: T;
  deletedAt?: T;
  createdBy?: T;
  team?: T;
  publishedBy?: T;
  publishedAt?: T;
  metadata?: T;
  sortRank?: T;
  compatibleWith?: T;
  compatibleWithCrossReference?: T;
  compatibleWithPlaceholders?:
    | T
    | {
        manufacturer?: T;
        productIdentifier?: T;
        componentType?: T;
        id?: T;
      };
  bulkUpload?: T;
  bulkUploadId?: T;
  completeness?: T;
  dataSource?: T;
  embeddingText?: T;
  embedding?: T;
  specificationsSummary?: T;
  manufacturerDetails?: T;
  savedCount?: T;
  viewedCount?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "componentFiles_select".
 */
export interface ComponentFilesSelect<T extends boolean = true> {
  component?: T;
  file?: T;
  type?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "componentPorts_select".
 */
export interface ComponentPortsSelect<T extends boolean = true> {
  component?: T;
  index?: T;
  type?: T;
  data?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramChatChannels_select".
 */
export interface DiagramChatChannelsSelect<T extends boolean = true> {
  name?: T;
  project?: T;
  type?: T;
  access?:
    | T
    | {
        users?: T;
        company?: T;
      };
  views?:
    | T
    | {
        user?: T;
        date?: T;
        id?: T;
      };
  viewed?: T;
  createdBy?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramChatMessages_select".
 */
export interface DiagramChatMessagesSelect<T extends boolean = true> {
  project?: T;
  channel?: T;
  parent?: T;
  content?:
    | T
    | {
        message?:
          | T
          | {
              content?: T;
              files?:
                | T
                | {
                    file?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
      };
  reactions?:
    | T
    | {
        reaction?: T;
        createdBy?: T;
        createdAt?: T;
        id?: T;
      };
  createdBy?: T;
  createdByCompany?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramAIMessages_select".
 */
export interface DiagramAIMessagesSelect<T extends boolean = true> {
  question?: T;
  feedback?: T;
  answer?: T;
  changes?:
    | T
    | {
        applied?: T;
        reverted?: T;
        failed?: T;
        transaction?: T;
      };
  action?:
    | T
    | {
        executed?: T;
        failed?: T;
        result?: T;
      };
  project?: T;
  diagram?: T;
  diagramSnapshot?: T;
  diagramValidations?: T;
  prompt?:
    | T
    | {
        system?: T;
        user?: T;
      };
  status?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramSimulationProfiles_select".
 */
export interface DiagramSimulationProfilesSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  data?: T;
  project?: T;
  diagram?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramLiveData_select".
 */
export interface DiagramLiveDataSelect<T extends boolean = true> {
  data?: T;
  project?: T;
  diagram?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramLiveDataAggregated_select".
 */
export interface DiagramLiveDataAggregatedSelect<T extends boolean = true> {
  data?: T;
  start?: T;
  end?: T;
  interval?: T;
  project?: T;
  diagram?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "intercomChannels_select".
 */
export interface IntercomChannelsSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  project?: T;
  company?: T;
  component?: T;
  access?:
    | T
    | {
        company?: T;
      };
  views?:
    | T
    | {
        user?: T;
        date?: T;
        id?: T;
      };
  viewed?: T;
  createdBy?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "intercomMessages_select".
 */
export interface IntercomMessagesSelect<T extends boolean = true> {
  channel?: T;
  content?:
    | T
    | {
        message?:
          | T
          | {
              content?: T;
              files?:
                | T
                | {
                    file?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
      };
  reactions?:
    | T
    | {
        reaction?: T;
        createdBy?: T;
        createdAt?: T;
        id?: T;
      };
  createdBy?: T;
  createdByCompany?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "manufacturers_select".
 */
export interface ManufacturersSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  description?: T;
  color?: T;
  logos?:
    | T
    | {
        small?: T;
        large?: T;
      };
  cover?: T;
  systemSize?:
    | T
    | {
        min?: T;
        max?: T;
      };
  projectBudget?:
    | T
    | {
        min?: T;
        max?: T;
      };
  applicationTags?: T;
  highlights?: T | StubArraySelect<T>;
  inAppSupport?: T;
  services?: T;
  otherServices?: T;
  serviceTags?: T;
  users?: T;
  about?: T;
  mission?: T;
  foundedIn?: T;
  companySize?: T;
  certificates?: T;
  compliance?:
    | T
    | {
        currentOS?: T;
        emergeAlliance?: T;
        ODCA?: T;
        other?: T;
        otherInput?: T;
      };
  website?: T;
  locations?:
    | T
    | {
        name?: T;
        description?: T;
        image?: T;
        address?:
          | T
          | {
              name?: T;
              street?: T;
              number?: T;
              postalCode?: T;
              city?: T;
              state?: T;
              country?: T;
              coordinates?: T;
            };
        contactInformation?:
          | T
          | {
              email?: T;
              phone?: T;
            };
        technicalSupport?:
          | T
          | {
              email?: T;
              phone?: T;
            };
        isHeadquarter?: T;
        id?: T;
      };
  socials?:
    | T
    | {
        linkedin?: T;
        facebook?: T;
        twitter?: T;
        youtube?: T;
      };
  contactPeople?:
    | T
    | {
        name?: T;
        email?: T;
        phone?: T;
        position?: T;
        image?: T;
        id?: T;
      };
  highlightedComponents?: T;
  highlightedProjects?: T;
  partners?:
    | T
    | {
        company?: T;
        name?: T;
        description?: T;
        linkedin?: T;
        email?: T;
        logo?: T;
        id?: T;
      };
  partnersCrossReference?: T;
  promos?: T | StubArraySelect<T>;
  files?:
    | T
    | {
        file?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  videos?:
    | T
    | {
        url?: T;
        description?: T;
        id?: T;
      };
  slug?: T;
  verified?: T;
  createdBy?: T;
  team?: T;
  internal?: T;
  publishedBy?: T;
  publishedAt?: T;
  claimedAt?: T;
  completeness?: T;
  imported?: T;
  showUpgradeMessage?: T;
  subscription?: T;
  admin?:
    | T
    | {
        note?: T;
      };
  status?: T;
  embedding?: T;
  embeddingText?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "StubArray_select".
 */
export interface StubArraySelect<T extends boolean = true> {
  name?: T;
  description?: T;
  button?:
    | T
    | {
        label?: T;
        url?: T;
      };
  image?: T;
  imageCrop?: T;
  files?:
    | T
    | {
        file?: T;
        id?: T;
      };
  tags?: T;
  createdAt?: T;
  id?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects_select".
 */
export interface ProjectsSelect<T extends boolean = true> {
  name?: T;
  thumbnail?: T;
  description?: T;
  customer?: T;
  tags?: T;
  location?:
    | T
    | {
        name?: T;
        street?: T;
        number?: T;
        postalCode?: T;
        city?: T;
        state?: T;
        country?: T;
        coordinates?: T;
      };
  designs?: T;
  collaborators?:
    | T
    | {
        users?:
          | T
          | {
              user?: T;
              permissions?: T;
              id?: T;
            };
        manufacturers?: T;
      };
  status?: T;
  statusUpdates?:
    | T
    | {
        status?: T;
        date?: T;
        user?: T;
        id?: T;
      };
  wireSizing?:
    | T
    | {
        standard?: T;
        voltageDrop?: T;
      };
  files?:
    | T
    | {
        file?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  visibility?: T;
  isReferenceDesign?: T;
  template?:
    | T
    | {
        profile?: T;
        components?: T;
        indicators?: T;
      };
  reference?:
    | T
    | {
        profile?: T;
        project?: T;
      };
  liveDataEnabled?: T;
  secret?: T;
  everyonePermissions?: T;
  createdBy?: T;
  team?: T;
  subscribers?: T;
  updatedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projectDesigns_select".
 */
export interface ProjectDesignsSelect<T extends boolean = true> {
  name?: T;
  diagrams?: T;
  orders?: T;
  subscribers?: T;
  files?:
    | T
    | {
        file?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  filesVectorStore?: T;
  createdBy?: T;
  project?: T;
  archivedAt?: T;
  deletedAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projectDesignDiagrams_select".
 */
export interface ProjectDesignDiagramsSelect<T extends boolean = true> {
  name?: T;
  componentInstances?: T;
  connections?: T;
  communications?: T;
  images?:
    | T
    | {
        file?: T;
        position?:
          | T
          | {
              x?: T;
              y?: T;
            };
        dimensions?:
          | T
          | {
              width?: T;
              height?: T;
            };
        id?: T;
      };
  textareas?:
    | T
    | {
        position?:
          | T
          | {
              x?: T;
              y?: T;
            };
        dimensions?:
          | T
          | {
              width?: T;
              height?: T;
            };
        color?: T;
        fontSize?: T;
        borderStyle?: T;
        borderColor?: T;
        backgroundColor?: T;
        content?: T;
        id?: T;
      };
  groups?:
    | T
    | {
        label?: T;
        position?:
          | T
          | {
              x?: T;
              y?: T;
            };
        width?: T;
        height?: T;
        borderStyle?: T;
        borderColor?: T;
        fillColor?: T;
        id?: T;
      };
  voltageClasses?: T;
  createdBy?: T;
  archivedAt?: T;
  deletedAt?: T;
  project?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "diagramComments_select".
 */
export interface DiagramCommentsSelect<T extends boolean = true> {
  diagram?: T;
  componentInstances?: T;
  position?:
    | T
    | {
        x?: T;
        y?: T;
      };
  thread?:
    | T
    | {
        status?:
          | T
          | {
              user?: T;
              date?: T;
              status?: T;
              id?: T;
              blockName?: T;
            };
        view?:
          | T
          | {
              user?: T;
              date?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              user?: T;
              manufacturer?: T;
              date?: T;
              content?: T;
              files?:
                | T
                | {
                    file?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
        ai?:
          | T
          | {
              date?: T;
              content?: T;
              json?: T;
              generating?: T;
              error?: T;
              id?: T;
              blockName?: T;
            };
      };
  createdBy?: T;
  subscribers?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "teams_select".
 */
export interface TeamsSelect<T extends boolean = true> {
  name?: T;
  users?:
    | T
    | {
        user?: T;
        newPermissions?: T;
        id?: T;
      };
  inactiveUsers?:
    | T
    | {
        user?: T;
        newPermissions?: T;
        id?: T;
      };
  createdBy?: T;
  stripeCustomerId?: T;
  stripeSubscription?: T;
  stripeSubscriptionSchedule?: T;
  subscriptions?:
    | T
    | {
        subscription?: T;
        quantity?: T;
        id?: T;
      };
  subscriptionBillingCycle?: T;
  subscriptionUpdateNotice?: T;
  subscriptionCancelsAt?: T;
  voltageClasses?: T;
  interestedIn?: T;
  showDesignEditor?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  goofy?: T;
  email?: T;
  profileImage?: T;
  lastActiveDate?: T;
  dismissedTips?: T;
  completedProgressItems?: T;
  team?: T;
  recentProjects?: T;
  measurementSystem?: T;
  type?: T;
  referrer?: T;
  adminPanel?: T;
  internal?: T;
  developer?: T;
  flags?: T;
  phone?: T;
  source?: T;
  signup?:
    | T
    | {
        company?: T;
        job?: T;
        industry?: T;
        industryOther?: T;
        referral?: T;
        systemSize?:
          | T
          | {
              min?: T;
              max?: T;
            };
      };
  dismissedFeatureSuggestions?:
    | T
    | {
        feature?: T;
        data?: T;
        id?: T;
      };
  teamSuggestions?:
    | T
    | {
        team?: T;
        reason?: T;
        id?: T;
      };
  teamSuggestionsDismissed?: T;
  companySuggestions?:
    | T
    | {
        company?: T;
        reason?: T;
        id?: T;
      };
  companySuggestionsDismissed?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "files_select".
 */
export interface FilesSelect<T extends boolean = true> {
  name?: T;
  group?: T;
  subfolder?: T;
  createdBy?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "formSubmissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  type?: T;
  name?: T;
  content?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "standards_select".
 */
export interface StandardsSelect<T extends boolean = true> {
  name?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "transactions_select".
 */
export interface TransactionsSelect<T extends boolean = true> {
  id?: T;
  diagram?: T;
  date?: T;
  changes?:
    | T
    | {
        type?: T;
        data?: T;
        previousData?: T;
        id?: T;
      };
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders_select".
 */
export interface OrdersSelect<T extends boolean = true> {
  name?: T;
  design?: T;
  components?:
    | T
    | {
        component?: T;
        quantity?: T;
        price?: T;
        leadTime?: T;
        availability?: T;
        notes?: T;
        id?: T;
      };
  createdBy?: T;
  team?: T;
  status?: T;
  totalPrice?: T;
  events?:
    | T
    | {
        type?: T;
        date?: T;
        user?: T;
        id?: T;
      };
  messages?:
    | T
    | {
        message?: T;
        date?: T;
        user?: T;
        id?: T;
      };
  files?:
    | T
    | {
        file?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  subscribers?: T;
  reviewers?:
    | T
    | {
        manufacturers?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "notifications_select".
 */
export interface NotificationsSelect<T extends boolean = true> {
  from?: T;
  to?: T;
  type?: T;
  data?: T;
  cache?: T;
  readAt?: T;
  handledAt?: T;
  broken?: T;
  deletedAt?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "loginCodes_select".
 */
export interface LoginCodesSelect<T extends boolean = true> {
  email?: T;
  token?: T;
  code?: T;
  expiresAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "billOfMaterials_select".
 */
export interface BillOfMaterialsSelect<T extends boolean = true> {
  projectDesign?: T;
  project?: T;
  components?:
    | T
    | {
        component?: T;
        componentInstances?: T;
        quantity?: T;
        price?: T;
        furnishedBy?: T;
        installedBy?: T;
        notes?: T;
        link?: T;
        customFields?: T;
        id?: T;
      };
  componentInstances?:
    | T
    | {
        componentInstance?: T;
        quantity?: T;
        price?: T;
        furnishedBy?: T;
        installedBy?: T;
        notes?: T;
        link?: T;
        customFields?: T;
        id?: T;
      };
  groups?: T;
  totalPrice?: T;
  customFields?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "stripeRequests_select".
 */
export interface StripeRequestsSelect<T extends boolean = true> {
  eventId?: T;
  createdAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "simulations_select".
 */
export interface SimulationsSelect<T extends boolean = true> {
  name?: T;
  execution?: T;
  diagram?: T;
  project?: T;
  team?: T;
  createdBy?: T;
  deletedAt?: T;
  status?: T;
  error?: T;
  result?: T;
  diagramSnapshot?: T;
  file?: T;
  type?: T;
  profiles?: T;
  nbTimestamps?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faultSimulations_select".
 */
export interface FaultSimulationsSelect<T extends boolean = true> {
  name?: T;
  execution?: T;
  diagram?: T;
  project?: T;
  team?: T;
  createdBy?: T;
  deletedAt?: T;
  status?: T;
  error?: T;
  result?: T;
  diagramSnapshot?: T;
  nbTimestamps?: T;
  faultConnectionId?: T;
  faultTime?: T;
  faultResistance?: T;
  faultLocation?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "wireSizings_select".
 */
export interface WireSizingsSelect<T extends boolean = true> {
  name?: T;
  execution?: T;
  diagram?: T;
  project?: T;
  team?: T;
  createdBy?: T;
  deletedAt?: T;
  status?: T;
  error?: T;
  result?: T;
  diagramSnapshot?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "jobLogs_select".
 */
export interface JobLogsSelect<T extends boolean = true> {
  job?: T;
  parentJob?: T;
  type?: T;
  data?: T;
  message?: T;
  execution?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dataSources_select".
 */
export interface DataSourcesSelect<T extends boolean = true> {
  name?: T;
  url?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "savedItems_select".
 */
export interface SavedItemsSelect<T extends boolean = true> {
  user?: T;
  item?: T;
  createdAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  isActive?: T;
  start?: T;
  start_tz?: T;
  end?: T;
  end_tz?: T;
  companies?:
    | T
    | {
        company?: T;
        booth?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "views_select".
 */
export interface ViewsSelect<T extends boolean = true> {
  page?: T;
  createdBy?: T;
  createdAt?: T;
  team?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles_select".
 */
export interface ArticlesSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  file?: T;
  teaser?:
    | T
    | {
        description?: T;
        button?:
          | T
          | {
              label?: T;
              url?: T;
            };
        image?: T;
      };
  embeddingText?: T;
  embedding?: T;
  company?: T;
  createdAt?: T;
  createdBy?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "companyImports_select".
 */
export interface CompanyImportsSelect<T extends boolean = true> {
  name?: T;
  linkedin?: T;
  status?: T;
  imported?: T;
  data?: T;
  notes?: T;
  createdBy?: T;
  createdAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "searchThreads_select".
 */
export interface SearchThreadsSelect<T extends boolean = true> {
  thread?: T;
  email?: T;
  exhibitorMatch?: T;
  internal?: T;
  event?: T;
  createdBy?: T;
  createdAt?: T;
  deletedAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitorMatches_select".
 */
export interface ExhibitorMatchesSelect<T extends boolean = true> {
  thread?: T;
  summarizeMatchResult?: T;
  requirements?: T;
  productSearchResults?: T;
  companySearchResults?: T;
  caseStudiesResults?: T;
  internetProductResults?: T;
  sentToCompanies?:
    | T
    | {
        company?: T;
        lead?: T;
        id?: T;
      };
  event?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitorMatchLeads_select".
 */
export interface ExhibitorMatchLeadsSelect<T extends boolean = true> {
  exhibitorMatch?: T;
  company?: T;
  requirements?: T;
  email?: T;
  user?: T;
  event?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "emails_select".
 */
export interface EmailsSelect<T extends boolean = true> {
  type?: T;
  email?: T;
  user?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "userInvitations_select".
 */
export interface UserInvitationsSelect<T extends boolean = true> {
  email?: T;
  user?: T;
  message?: T;
  status?: T;
  references?:
    | T
    | {
        exhibitorMatchLead?: T;
        company?: T;
      };
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "qrScans_select".
 */
export interface QrScansSelect<T extends boolean = true> {
  user?: T;
  userInfo?:
    | T
    | {
        name?: T;
        email?: T;
      };
  company?: T;
  event?: T;
  contact?: T;
  note?: T;
  noteCategories?: T;
  createdAt?: T;
  deletedAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "qrScansAISummaries_select".
 */
export interface QrScansAISummariesSelect<T extends boolean = true> {
  type?: T;
  summary?: T;
  company?: T;
  event?: T;
  user?: T;
  email?: T;
  dataHash?: T;
  createdBy?: T;
  team?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "general".
 */
export interface General {
  id: string;
  reference?: {
    projects?: (string | Project)[] | null;
  };
  supportTeam?: (string | null) | Manufacturer;
  /**
   * The profile that will be used as an example for new users.
   */
  exampleProfile?: (string | null) | Manufacturer;
  highlights?: {
    profiles?: (string | Manufacturer)[] | null;
    caseStudies?: (string | Article)[] | null;
  };
  /**
   * This message will be shown to all users above the header until they dismiss it. Use it to inform users of maintenance or other important information.
   */
  bannerMessage?: string | null;
  /**
   * This message will be shown when the site is in maintenance mode. Use it to inform users of when the site will be back online.
   */
  maintenanceMessage?: string | null;
  /**
   * This message will be shown to all users in the sidebar until they dismiss it. Use it to inform users of new features or updates.
   */
  updateMessage?: {
    title?: string | null;
    description?: string | null;
    label?: string | null;
    url?: string | null;
  };
  statsProductUsage?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  featuredProducts: (string | Component)[];
  qrScanNoteCategories?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sitemap".
 */
export interface Sitemap {
  id: string;
  products?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  profiles?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "general_select".
 */
export interface GeneralSelect<T extends boolean = true> {
  reference?:
    | T
    | {
        projects?: T;
      };
  supportTeam?: T;
  exampleProfile?: T;
  highlights?:
    | T
    | {
        profiles?: T;
        caseStudies?: T;
      };
  bannerMessage?: T;
  maintenanceMessage?: T;
  updateMessage?:
    | T
    | {
        title?: T;
        description?: T;
        label?: T;
        url?: T;
      };
  statsProductUsage?: T;
  featuredProducts?: T;
  qrScanNoteCategories?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sitemap_select".
 */
export interface SitemapSelect<T extends boolean = true> {
  products?: T;
  profiles?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}