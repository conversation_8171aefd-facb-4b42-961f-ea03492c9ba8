import { EmailService } from '@/services';

import { designUserReminderXDays } from '@/collections/emails/reminders/design-user-reminder-x-days';
import { generateCronEndpoint } from '@/endpoints/crons/generate-cron-endpoint';

const automailer = generateCronEndpoint({
    subPath: '/automailer',
    method: 'get',
    handler: async (request) => {
        const { payload } = request;

        const emails = [
            designUserReminderXDays(2 * 7), // 2 weeks
            designUserReminderXDays(8 * 7), // 8 weeks
            designUserReminderXDays(3 * 30), // 3 months
            designUserReminderXDays(6 * 30), // 6 months
        ];

        let sendCount = 0;

        for (const mail of emails) {
            for (const candidate of await mail.candidates()) {
                const type = `automailer.${mail.key}`;

                const {
                    docs: [harassed],
                } = await payload.find({
                    collection: 'emails',
                    where: {
                        type: { equals: type },
                        user: { equals: candidate.id },
                    },
                    limit: 1,
                });

                if (!harassed) {
                    sendCount += 1;

                    await EmailService.send({
                        to: candidate.email,
                        replyTo: '<EMAIL>',
                        data: await mail.email(candidate),
                        tracking: {
                            type: type,
                            user: candidate.id,
                        },
                    });
                }
            }
        }

        return Response.json({
            success: true,
            sendCount,
        });
    },
});

export { automailer };
