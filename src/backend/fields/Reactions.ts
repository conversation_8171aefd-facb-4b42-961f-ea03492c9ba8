import { CollectionSlug, Endpoint, Field, PayloadRequest } from 'payload';

import { CreatedAt, CreatedBy } from '@/fields/metadata';
import { getAuthenticatedRequestUser } from '@/helpers';

const field = {
    name: 'reactions',
    type: 'array',
    fields: [
        {
            name: 'reaction',
            type: 'text',
        },
        CreatedBy,
        CreatedAt,
    ],
    defaultValue: [],
} as Field;

const toggle: (collection: CollectionSlug) => Endpoint = (collection) => ({
    path: `/:id/reactions`,
    method: 'post',
    handler: async (request: PayloadRequest) => {
        const { payload } = request;
        const user = await getAuthenticatedRequestUser(request);

        const id = request.routeParams?.id as string;
        const { reaction } = (await request.json!()) as { reaction: string };

        const doc = await payload.findByID({
            collection,
            id,
            depth: 0,
        });

        if (!('reactions' in doc)) {
            return Response.json({
                success: false,
                message: 'Collection has no reactions field',
            });
        }

        let reactions = (doc.reactions || []) as any[];
        const exists = reactions.find((needle) => needle.reaction === reaction && needle.createdBy === user.id);

        if (exists) {
            reactions = reactions.filter((needle) => {
                return !(needle.reaction === reaction && needle.createdBy === user.id);
            });
        } else {
            reactions.push({
                reaction,
                createdBy: user.id,
                createdAt: new Date().toISOString(),
            });
        }

        await payload.update({
            collection,
            id,
            data: {
                // @ts-ignore
                reactions,
            },
        });

        return Response.json({
            success: true,
        });
    },
});

const Reactions = {
    field,
    endpoint: toggle,
};

export { Reactions };
