{"name": "dcide-app", "version": "0.3.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3001 --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome lint --write", "format": "biome format --write", "typecheck": "tsc --noEmit", "test:unit": "jest --runInBand --detectLeaks", "dev:email": "email dev --port 3004 --dir src/backend/emails/preview", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "patch": "patch-package --patch-dir=./patches", "postinstall": "npm run patch", "prepare": "husky"}, "dependencies": {"@ably/spaces": "^0.4.0", "@aws-sdk/client-s3": "^3.709.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^7.0.0", "@emotion/css": "^11.13.5", "@emotion/server": "^11.11.0", "@faker-js/faker": "^9.9.0", "@fireworks-js/react": "^2.10.5", "@google-cloud/vertexai": "^1.10.0", "@hookform/resolvers": "^3.3.4", "@mantine/carousel": "8.1.2", "@mantine/charts": "8.1.2", "@mantine/core": "8.1.2", "@mantine/dates": "8.1.2", "@mantine/dropzone": "8.1.2", "@mantine/form": "8.1.2", "@mantine/hooks": "8.1.2", "@mantine/modals": "8.1.2", "@mantine/next": "^6.0.22", "@mantine/notifications": "8.1.2", "@mantine/nprogress": "8.1.2", "@mantine/spotlight": "8.1.2", "@mantine/tiptap": "8.1.2", "@mapbox/search-js-react": "^1.0.0-beta.22", "@payloadcms/db-mongodb": "3.28.1", "@payloadcms/email-nodemailer": "3.28.1", "@payloadcms/live-preview-react": "3.28.1", "@payloadcms/next": "3.28.1", "@payloadcms/plugin-sentry": "3.28.1", "@payloadcms/richtext-lexical": "3.28.1", "@payloadcms/richtext-slate": "3.28.1", "@payloadcms/storage-s3": "3.28.1", "@payloadcms/ui": "3.28.1", "@react-email/components": "^0.0.31", "@sentry/nextjs": "^10.1.0", "@sentry/react": "^9.44.2", "@svgdotjs/svg.js": "^3.2.0", "@tiptap/extension-bold": "^2.3.0", "@tiptap/extension-bullet-list": "^2.3.0", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-document": "^2.3.0", "@tiptap/extension-italic": "^2.3.0", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-list-item": "^2.3.0", "@tiptap/extension-mention": "^2.9.1", "@tiptap/extension-ordered-list": "^2.3.0", "@tiptap/extension-paragraph": "^2.3.0", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-task-item": "^2.9.1", "@tiptap/extension-task-list": "^2.9.1", "@tiptap/extension-text": "^2.3.0", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/html": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/suggestion": "^2.9.1", "@use-gesture/react": "^10.3.1", "@usersnap/browser": "^1.0.2", "@vercel/analytics": "^1.2.2", "@vercel/functions": "^1.5.2", "@vercel/speed-insights": "^1.0.7", "@visx/responsive": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/text": "^3.12.0", "@visx/wordcloud": "^3.12.0", "ably": "^2.7.0", "cookie": "^0.6.0", "cross-env": "^7.0.3", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "derive-valtio": "^0.1.0", "embla-carousel-auto-height": "8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "8.6.0", "export-to-csv": "^1.2.2", "fflate": "^0.8.2", "file-type": "^20.4.1", "fireworks-js": "^2.10.7", "frimousse": "^0.3.0", "fuse.js": "^7.1.0", "graphql": "^16.8.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "json-2-csv": "^5.5.6", "jsonwebtoken": "^9.0.2", "mongodb": "^6.11.0", "next": "^15.4.5", "nodemailer-sendgrid": "^1.0.3", "openai": "^4.100.0", "patch-package": "^8.0.0", "payload": "3.28.1", "qrcode": "^1.5.4", "qs": "^6.13.1", "radash": "^12.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-dropzone-esm": "^15.0.1", "react-email": "^3.0.4", "react-error-boundary": "^4.0.13", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.51.3", "react-hotkeys-hook": "^4.5.0", "react-icons": "5.4.0", "react-international-phone": "^4.5.0", "react-markdown": "^9.0.0", "react-resizable": "^3.0.5", "reagraph": "^4.25.0", "recharts": "^2.15.3", "remark-gfm": "^4.0.0", "sharp": "^0.34.2", "simpleheat": "^0.4.0", "socket.io-client": "^4.7.5", "sonner": "^1.5.0", "stripe": "^15.2.0", "swr": "^2.2.5", "tiptap-markdown": "^0.8.10", "tiptap-unique-id": "^1.2.1", "unique-names-generator": "^4.7.1", "valtio": "^2.1.3", "zod": "^3.24.3", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/cookie": "^0.6.0", "@types/graceful-fs": "^4.1.9", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-lib-report": "^3.0.3", "@types/istanbul-reports": "^3.0.4", "@types/jest": "^29.5.12", "@types/json5": "^0.0.30", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.12.7", "@types/nodemailer-sendgrid": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/react-gtm-module": "^2.0.2", "@types/react-resizable": "^3.0.4", "@types/simpleheat": "^0.4.3", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^14.0.1", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "sass": "^1.75.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "turbo": "^1.13.0", "typescript": "^5.4.5"}, "resolutions": {"markdown-it": "13.0.2"}, "overrides": {"react-is": "19.1.0", "prettier": "3.5.2"}}