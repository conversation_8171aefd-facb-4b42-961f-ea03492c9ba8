# DCIDE Monorepo

## Set Up

-   Install node deps

```
npm i
```

-   Link vercel and pull env file

```
dcide-app git:(main) vercel link
Vercel CLI 32.1.0
? Set up "~/.../dcide-app"? [Y/n] y
? Which scope should contain your project? dcide
? Link to existing project? [y/N] y
? What's the name of your existing project? dcide-app-frontend
...
vercel pull
mv .vercel/.env.local apps/frontend
```

-   Create backend `.env` file. Reference existing project or DigitalOcean

-   Patch packages
    `npx patch-package --patch-dir=./apps/backend/patches`
